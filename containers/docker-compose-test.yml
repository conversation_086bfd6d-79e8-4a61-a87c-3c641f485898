version: "3.5"
services:
  dbpg-atom-1:
    image: postgres:16
    container_name: dbpg-atom-1
    restart: always
    environment:
      POSTGRES_USER: put_your_data
      POSTGRES_PASSWORD: put_your_data
      POSTGRES_DB: put_your_data
      PGDATA: /var/lib/postgresql/data/pgdata
    command: ["-c", "ssl=on", "-c", "ssl_cert_file=/var/lib/postgresql/server.crt", "-c", "ssl_key_file=/var/lib/postgresql/server.key"]
    ports:
      - '5438:5432'
    volumes:
      - ./dbpg:/var/lib/postgresql/data
      - "./path_to_certificate:/var/lib/postgresql/server.crt"
      - "./path_to_private_key:/var/lib/postgresql/server.key"
