# Placeholder Dockerfile for qa-frontend
FROM python:3.9-slim

WORKDIR /app

# Install basic dependencies
RUN pip install --no-cache-dir fastapi uvicorn pika psycopg2-binary minio

# Create a simple health check endpoint
RUN echo 'from fastapi import FastAPI; app = FastAPI(); @app.get("/health"); def health(): return {"status": "healthy"}' > main.py

EXPOSE 8080

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
