
---
apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: HelmRepository
metadata:
  name: cluster-autoscaler
  namespace: kube-system
spec:
  interval: 24h
  url: https://kubernetes.github.io/autoscaler
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: cluster-autoscaler
  namespace: kube-system
spec:
  interval: 30m
  chart:
    spec:
      chart: cluster-autoscaler
      version: 9.46.2
      sourceRef:
        kind: HelmRepository
        name: cluster-autoscaler
        namespace: kube-system
      interval: 12h
  values:
    awsRegion: us-east-2
    autoDiscovery:
      clusterName: atom-advantage-us-east-2
    resources:
     limits:
       cpu: 200m
       memory: 512Mi
     requests:
       cpu: 100m
       memory: 128Mi
