---
apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: HelmRepository
metadata:
  name: prometheus
  namespace: monitoring
spec:
  interval: 24h
  url: https://prometheus-community.github.io/helm-charts
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: prometheus
  namespace: monitoring
spec:
  interval: 30m
  install:
    remediation:
      retries: -1
  upgrade:
    remediation:
      retries: -1
      strategy: rollback
  chart:
    spec:
      chart: prometheus
      version: 27.7.0
      sourceRef:
        kind: HelmRepository
        name: prometheus
        namespace: monitoring
      interval: 12h
  values:
    server:
      persistentVolume:
        storageClass: gp2
      tolerations:
        - key: "monitoring"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - monitoring
    configmapReload:
      prometheus:
        resources:
          limits:
            cpu: 100m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi
    serverFiles:
      alerts:
        groups:
        - name: PodStatus
          rules:
          - alert: UnhealthyPod
            expr: kube_pod_status_phase{phase!~"Running|Succeeded"} > 0
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Pod {{ $labels.namespace }}/{{ $labels.pod }} is in a non-healthy state ({{ $labels.phase }})"
              description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} has been in a {{ $labels.phase }} state for more than 5 minutes."

          - alert: HighPodMemoryUsage
            expr: (sum(container_memory_working_set_bytes{container!="POD",namespace!="",pod!~"aws-node-.*|kube-proxy-.*|cert-manager-.*|csi-blob.*|helm-loki-stack.*"}) by (container, namespace, pod)) / (sum(container_spec_memory_limit_bytes{container!="POD",namespace!="",pod!~"aws-node-.*|kube-proxy-.*|cert-manager-.*|csi-blob.*|helm-loki-stack.*"}) by (container,namespace, pod)) > 0.9
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High memory usage in pod {{ $labels.namespace }}/{{ $labels.pod }}"
              description: "The Pod {{ $labels.namespace }}/{{ $labels.pod }} is using over 80% of its memory limit."

          - alert: HighPodCPUUsage
            expr: |
              (
                sum(rate(container_cpu_usage_seconds_total{container!="POD",namespace!="",pod!~"aws-node-.*|kube-proxy-.*"}[5m])) by (container, namespace, pod)
              ) / (
                sum(container_spec_cpu_quota{container!="POD",namespace!="",pod!~"aws-node-.*|kube-proxy-.*"} / container_spec_cpu_period{container!="POD",namespace!="",pod!~"aws-node-.*|kube-proxy-.*"}) by (container, namespace, pod)
              ) > 0.8
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High CPU usage in pod {{ $labels.namespace }}/{{ $labels.pod }}"
              description: "The Pod {{ $labels.namespace }}/{{ $labels.pod }} is using over 80% of its CPU limit."


        - name: NodeAlerts
          rules:
          - alert: HighNodeCPUUsage
            expr: 100 - ( avg by (node) ( rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100 ) > 90
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High CPU usage on node {{ $labels.node }}"
              description: "Node {{ $labels.node }} has CPU usage above 90%."
          - alert: HighNodeMemoryUsage
            expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High memory usage on node {{ $labels.node }}"
              description: "Node {{ $labels.node }} has memory usage above 90%."

          - alert: NodeNotReady
            expr: kube_node_status_condition{condition="Ready",status="false"} == 1
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Node {{ $labels.node }} is not ready"
              description: "The node {{ $labels.node }} has been in a NotReady state for more than 5 minutes."


        - name: StorageAlerts
          rules:
          - alert: HighPVCUsage
            expr: (kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes) > 0.8
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High PVC usage for {{ $labels.persistentvolumeclaim }} in namespace {{ $labels.namespace }}"
              description: "PVC {{ $labels.persistentvolumeclaim }} in namespace {{ $labels.namespace }} has storage usage above 80%."

          - alert: PVCInPendingState
            expr: kube_persistentvolumeclaim_status_phase{phase="Pending"} > 0
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "PVC {{ $labels.persistentvolumeclaim }} in namespace {{ $labels.namespace }} is in Pending state"
              description: "PVC {{ $labels.persistentvolumeclaim }} in namespace {{ $labels.namespace }} has been in Pending state for over 5 minutes."
        - name: IngressControllerAlerts
          rules:
          - alert: IngressController4xxErrorRate
            expr: |
              sum by (namespace, service, group, severity, ingress, host) (
                irate(nginx_ingress_controller_requests{status=~"^4.."}[5m])
              ) / sum by (namespace, service, group, severity, ingress, host) (
                irate(nginx_ingress_controller_requests[5m])
              ) * 100 > 20
            for: 5m
            annotations:
              summary: "High Ingress Controller 4xx Error Rate"
              description: "The 4xx error rate for Ingress Controller requests is above 20%."
            labels:
              group: "IngressControllerAlerts"
          - alert: IngressController5xxErrorRate
            expr: |
              sum by (namespace, service, group, severity, ingress, host) (
                irate(nginx_ingress_controller_requests{status=~"^5.."}[5m])
              ) / sum by (namespace, service, group, severity, ingress, host) (
                irate(nginx_ingress_controller_requests[5m])
              ) * 100 > 5
            for: 5m
            annotations:
              summary: "High Ingress Controller 5xx Error Rate"
              description: "The 5xx error rate for Ingress Controller requests is above 5%."
            labels:
              group: "IngressControllerAlerts"
          - alert: IngressControllerLatencyP99
            expr: |
              histogram_quantile(0.99, sum by (namespace, service, group, severity, ingress, host, le) (
                irate(nginx_ingress_controller_request_duration_seconds_bucket[5m])
              )) > 1
            for: 5m
            annotations:
              summary: "High Ingress Controller Latency (p99)"
              description: "The 99th percentile latency for Ingress Controller requests is above 1 second."
            labels:
              group: "IngressControllerAlerts"
          - alert: IngressControllerLatencyP90
            expr: |
              histogram_quantile(0.90, sum by (namespace, service, group, severity, ingress, host, le) (
                irate(nginx_ingress_controller_request_duration_seconds_bucket[5m])
              )) > 1
            for: 5m
            annotations:
              summary: "High Ingress Controller Latency (p90)"
              description: "The 90th percentile latency for Ingress Controller requests is above 1 second."
            labels:
              group: "IngressControllerAlerts"
          - alert: IngressControllerLatencyP50
            expr: |
              histogram_quantile(0.50, sum by (namespace, service, group, severity, ingress, host, le) (
                irate(nginx_ingress_controller_request_duration_seconds_bucket[5m])
              )) > 1
            for: 5m
            annotations:
              summary: "High Ingress Controller Latency (p50)"
              description: "The 50th percentile latency for Ingress Controller requests is above 1 second."
            labels:
              group: "IngressControllerAlerts"
    prometheus-pushgateway:
      enabled: false
    prometheus-node-exporter:
      enabled: true
      resources:
        limits:
          cpu: 200m
          memory: 100Mi
        requests:
          cpu: 50m
          memory: 30Mi
      tolerations:
        - key: "qa-tool"
          operator: Equal
          value: "true"
          effect: "NoExecute"
        - key: "ml-workloads"
          operator: Equal
          value: "true"
          effect: "NoExecute"
        - key: "monitoring"
          operator: Equal
          value: "true"
          effect: "NoExecute"
    kube-state-metrics:
      resources:
        limits:
          cpu: 200m
          memory: 256Mi
        requests:
          cpu: 50m
          memory: 30Mi
      tolerations:
        - key: "qa-tool"
          operator: Equal
          value: "true"
          effect: "NoExecute"
        - key: "ml-workloads"
          operator: Equal
          value: "true"
          effect: "NoExecute"
        - key: "monitoring"
          operator: Equal
          value: "true"
          effect: "NoExecute"
    alertmanager:
      enabled: true
      tolerations:
        - key: "qa-tool"
          operator: Equal
          value: "true"
          effect: "NoExecute"
        - key: "ml-workloads"
          operator: Equal
          value: "true"
          effect: "NoExecute"
        - key: "monitoring"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      persistence:
        storageClass: gp2
      resources:
        limits:
          cpu: 200m
          memory: 256Mi
        requests:
          cpu: 50m
          memory: 30Mi
