---
apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: HelmRepository
metadata:
  name: helm-grafana
  namespace: monitoring
spec:
  interval: 24h
  url: https://grafana.github.io/helm-charts

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: loki
  namespace: monitoring
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/loki-role

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: loki
  namespace: monitoring
spec:
  ingressClassName: nginx-internal
  rules:
    - http:
        paths:
          - path: /loki
            pathType: Prefix
            backend:
              service:
                name: loki
                port:
                  number: 3100
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: loki-gateway
  namespace: monitoring
spec:
  ingressClassName: nginx-internal
  rules:
    - http:
        paths:
          - path: /loki/api/v1/push
            pathType: Prefix
            backend:
              service:
                name: loki-gateway
                port:
                  number: 80


---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: helm-loki
  namespace: monitoring
spec:
  releaseName: loki
  targetNamespace: monitoring
  interval: 10m
  chart:
    spec:
      version: "6.7.3"
      chart: loki
      sourceRef:
        kind: HelmRepository
        name: helm-grafana
      interval: 60m
  values:
    deploymentMode: SingleBinary
    serviceAccount:
      create: false
      name: loki
    loki:
      auth_enabled: false
      commonConfig:
        replication_factor: 1
      compactor:
        working_directory: /var/loki/compactor/retention
        compaction_interval: 10m 
        retention_enabled: true
        retention_delete_delay: 2h
        retention_delete_worker_count: 150
        delete_request_store: s3
      schemaConfig:
        configs:
        - from: "2024-01-01"
          store: tsdb
          index:
            prefix: loki_index_
            period: 24h
          object_store: s3
          schema: v13
      storage:
        type: 's3'
        bucketNames:
          chunks: atom-advantage-loki-dev
          ruler: atom-advantage-loki-dev
          admin: atom-advantage-loki-dev
        s3:
          region: us-east-2
          s3ForcePathStyle: false
          insecure: false
    singleBinary:
      replicas: 1
      persistence:
        enabled: true
        storageClass: gp2
        size: 30Gi
      resources:
        limits:
          cpu: 500m
          memory: 750Mi
        requests:
          cpu: 100m
          memory: 128Mi
      tolerations:
        - key: "monitoring"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"
    chunksCache:
      allocatedMemory: 650
      resources:
        limits:
          cpu: 600m
          memory: 700Mi
        requests:
          cpu: 100m
          memory: 128Mi
      tolerations:
        - key: "monitoring"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"
    resultsCache:
      allocatedMemory: 512
      resources:
        limits:
          cpu: 600m
          memory: 600Mi
        requests:
          cpu: 100m
          memory: 128Mi
      tolerations:
        - key: "monitoring"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"
    gateway:
      resources:
        limits:
          cpu: 250m
          memory: 120Mi
        requests:
          cpu: 10m
          memory: 10Mi
      enabled: true
      tolerations:
        - key: "monitoring"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"
    lokiCanary:
      resources:
        limits:
          cpu: 250m
          memory: 250Mi
        requests:
          cpu: 100m
          memory: 128Mi
      tolerations:
        - key: "monitoring"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"
    read:
      replicas: 0
    backend:
      replicas: 0
    write:
      replicas: 0