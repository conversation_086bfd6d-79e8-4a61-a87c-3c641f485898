---
apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: HelmRepository
metadata:
  name: ingress-nginx
  namespace: ingress-controller
spec:
  interval: 24h
  url: https://kubernetes.github.io/ingress-nginx
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: ingress-nginx
  namespace: ingress-controller
spec:
  interval: 30m
  chart:
    spec:
      chart: ingress-nginx
      version: 4.12.1
      sourceRef:
        kind: HelmRepository
        name: ingress-nginx
        namespace: ingress-controller
      interval: 12h
  install:
    remediation:
      retries: -1
  values:
    controller:
      metrics:
        enabled: true
      resources:
        limits:
          cpu: 500m
          memory: 500Mi
        requests:
          cpu: 100m
          memory: 100Mi
      allowSnippetAnnotations: true
      config:
        ssl-redirect: "false"
      containerPort:
        http: 80
        https: 443
        tohttps: 2443
      service:
        targetPorts:
          http: http
          https: tohttps
        externalTrafficPolicy: Local
        annotations:
          service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: '30'
          service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: 'true'
          service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "443"
          service.beta.kubernetes.io/aws-load-balancer-type: external
          service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
          service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
          service.beta.kubernetes.io/aws-load-balancer-name: ingress-nginx
          service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
        type: LoadBalancer
        publishService:
          enabled: true