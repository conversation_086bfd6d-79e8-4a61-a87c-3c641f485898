---
apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: HelmRepository
metadata:
  name: aws-alb-controller
  namespace: ingress-controller
spec:
  interval: 24h
  url: https://aws.github.io/eks-charts
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: aws-alb-controller
  namespace: ingress-controller
spec:
  interval: 30m
  chart:
    spec:
      chart: aws-load-balancer-controller
      version: 1.4.7
      sourceRef:
        kind: HelmRepository
        name: aws-alb-controller
        namespace: ingress-controller
      interval: 12h
  install:
    remediation:
      retries: -1
  values:
    clusterName: atom-advantage-us-east-2
    serviceAccount:
      create: true
      name: aws-load-balancer-controller 
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/atom-advantage-us-east-2-alb-ingress-controller
