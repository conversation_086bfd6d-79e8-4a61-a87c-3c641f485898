---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: helm-promtail
  namespace: monitoring
spec:
  releaseName: promtail
  targetNamespace: monitoring
  interval: 10m
  chart:
    spec:
      version: "6.16.4"
      chart: promtail
      sourceRef:
        kind: HelmRepository
        name: helm-grafana
      interval: 60m
  values:
    resources:
      limits:
        cpu: 200m
        memory: 350Mi
      requests:
        cpu: 100m
        memory: 128Mi
    tolerations:
      - key: node-role.kubernetes.io/master
        operator: Exists
        effect: NoSchedule
      - key: node-role.kubernetes.io/control-plane
        operator: Exists
        effect: NoSchedule
      - key: "monitoring"
        operator: Equal
        value: "true"
        effect: "NoExecute"
      - key: "qa-tool"
        operator: Equal
        value: "true"
        effect: "NoExecute"
      - key: "stage"
        operator: Equal
        value: "true"
        effect: "NoExecute"
      - key: "ml-workloads"
        operator: Equal
        value: "true"
        effect: "NoExecute"
      - key: "controller"
        operator: Equal
        value: "true"
        effect: "NoExecute"
      - key: "training"
        operator: Equal
        value: "true"
        effect: "NoExecute"
    config:
    # publish data to loki
      clients:
        - url: http://loki-gateway/loki/api/v1/push
          tenant_id: 1