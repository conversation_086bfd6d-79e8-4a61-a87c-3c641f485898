---
apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: HelmRepository
metadata:
  name: secrets-store-csi-driver
  namespace: kube-system
spec:
  interval: 24h
  url: https://kubernetes-sigs.github.io/secrets-store-csi-driver/charts
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: secrets-store-csi-driver
  namespace: kube-system
spec:
  interval: 60m
  chart:
    spec:
      chart: secrets-store-csi-driver
      version: "*"
      sourceRef:
        kind: HelmRepository
        name: secrets-store-csi-driver
      interval: 12h
  install:
    crds: Create
  upgrade:
    crds: CreateReplace
  values:
    syncSecret:
      enabled: true
    enableSecretRotation: true
    rotationPollInterval: "60s"
