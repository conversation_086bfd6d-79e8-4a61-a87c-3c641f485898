---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: helm-ingress-nginx-internal
  namespace: ingress-controller
spec:
  releaseName: ingress-nginx-internal
  interval: 12h
  chart:
    spec:
      chart: ingress-nginx
      version: 4.11.1
      sourceRef:
        kind: HelmRepository
        name: ingress-nginx
        namespace: ingress-controller
  values:
    controller:
      admissionWebhooks:
        patch:
          tolerations:
            - key: "controller"
              operator: Equal
              value: "true"
              effect: "NoExecute"
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: assignment
                        operator: In
                        values:
                          - controller
      electionID: ingress-nginx-internal-leader
      ingressClass: nginx-internal  # default: nginx
      ingressClassResource:
        name: "nginx-internal"
        enabled: true
        default: false
        controllerValue: "k8s.io/internal-ingress-nginx"  # default: k8s.io/ingress-nginx
      resources:
        limits:
          cpu: 500m
          memory: 500Mi
        requests:
          cpu: 100m
          memory: 100Mi
      tolerations:
        - key: "controller"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - controller
      metrics:
        enabled: true
      podAnnotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "10254"
      service:
        externalTrafficPolicy: Local
        annotations:
          service.beta.kubernetes.io/aws-load-balancer-type: external
          service.beta.kubernetes.io/aws-load-balancer-scheme: internal
          service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
          service.beta.kubernetes.io/aws-load-balancer-name: k8s-ingress-nginx-internal
          service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
          service.beta.kubernetes.io/aws-load-balancer-attributes: load_balancing.cross_zone.enabled=true
        type: LoadBalancer