apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: HelmRepository
metadata:
  name: bitnami-oci-minio
  namespace: minio
spec:
  type: oci
  url:  oci://registry-1.docker.io/bitnamicharts
  interval: 1h
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: minio
  namespace: minio
spec:
  interval: 30m
  chart:
    spec:
      chart: minio
      version: "16.0.8"
      sourceRef:
        kind: HelmRepository
        name: bitnami-oci-minio
        namespace: minio
      interval: 5m
  install:
    remediation:
      retries: -1
  values:
    persistence:
      enabled: true
      storageClass: gp2
      size: 100Gi
    defaultBuckets: "downloader-intake"
    region: "us-east-2"
    tls:
      enabled: false
