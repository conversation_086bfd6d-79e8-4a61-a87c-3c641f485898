apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: HelmRepository
metadata:
  name: bitnami-oci
  namespace: rabbitmq
spec:
  type: oci
  url:  oci://registry-1.docker.io/bitnamicharts
  interval: 1h
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: rabbitmq
  namespace: rabbitmq
spec:
  interval: 30m
  chart:
    spec:
      chart: rabbitmq
      version: "16.0.2"
      sourceRef:
        kind: HelmRepository
        name: bitnami-oci
        namespace: rabbitmq
      interval: 5m
  install:
    remediation:
      retries: -1
  values:
    persistence:
      enabled: true
      storageClass: gp2
      size: 100Gi
