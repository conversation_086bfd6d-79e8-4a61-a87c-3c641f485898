-- Migration: Add Tenant Upload Configuration Table
-- This script adds the tenant_upload_configurations table for storing tenant-specific upload configurations

-- Ensure subtenants table has the required unique constraint
-- This is needed for the foreign key constraint in tenant_upload_configurations
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'unique_subtenant_tenant' 
        AND conrelid = 'subtenants'::regclass
    ) THEN
        ALTER TABLE subtenants 
        ADD CONSTRAINT unique_subtenant_tenant 
            UNIQUE (tenant_id, subtenant_id);
    END IF;
END $$;

-- Create tenant_upload_configurations table
CREATE TABLE IF NOT EXISTS tenant_upload_configurations (
    id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    tenant_id VARCHAR(128) NOT NULL,
    subtenant_id VARCHAR(128),
    configuration_name VARCHAR(256) NOT NULL,
    channel_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_default BOOLEAN NOT NULL DEFAULT false,
    channel_config JSONB NOT NULL,
    description TEXT,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(128),
    updated_by VARCHAR(128),
    
    -- Foreign key constraints
    CONSTRAINT fk_tenant_upload_config_tenant 
        FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    CONSTRAINT fk_tenant_upload_config_subtenant 
        FOREIGN KEY (tenant_id, subtenant_id) REFERENCES subtenants(tenant_id, subtenant_id) ON DELETE CASCADE,
    
    -- Unique constraint to ensure only one default per tenant/channel combination
    CONSTRAINT unique_tenant_channel_default 
        UNIQUE (tenant_id, subtenant_id, channel_type, is_default)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tenant_upload_config_tenant 
    ON tenant_upload_configurations(tenant_id, subtenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_upload_config_active 
    ON tenant_upload_configurations(is_active);
CREATE INDEX IF NOT EXISTS idx_tenant_upload_config_channel 
    ON tenant_upload_configurations(channel_type);
CREATE INDEX IF NOT EXISTS idx_tenant_upload_config_default 
    ON tenant_upload_configurations(tenant_id, subtenant_id, is_default);

-- Create trigger to update updated_date automatically
CREATE OR REPLACE FUNCTION update_tenant_upload_config_updated_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_tenant_upload_config_updated_date
    BEFORE UPDATE ON tenant_upload_configurations
    FOR EACH ROW
    EXECUTE FUNCTION update_tenant_upload_config_updated_date();

-- Create trigger to ensure only one default per tenant/channel combination
CREATE OR REPLACE FUNCTION ensure_single_default_tenant_upload_config()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is being set as default, unset other defaults for the same tenant/channel
    IF NEW.is_default = true THEN
        UPDATE tenant_upload_configurations 
        SET is_default = false 
        WHERE tenant_id = NEW.tenant_id 
          AND (subtenant_id = NEW.subtenant_id OR (subtenant_id IS NULL AND NEW.subtenant_id IS NULL))
          AND channel_type = NEW.channel_type 
          AND id != NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ensure_single_default_tenant_upload_config
    BEFORE INSERT OR UPDATE ON tenant_upload_configurations
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_default_tenant_upload_config();

-- Insert some example configurations for testing
-- Note: These are examples and should be replaced with actual configurations

-- Create example tenant if it doesn't exist
INSERT INTO tenants (tenant_id, tenant_name, description, active)
SELECT 'example_tenant', 'Example Tenant', 'Example tenant for testing upload configurations', TRUE
WHERE NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = 'example_tenant');

-- Example SFTP configuration
INSERT INTO tenant_upload_configurations (
    tenant_id, 
    configuration_name, 
    channel_type, 
    channel_config, 
    description, 
    is_default, 
    created_by
) VALUES (
    'example_tenant',
    'Default SFTP Configuration',
    'sftp',
    '{
        "host": "sftp.example.com",
        "port": 22,
        "username": "upload_user",
        "password": "secure_password",
        "upload_path": "Documents",
        "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}",
        "file_naming_convention": "{claim_number}_{sender_name}_{document_type}_{patient_name}",
        "sort_by_date": false,
        "create_metadata_file": true,
        "metadata_filename": "index.json"
    }'::jsonb,
    'Default SFTP upload configuration for example tenant',
    true,
    'system'
) ON CONFLICT DO NOTHING;

-- Example Service Bus Topic configuration
INSERT INTO tenant_upload_configurations (
    tenant_id, 
    configuration_name, 
    channel_type, 
    channel_config, 
    description, 
    is_default, 
    created_by
) VALUES (
    'example_tenant',
    'Service Bus Topic Configuration',
    'servicebus_topic',
    '{
        "connection_string": "Endpoint=sb://example.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your_key_here",
        "topic_name": "document-uploads",
        "blob_storage_connection_string": "DefaultEndpointsProtocol=https;AccountName=example;AccountKey=your_key_here;EndpointSuffix=core.windows.net",
        "blob_container_name": "documents",
        "message_format": "json",
        "include_blob_urls": true,
        "sas_token_expiry_days": 14,
        "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}"
    }'::jsonb,
    'Service Bus Topic configuration for example tenant',
    false,
    'system'
) ON CONFLICT DO NOTHING;

-- Example API S3 configuration
INSERT INTO tenant_upload_configurations (
    tenant_id, 
    configuration_name, 
    channel_type, 
    channel_config, 
    description, 
    is_default, 
    created_by
) VALUES (
    'example_tenant',
    'API S3 Configuration',
    'api_s3',
    '{
        "api_endpoint": "https://api.example.com/upload",
        "api_key": "your_api_key_here",
        "s3_bucket_name": "example-documents",
        "s3_access_key": "your_s3_access_key",
        "s3_secret_key": "your_s3_secret_key",
        "s3_region": "us-east-1",
        "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}",
        "upload_method": "multipart",
        "include_metadata": true
    }'::jsonb,
    'API S3 configuration for example tenant',
    false,
    'system'
) ON CONFLICT DO NOTHING;

-- Example API REST configuration
INSERT INTO tenant_upload_configurations (
    tenant_id, 
    configuration_name, 
    channel_type, 
    channel_config, 
    description, 
    is_default, 
    created_by
) VALUES (
    'example_tenant',
    'API REST Configuration',
    'api_rest',
    '{
        "api_endpoint": "https://api.example.com/upload",
        "api_key": "your_api_key_here",
        "auth_method": "bearer",
        "content_type": "multipart/form-data",
        "upload_method": "multipart",
        "include_metadata": true,
        "retry_attempts": 3,
        "timeout_seconds": 30,
        "custom_headers": {}
    }'::jsonb,
    'API REST configuration for example tenant',
    false,
    'system'
) ON CONFLICT DO NOTHING;

-- Add comments to the table
COMMENT ON TABLE tenant_upload_configurations IS 'Stores tenant-specific upload configurations for different channel types';
COMMENT ON COLUMN tenant_upload_configurations.id IS 'Unique identifier for the configuration';
COMMENT ON COLUMN tenant_upload_configurations.tenant_id IS 'Tenant ID this configuration belongs to';
COMMENT ON COLUMN tenant_upload_configurations.subtenant_id IS 'Subtenant ID (optional)';
COMMENT ON COLUMN tenant_upload_configurations.configuration_name IS 'Human-readable name for this configuration';
COMMENT ON COLUMN tenant_upload_configurations.channel_type IS 'Type of upload channel (sftp, servicebus_topic, servicebus_queue, api_s3, api_rest)';
COMMENT ON COLUMN tenant_upload_configurations.is_active IS 'Whether this configuration is active';
COMMENT ON COLUMN tenant_upload_configurations.is_default IS 'Whether this is the default configuration for this tenant/channel combination';
COMMENT ON COLUMN tenant_upload_configurations.channel_config IS 'JSON configuration specific to the channel type';
COMMENT ON COLUMN tenant_upload_configurations.description IS 'Description of this configuration';
COMMENT ON COLUMN tenant_upload_configurations.created_date IS 'When this configuration was created';
COMMENT ON COLUMN tenant_upload_configurations.updated_date IS 'When this configuration was last updated';
COMMENT ON COLUMN tenant_upload_configurations.created_by IS 'User who created this configuration';
COMMENT ON COLUMN tenant_upload_configurations.updated_by IS 'User who last updated this configuration'; 