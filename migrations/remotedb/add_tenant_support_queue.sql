-- =====================================================
-- Tenant Support Migration Script for Queue Database
-- =====================================================
-- This script adds tenant support to the Queue database
-- Tables: raw_incoming_packages
-- =====================================================

-- Add tenant fields to raw_incoming_packages
ALTER TABLE raw_incoming_packages 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128),
ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);

-- Create indexes for raw_incoming_packages
CREATE INDEX IF NOT EXISTS idx_raw_incoming_tenant ON raw_incoming_packages(tenant_id, subtenant_id);
CREATE INDEX IF NOT EXISTS idx_raw_incoming_status_tenant ON raw_incoming_packages(status, tenant_id, subtenant_id);

-- =====================================================
-- UPDATE EXISTING DATA WITH DEFAULT TENANT
-- =====================================================

-- Update existing data in Queue database
UPDATE raw_incoming_packages SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;

-- =====================================================
-- MIGRATION COMPLETED FOR QUEUE DATABASE
-- =====================================================
-- All tenant support fields and indexes have been added to Queue database
-- Existing data has been updated to use the default tenant
-- Note: Tenant and subtenant tables are managed in the RR database 