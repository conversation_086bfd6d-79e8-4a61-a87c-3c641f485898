-- =====================================================
-- Tenant Support Migration Script for RR Database
-- =====================================================
-- This script adds tenant support to the RR database
-- Tables: incoming_packages, documents, document_chunk, 
--         splitted_documents, users, user_activity_logs
-- =====================================================

-- Create tenants table
CREATE TABLE IF NOT EXISTS tenants (
    tenant_id VARCHAR(128) PRIMARY KEY,
    tenant_name VARCHAR(256) NOT NULL,
    description VARCHAR(512),
    contact_email VARCHAR(256),
    created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    active BOOLEAN DEFAULT TRUE,
    tenant_config JSON
);

-- Create subtenants table
CREATE TABLE IF NOT EXISTS subtenants (
    subtenant_id VARCHAR(128) PRIMARY KEY,
    tenant_id VARCHAR(128) NOT NULL REFERENCES tenants(tenant_id),
    subtenant_name VARCHAR(256) NOT NULL,
    description VARCHAR(512),
    contact_email VARCHAR(256),
    created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    active BOOLEAN DEFAULT TRUE,
    subtenant_config JSON
);

-- Create index on subtenants
CREATE INDEX IF NOT EXISTS idx_subtenant_tenant ON subtenants(tenant_id, subtenant_id);

-- Add tenant fields to incoming_packages
ALTER TABLE incoming_packages 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128) REFERENCES tenants(tenant_id),
ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);

-- Add tenant fields to documents
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128) REFERENCES tenants(tenant_id),
ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);

-- Add tenant fields to document_chunk
ALTER TABLE document_chunk 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128) REFERENCES tenants(tenant_id),
ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);

-- Add tenant fields to splitted_documents
ALTER TABLE splitted_documents 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128) REFERENCES tenants(tenant_id),
ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);

-- Add tenant fields to users
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS accessible_tenants JSON,
ADD COLUMN IF NOT EXISTS default_tenant_id VARCHAR(128);

-- Add tenant fields to user_activity_logs
ALTER TABLE user_activity_logs 
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128),
ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);

-- Create indexes for incoming_packages
CREATE INDEX IF NOT EXISTS idx_incoming_package_tenant ON incoming_packages(tenant_id, subtenant_id);
CREATE INDEX IF NOT EXISTS idx_incoming_package_status_tenant ON incoming_packages(status, tenant_id, subtenant_id);

-- Create indexes for documents
CREATE INDEX IF NOT EXISTS idx_document_tenant ON documents(tenant_id, subtenant_id);
CREATE INDEX IF NOT EXISTS idx_document_status_tenant ON documents(status, tenant_id, subtenant_id);

-- Create indexes for document_chunk
CREATE INDEX IF NOT EXISTS idx_document_chunk_tenant ON document_chunk(tenant_id, subtenant_id);
CREATE INDEX IF NOT EXISTS idx_document_chunk_status_tenant ON document_chunk(status, tenant_id, subtenant_id);

-- Create indexes for splitted_documents
CREATE INDEX IF NOT EXISTS idx_splitted_document_tenant ON splitted_documents(tenant_id, subtenant_id);
CREATE INDEX IF NOT EXISTS idx_splitted_document_status_tenant ON splitted_documents(status, tenant_id, subtenant_id);
CREATE INDEX IF NOT EXISTS idx_splitted_document_type_tenant ON splitted_documents(document_type, tenant_id, subtenant_id);

-- Create indexes for user_activity_logs
CREATE INDEX IF NOT EXISTS idx_activity_log_tenant ON user_activity_logs(tenant_id, subtenant_id);

-- =====================================================
-- CREATE DEFAULT TENANT AND SUBTENANT
-- =====================================================

-- Insert default tenant (only if it doesn't exist)
INSERT INTO tenants (tenant_id, tenant_name, description, active)
SELECT 'default', 'Default Tenant', 'Default tenant for existing data migration', TRUE
WHERE NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = 'default');

-- Insert default subtenant (only if it doesn't exist)
INSERT INTO subtenants (subtenant_id, tenant_id, subtenant_name, description, active)
SELECT 'default', 'default', 'Default Subtenant', 'Default subtenant for existing data migration', TRUE
WHERE NOT EXISTS (SELECT 1 FROM subtenants WHERE subtenant_id = 'default');

-- =====================================================
-- UPDATE EXISTING DATA WITH DEFAULT TENANT
-- =====================================================

-- Update existing data in RR database tables
UPDATE incoming_packages SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;
UPDATE documents SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;
UPDATE document_chunk SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;
UPDATE splitted_documents SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;
UPDATE user_activity_logs SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;

-- =====================================================
-- MIGRATION COMPLETED FOR RR DATABASE
-- =====================================================
-- All tenant support fields and indexes have been added to RR database
-- Default tenant and subtenant have been created
-- Existing data has been updated to use the default tenant 