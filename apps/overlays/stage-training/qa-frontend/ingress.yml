---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: qa-frontend
  namespace: training
spec:
  ingressClassName: nginx
  rules:
    - host: training-qa-frontend-stage.atomadvantage.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: qa-frontend
                port:
                  number: 80
