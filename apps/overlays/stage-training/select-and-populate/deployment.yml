apiVersion: apps/v1
kind: Deployment
metadata:
  name: select-and-populate
  namespace: training
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 0
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: select-and-populate
  template:
    metadata:
      labels:
        app: select-and-populate
    spec:
      serviceAccountName: select-and-populate-training
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: assignment
                operator: In
                values:
                - "ml-workloads"
      tolerations:
      - key: "ml-workloads"
        operator: "Equal"
        value: "true"
        effect: "NoExecute"
      dnsPolicy: ClusterFirst
      containers:
      - name: select-and-populate
        image: ************.dkr.ecr.us-east-2.amazonaws.com/select-and-populate:67ec7915
        imagePullPolicy: Always
        resources:
          limits:
            memory: 4Gi
            cpu: 2
            nvidia.com/gpu: "1"
          requests:
            memory: 2Gi
            cpu: 1
        env:
        - name: TENANT_NAME
          value: "training"
        - name: REMOTE_RABBITMQ_HOST
          value: "rabbitmq.rabbitmq.svc.cluster.local"
        - name: REMOTE_RABBITMQ_PORT
          value: "5672"
        - name: REMOTE_RABBITMQ_USERNAME
          value: "user"
        - name: REMOTE_RABBITMQ_PASSWORD
          value: "1hENwBdjtrGj29Tg"
        - name: REMOTE_RABBITMQ_VHOST
          value: "/"
        - name: REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME
          value: "training_from_backend_qa"
        - name: REMOTE_RABBITMQ_FROM_BACKEND_SCREENSHOT
          value: "training_from_backend_screenshot"
        - name: REMOTE_SSL_CAFILE_PATH
          value: "/certs/ca.pem"
        - name: REMOTE_SSL_CERTFILE_PATH
          value: "/certs/cert.pem"
        - name: REMOTE_SSL_KEYFILE_PATH
          value: "/certs/key.pem"
        volumeMounts:
        - name: certs
          mountPath: /certs
          readOnly: true
      volumes:
      - name: certs
        secret:
          secretName: select-and-populate
          optional: true
