import os
import json
import logging
from datetime import datetime, timedelta, timezone
from azure.servicebus import ServiceBusClient, ServiceBusMessage
from azure.storage.blob import (
    BlobServiceClient,
    generate_blob_sas,
    BlobSasPermissions,
)

# --- Configuration ---
# Set up basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load configuration from environment variables with fallbacks for local dev
# In a real environment, these should be set securely.
SERVICE_BUS_CONNECTION_STR = os.environ.get(
    "SERVICE_BUS_CONNECTION_STR",
    "Endpoint=sb://atom-advantange-sbus-training.servicebus.windows.net/;SharedAccessKeyName=listen;SharedAccessKey=uuIrNa8G2RYEKytlbEbeIB73Q9hYVpJsT+ASbJAwe+0=;EntityPath=atom-downloader-training"
)
QUEUE_NAME = os.environ.get("QUEUE_NAME", "atom-downloader-training")

AZURE_STORAGE_CONNECTION_STRING = os.environ.get(
    "AZURE_STORAGE_CONNECTION_STRING",
    "DefaultEndpointsProtocol=https;AccountName=atomtraining;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
)
AZURE_CONTAINER_NAME = os.environ.get("AZURE_CONTAINER_NAME", "downloader-training")
BLOB_PREFIX = "enylte/"

# --- Main Functions ---

def get_blob_list(storage_conn_str, container_name, prefix):
    """
    Connects to Azure Blob Storage and lists all blobs with a given prefix.
    """
    try:
        blob_service_client = BlobServiceClient.from_connection_string(storage_conn_str)
        container_client = blob_service_client.get_container_client(container_name)
        
        logging.info(f"Fetching blobs from container '{container_name}' with prefix '{prefix}'...")
        blob_list = [blob.name for blob in container_client.list_blobs(name_starts_with=prefix)]
        
        if not blob_list:
            logging.warning("No blobs found matching the prefix.")
            return []
            
        logging.info(f"Found {len(blob_list)} blob(s):")
        for blob_name in blob_list:
            logging.info(f"  - {blob_name}")
            
        return blob_list
    except Exception as e:
        logging.error(f"Failed to connect to Azure Storage or list blobs: {e}")
        return []

def generate_sas_uri(blob_service_client, container_name, blob_name):
    """
    Generates a short-lived SAS URI for a specific blob.
    """
    sas_token = generate_blob_sas(
        account_name=blob_service_client.account_name,
        container_name=container_name,
        blob_name=blob_name,
        account_key=blob_service_client.credential.account_key,
        permission=BlobSasPermissions(read=True),
        expiry=datetime.now(timezone.utc) + timedelta(hours=1)  # Token is valid for 1 hour
    )
    return f"https://{blob_service_client.account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"

def send_single_message(service_bus_conn_str, queue_name, message_content):
    """
    Sends a single message to the specified Service Bus queue.
    """
    try:
        with ServiceBusClient.from_connection_string(service_bus_conn_str) as client:
            with client.get_queue_sender(queue_name) as sender:
                # Create a Service Bus message from the dictionary
                message = ServiceBusMessage(json.dumps(message_content))
                sender.send_messages(message)
                logging.info(f"Successfully sent message for blob '{message_content['blobName']}'.")
                return True
    except Exception as e:
        logging.error(f"Error sending message to Service Bus: {e}")
        return False


def main():
    """
    Main function to orchestrate finding blobs and sending messages interactively.
    """
    logging.info("--- Starting Interactive Blob to Service Bus Script ---")
    
    blob_names = get_blob_list(AZURE_STORAGE_CONNECTION_STRING, AZURE_CONTAINER_NAME, BLOB_PREFIX)
    
    if not blob_names:
        logging.info("No blobs to process. Exiting.")
        return

    logging.info(f"Found {len(blob_names)} blob(s). Starting interactive processing...\n")

    sent_count = 0
    skipped_count = 0
    quit_requested = False
    
    blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    
    for blob_name in blob_names:
        while True:
            # Ask for user confirmation for each file
            user_input = input(f"Process file '{blob_name}'? (y/n/q to quit): ").lower()
            
            if user_input == 'y':
                logging.info(f"Processing '{blob_name}'...")
                sas_uri = generate_sas_uri(blob_service_client, AZURE_CONTAINER_NAME, blob_name)
                message_content = {
                    "sasUri": sas_uri,
                    "blobName": blob_name,
                    "tenant_id": "training",
                    "subtenant_id": "atomtraining"
                }
                if send_single_message(SERVICE_BUS_CONNECTION_STR, QUEUE_NAME, message_content):
                    sent_count += 1
                # Break the inner while loop to move to the next blob
                break
            
            elif user_input == 'n':
                logging.info(f"Skipping '{blob_name}'.")
                skipped_count += 1
                # Break the inner while loop to move to the next blob
                break
            
            elif user_input == 'q':
                logging.info("User requested to quit.")
                quit_requested = True
                # Break the inner while loop
                break
            
            else:
                print("Invalid input. Please enter 'y' to send, 'n' to skip, or 'q' to quit.")
        
        if quit_requested:
            # Break the outer for loop if user wants to quit
            break

    logging.info("\n--- Script finished ---")
    logging.info(f"Summary: Sent {sent_count} messages, Skipped {skipped_count} files.")


if __name__ == "__main__":
    main()
