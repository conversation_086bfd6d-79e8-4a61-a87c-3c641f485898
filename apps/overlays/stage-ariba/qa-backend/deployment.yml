---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qa-backend
  namespace: ariba
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 0
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: qa-backend
  template:
    metadata:
      labels:
        app: qa-backend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - ariba
      tolerations:
        - key: "ariba"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      serviceAccountName: qa-backend-ariba
      # volumes:
      #   - name: secrets-store-inline
      #     csi:
      #       driver: secrets-store.csi.k8s.io
      #       readOnly: true
      #       volumeAttributes:
      #         secretProviderClass: qa-backend
      dnsPolicy: ClusterFirst
      containers:
        - name: qa-backend
          image: ************.dkr.ecr.us-east-2.amazonaws.com/qa-backend:0d273bf3
          # readinessProbe:
          #   httpGet:
          #     path: /health
          #     port: 3000
          #   initialDelaySeconds: 30
          #   periodSeconds: 15
          #   timeoutSeconds: 5 
          # livenessProbe:
          #   httpGet:
          #     path: /health  
          #     port: 3000
          #   initialDelaySeconds: 30 
          #   periodSeconds: 15
          #   timeoutSeconds: 5
          resources:
            limits:
              memory: 1000Mi
              cpu: 500m
            requests:
              memory: 200Mi
              cpu: 200m
          # envFrom:
          #   - secretRef:
          #       name: qa-backend
          env:
            - name: API_HOST
              value: "0.0.0.0"
            - name: API_PORT
              value: "8000"
            - name: RABBITMQ_HOST
              value: rabbitmq.rabbitmq.svc.cluster.local
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_DEFAULT_USER
              value: user
            - name: RABBITMQ_DEFAULT_PASS
              value: 1hENwBdjtrGj29Tg
            - name: RABBITMQ_DEFAULT_VHOST
              value: "/"
            - name: RABBITMQ_QUEUE_SUB_PACKET_INCOMING
              value: "ariba_to_backend_qa"
            - name: RABBITMQ_QUEUE_SUB_PACKET_SKIPPED_INCOMING
              value: "ariba_to_backend_over_qa"
            - name: RABBITMQ_QUEUE_SUB_PACKET_STATUS
              value: "ariba_packet_status"
            - name: RABBITMQ_QUEUE_SUB_PACKET_SCREENSHOT
              value: "ariba_to_screenshot_postprocess"
            - name: RABBITMQ_QUEUE_PUB
              value: "ariba_from_backend_qa"
            - name: RABBITMQ_QUEUE_PUB_TO_QA_POSTPROCESSOR
              value: "ariba_queue_from_backend_qa"
            - name: FRONTEND_URL 
              value: "https://ariba-qa-frontend-stage.atomadvantage.ai"
            - name: SOURCE_SECRET_KEY
              value: "zqnR3gKxR2h_xZ9YqL8n7XgP6sW5tY3jK2mN1bC0vFg="
            - name: AWS_S3_BUCKET
              value: "atom-advantage-packets-stage"
            - name: ACCESS_TOKEN_EXPIRE_MINUTES
              value: "60"
            - name: API_KEY
              value: "284fd553d1daf5e4a2c68fabeac5ab1890c265231144a48397c520049a4f3921"
            - name: DB_HOST
              value: "backend-ariba.cpowm4cec1q3.us-east-2.rds.amazonaws.com"
            - name: DB_PORT
              value: "5432"
            - name: DB_NAME
              value: "backend_ariba"
            - name: DB_USER
              value: "backend"
            - name: DB_PASSWORD
              value: "fYLgCBZrc0lMrC8Q"
            - name: JWT_REFRESH_SECRET_KEY
              value: "MOoxfc1Vixli1AFiud5qEW_yVEjzBRFTVfxvC_U2COs"
            - name: JWT_SECRET_KEY
              value: "BIHzguO4y97HOtZPuqnYpFHiFpQmjyKxhDAaewSKBV8"
            - name: MAIL_TOKEN
              value: "4562be90340fae1bd6d7ad2e6b17f6f1c506eebd3d230076c5712af81cb594a2"
            - name: MAILGUN_API_DOMAIN
              value: "mg.atomadvantage.ai"
            - name: MAILGUN_API_KEY
              value: "**************************************************"
          # volumeMounts:
          #   - name: secrets-store-inline
          #     mountPath: /secrets
