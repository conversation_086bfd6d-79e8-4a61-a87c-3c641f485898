---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: validate-route
  namespace: ariba
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 0
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: validate-route
  template:
    metadata:
      labels:
        app: validate-route
    spec:
      # serviceAccountName: metadata-extractor
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - ariba
      tolerations:
        - key: "ariba"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"
      # volumes:
      #   - name: secrets-store-inline
      #     csi:
      #       driver: secrets-store.csi.k8s.io
      #       readOnly: true
      #       volumeAttributes:
      #         secretProviderClass: validate-route

      containers:
        - name: validate-route
          image: ************.dkr.ecr.us-east-2.amazonaws.com/validate-route:fec932f0db56639576bfbe467905ab436be48376
          imagePullPolicy: Always
          resources:
            limits:
              memory: "2Gi" 
              cpu: "500m"
            requests:
              memory: "1Gi"
              cpu: "250m"
          # envFrom:
          #   - secretRef:
          #       name: metadata-extractor
          env:
            - name: PROJECT_NAME
              value: "validate-route" 
            - name: APP_NAME
              value: "validate-route"
            - name: LOGGING_LEVEL
              value: "INFO"
            - name: RABBITMQ_HOST
              value: rabbitmq.rabbitmq.svc.cluster.local
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME
              value: "ariba_to_extract_metadata"
            - name: RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME
              value: "ariba_to_postprocess_metadata" 
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "1hENwBdjtrGj29Tg"
            - name: RABBITMQ_TO_VALIDATE_QUEUE_NAME
              value: "ariba_to_validate"
            - name: RABBITMQ_TO_UPLOAD_QUEUE_NAME
              value: "ariba_to_upload"
            - name : RABBITMQ_TO_BACKEND_QA_QUEUE_NAME
              value: "ariba_to_backend_qa"
            - name : RABBITMQ_TO_BACKEND_OVER_QA_NAME
              value: "ariba_to_backend_over_qa"
            - name: MINIO_OBJECT_URI_PREFIX
              value: ""
            - name: MINIO_EXPIRATION_TIME
              value: "604800"
            - name: MINIO_PRESIGNED_URL_HOST
              value: "minio.minio.svc.cluster.local:9000"
            - name: MINIO_PRESIGNED_URL_SECURE
              value: "false"
            - name: MINIMUM_SIZE_OF_LARGE_FILE
              value: "250"
            - name: MINIMAL_CLASSIFICATION_CONFIDENCE
              value: "0.985"
            - name: MINIMAL_METADATA_CONFIDENCE
              value: "0.985"
            - name: FORCE_ROUTE
              value: "1"
            - name: METADATA_DISPLAY
              value: "['namingData', 'metadata']"
            - name: MONITOR_HOST
              value: "http://monitor.ariba.svc.cluster.local"
            - name: MONITOR_PORT
              value: "7795"
            - name: CRYPTOGRAPHY_KEY
              value: "zqnR3gKxR2h_xZ9YqL8n7XgP6sW5tY3jK2mN1bC0vFg="
            - name: TENANT_NAME
              value: "ariba"
            - name: DB_PORT
              value: "5432"
            - name: MINIO_URI
              value: "minio.minio.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "sqMPOQPW04o3Oaagd2vb"
            - name: MINIO_SECRET_KEY
              value: "p5uCaFcPCyZY1t562oJVwUILHqTXiqSSvNBpgzgc"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-stage"
            - name: MINIO_SECURE
              value: "false"
            - name: SERVER_HOST
              value: "llm-server.ariba.svc.cluster.local"
            - name: SERVER_PORT
              value: "11434"
            - name: PGSQL_HOST
              value: "backend-ariba.cpowm4cec1q3.us-east-2.rds.amazonaws.com"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "fYLgCBZrc0lMrC8Q"
            - name: PGSQL_DB_NAME
              value: "rr"
          # volumeMounts:
          #   - name: secrets-store-inline
          #     mountPath: "/secrets" 
          #     readOnly: true
