apiVersion: apps/v1
kind: Deployment
metadata:
  name: llm-server
  namespace: apps
  labels:
    app: llm-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llm-server
  template:
    metadata:
      labels:
        app: llm-server
    spec:
      # serviceAccountName: llm-server
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - "ml-workloads"
      tolerations:
        - key: "ml-workloads"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"
      containers:
        - name: llm-server
          image: ************.dkr.ecr.us-east-2.amazonaws.com/llm-server:59a0aaae
          imagePullPolicy: Always
          ports:
            - containerPort: 11434
              name: ollama-http
          resources:
            limits:
              nvidia.com/gpu: 1
              memory: "16Gi"
              cpu: "4000m"
            requests:
              nvidia.com/gpu: 1
              memory: "4Gi"
              cpu: "2000m"
          # readinessProbe:
          #   httpGet:
          #     path: /
          #     port: 11434
          #   initialDelaySeconds: 60
          #   periodSeconds: 10
          #   timeoutSeconds: 5
          #   failureThreshold: 6
          # livenessProbe:
          #   httpGet:
          #     path: /
          #     port: 11434
          #   initialDelaySeconds: 180
          #   periodSeconds: 20
          #   timeoutSeconds: 5
          env:
            - name: OLLAMA_HOST
              value: "0.0.0.0"
