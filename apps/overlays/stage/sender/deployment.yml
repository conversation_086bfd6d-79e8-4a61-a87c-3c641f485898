---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sender
  namespace: apps
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: sender
  template:
    metadata:
      labels:
        app: sender
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - qa-tool
      tolerations:
        - key: "qa-tool"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      serviceAccountName: uploader
      # volumes:
      #   - name: secrets-store-inline
      #     csi:
      #       driver: secrets-store.csi.k8s.io
      #       readOnly: true
      #       volumeAttributes:
      #         secretProviderClass: downloader
      dnsPolicy: ClusterFirst
      
      containers:
        - name: sender
          image: ************.dkr.ecr.us-east-2.amazonaws.com/sender:latest
          imagePullPolicy: Always
          resources:
            limits:
              memory: 1000Mi
              cpu: 500m
            requests:
              memory: 200Mi
              cpu: 200m
          env:
           - name: AZURE_STORAGE_CONNECTION_STRING
              value: "DefaultEndpointsProtocol=https;AccountName=stageatom;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
            - name: AZURE_CONTAINER_NAME
              value: "files"
            - name: SERVICE_BUS_CONNECTION_STRING
              value: "Endpoint=sb://atom-advantage-stage-service-bus-1.servicebus.windows.net/;SharedAccessKeyName=sbus-queue-policy-stage-atom;SharedAccessKey=pLLvKJWeLfT5h+v61I7fcjjCqbmnE/qPJ+ASbGrnLAc="
            - name: SERVICE_BUS_QUEUE_NAME
              value: "sbq-stagestack-stagename-use-stage-atom-advantage-stage-service-bus-1-queue-1"
            - name: S3_BUCKET_NAME
              value: "atom-advantage-training-control-group-stage"
            - name: S3_REGION
              value: "us-east-2"
