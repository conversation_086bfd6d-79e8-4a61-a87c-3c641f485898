# Use Python 3.9 slim image as base
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the script
COPY sbus.py .

# Set environment variables
ENV SERVICE_BUS_CONNECTION_STRING=""
ENV SERVICE_BUS_QUEUE_NAME=""
ENV AZURE_STORAGE_CONNECTION_STRING=""
ENV AZURE_CONTAINER_NAME=""
ENV S3_BUCKET_NAME=""
ENV S3_REGION=""

# Run the script
CMD ["python", "sbus.py"] 