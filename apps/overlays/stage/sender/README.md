# S3 to Azure File Transfer Service

This service monitors an AWS S3 bucket for new files, uploads them to Azure Blob Storage, and sends file information to Azure Service Bus.

## Features

- Continuously monitors an S3 bucket for new files
- Uploads new files to Azure Blob Storage
- Generates SAS URIs for uploaded files
- Sends file name and SAS URI as JSON to Azure Service Bus
- Tracks processed files to avoid duplication
- Uses IAM roles from Kubernetes for AWS authentication

## Configuration

The application uses environment variables for configuration:

### AWS S3 Configuration
- `S3_BUCKET_NAME`: Name of the S3 bucket to monitor
- `S3_REGION`: AWS region (default: us-east-1)

Note: AWS authentication is handled through IAM roles assigned to the Kubernetes pod, so no AWS credentials need to be provided.

### Azure Blob Storage Configuration
- `AZURE_STORAGE_CONNECTION_STRING`: Azure Storage connection string
- `AZURE_CONTAINER_NAME`: Azure Blob Storage container name (default: files)

### Azure Service Bus Configuration
- `SERVICE_BUS_CONNECTION_STRING`: Azure Service Bus connection string
- `SERVICE_BUS_QUEUE_NAME`: Name of the Service Bus queue

If these environment variables are not provided, the application will use default values, but it's recommended to set them properly in the deployment configuration.

## Deployment

The application can be deployed as a Docker container in Kubernetes with the appropriate IAM role binding:

```bash
docker build -t s3-to-azure-transfer .
```

Example Kubernetes deployment (ensure the appropriate IAM role is bound to the pod):

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: s3-to-azure-transfer
  annotations:
    iam.amazonaws.com/role: "your-s3-access-role"  # AWS IAM role annotation
spec:
  containers:
  - name: s3-to-azure-transfer
    image: s3-to-azure-transfer:latest
    env:
    - name: S3_BUCKET_NAME
      value: "your_bucket_name"
    - name: SERVICE_BUS_CONNECTION_STRING
      valueFrom:
        secretKeyRef:
          name: servicebus-secret
          key: connection-string
    - name: SERVICE_BUS_QUEUE_NAME
      value: "your_queue_name"
    - name: AZURE_STORAGE_CONNECTION_STRING
      valueFrom:
        secretKeyRef:
          name: azure-storage-secret
          key: connection-string
```

## Development

To run the application locally:

1. Install the required dependencies:
   ```bash
   pip install azure-servicebus azure-storage-blob boto3
   ```

2. Set the required environment variables and ensure AWS credentials are configured in your environment if not using IAM roles
3. Run the application:
   ```bash
   python sbus.py
   ``` 