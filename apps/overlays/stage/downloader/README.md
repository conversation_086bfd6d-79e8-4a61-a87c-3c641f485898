# SBUS (Service Bus Upload Script)

This script processes blobs from Azure Blob Storage and sends them to Azure Service Bus for further processing.

## Features

- Process all blobs or filter by folder name
- Configurable tenant_id and subtenant_id
- Batch processing with configurable batch size
- Concurrent processing with configurable worker threads
- Support for environment variables and command-line arguments

## Usage

### Command Line Arguments

```bash
python sbus.py [OPTIONS]
```

#### Available Options:

- `--tenant-id, -t`: Tenant ID (default: from TENANT_ID env var or "atomtest")
- `--subtenant-id, -s`: Subtenant ID (default: from SUBTENANT_ID env var or "atomtest_subclient")
- `--folder, -f`: Folder name to filter blobs (default: from FOLDER env var or empty for all)
- `--batch-size, -b`: Batch size for processing (default: from BATCH_SIZE env var or 100)
- `--max-workers, -w`: Maximum number of worker threads (default: from MAX_WORKERS env var or 10)

### Examples

#### Process all blobs with default settings:
```bash
python sbus.py
```

#### Process blobs from a specific folder:
```bash
python sbus.py --folder "my-folder/"
```

#### Process with custom tenant and subtenant IDs:
```bash
python sbus.py --tenant-id "mytenant" --subtenant-id "mysubtenant"
```

#### Process with custom batch size and workers:
```bash
python sbus.py --batch-size 50 --max-workers 5
```

#### Combine multiple options:
```bash
python sbus.py --folder "data/" --tenant-id "tenant1" --subtenant-id "sub1" --batch-size 200
```

## Docker Usage

### Build the Docker image:
```bash
docker build -t sbus .
```

### Run with environment variables:
```bash
docker run \
  -e TENANT_ID="mytenant" \
  -e SUBTENANT_ID="mysubtenant" \
  -e FOLDER="my-folder/" \
  -e BATCH_SIZE="50" \
  -e MAX_WORKERS="5" \
  sbus
```

### Run with command-line arguments:
```bash
docker run sbus \
  --tenant-id "mytenant" \
  --subtenant-id "mysubtenant" \
  --folder "my-folder/" \
  --batch-size 50 \
  --max-workers 5
```

### Run with both environment variables and command-line arguments (CLI args take precedence):
```bash
docker run \
  -e TENANT_ID="default-tenant" \
  -e SUBTENANT_ID="default-subtenant" \
  sbus \
  --tenant-id "override-tenant" \
  --folder "specific-folder/"
```

## Environment Variables

The script supports the following environment variables:

- `TENANT_ID`: Default tenant ID
- `SUBTENANT_ID`: Default subtenant ID
- `FOLDER`: Default folder name to filter blobs
- `BATCH_SIZE`: Default batch size for processing
- `MAX_WORKERS`: Default number of worker threads
- `AZURE_STORAGE_CONNECTION_STRING`: Azure Storage connection string
- `AZURE_CONTAINER_NAME`: Azure Storage container name

## Message Format

Each message sent to Service Bus contains:

```json
{
  "sasUri": "https://account.blob.core.windows.net/container/blob?token",
  "blobName": "path/to/blob",
  "tenant_id": "tenant_id",
  "subtenant_id": "subtenant_id"
}
```

## Configuration

The script uses the following default configurations:

- Service Bus: `atom-dev-downloader` queue
- Azure Storage: `downloader-dev` container
- Batch Size: 100 messages per batch
- Max Workers: 10 concurrent threads
- SAS Token Expiry: 7 days

## Error Handling

- Failed SAS URI generation: Blob is skipped and logged
- Failed message sending: Batch is marked as failed
- Network errors: Retried automatically by Azure SDK
- Invalid arguments: Script exits with usage information

## Output

The script provides detailed logging including:

- Number of blobs found
- Processing progress
- Success/failure counts
- Processing time statistics
- Summary report at completion 