apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../../base/qa-backend
- ./qa-backend
- ../../base/qa-frontend
- ./qa-frontend
- ../../base/pgbouncer
- ./pgbouncer
- ./downloader
- ../../base/downloader
- ./uploader
- ../../base/uploader
- ../../base/classifier
- ./classifier
- ../../base/metadata-extractor
- ./metadata-extractor
- ../../base/llm-server
- ./llm-server
- ../../base/splitter
- ./splitter
- ../../base/qa-post-processor
- ./qa-post-processor
- ../../base/validate-route
- ./validate-route
- ../../base/metadata-post-processor
- ./metadata-post-processor
# - ./sender
# - ../../base/sender
patches:
- path: qa-backend/sa.yml
- path: qa-backend/deployment.yml
- path: qa-backend/ingress.yml
- path: qa-frontend/deployment.yml
- path: qa-frontend/ingress.yml
- path: pgbouncer/sa.yml
- path: downloader/deployment.yml
- path: downloader/sa.yml
- path: classifier/deployment.yml
- path: classifier/sa.yml
- path: metadata-extractor/deployment.yml
- path: metadata-extractor/sa.yml
- path: llm-server/deployment.yml
- path: uploader/deployment.yml
- path: uploader/sa.yml
- path: splitter/deployment.yml
- path: splitter/sa.yml
- path: qa-post-processor/deployment.yml
- path: qa-post-processor/sa.yml
- path: validate-route/deployment.yml
- path: metadata-post-processor/deployment.yml
- path: metadata-post-processor/sa.yml
# - path: sender/deployment.yml
# - path: sender/sa.yml