---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metadata-extractor
  namespace: apps
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: metadata-extractor
  template:
    metadata:
      labels:
        app: metadata-extractor
    spec:
      serviceAccountName: metadata-extractor
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - "ml-workloads"
      tolerations:
        - key: "ml-workloads"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"

      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: metadata-extractor
        - name: models
          emptyDir: {}
      initContainers:
      - name: model-downloader
        image: amazon/aws-cli
        command:
          - "/bin/sh"
          - "-c"
          - |
            echo "Starting model and config download for metadata-extractor..."
            aws s3 cp s3://atom-advantage-ml-data-dev/handwritten.pt /models_init/handwritten.pt && \
            aws s3 cp s3://atom-advantage-ml-data-dev/lang_detector.pt /models_init/lang_detector.pt && \
            aws s3 cp s3://atom-advantage-ml-data-dev/medical_records_title_and_text_layout_model.pt /models_init/medical_records_title_and_text_layout_model.pt && \
            aws s3 cp s3://atom-advantage-ml-data-dev/rfa_expedited_checkbox_model.pt /models_init/rfa_expedited_checkbox_model.pt && \
            aws s3 cp s3://atom-advantage-ml-data-dev/stamp_is_rush_model.pt /models_init/stamp_is_rush_model.pt && \
            aws s3 cp s3://atom-advantage-ml-data-dev/work_status_summary_checkbox_model.pt /models_init/work_status_summary_checkbox_model.pt && \
            aws s3 cp s3://atom-advantage-ml-data-dev/work_status_title_and_text_layout_model.pt /models_init/work_status_title_and_text_layout_model.pt && \
            echo "Models for metadata-extractor downloaded."
        volumeMounts:
          - name: models
            mountPath: /models_init
        env:
          - name: AWS_REGION
            value: "us-east-2"

      containers:
        - name: metadata-extractor
          image: 112623991000.dkr.ecr.us-east-2.amazonaws.com/metadata-extractor:3f97c6bd994bcb705dac6b77ea463533d2e4ed57
          imagePullPolicy: Always
          resources:
            limits:
              nvidia.com/gpu: 1
              memory: "12Gi" 
              cpu: "1000m"
            requests:
              nvidia.com/gpu: 1
              memory: "6Gi"
              cpu: "500m"
          # envFrom:
          #   - secretRef:
          #       name: metadata-extractor
          env:
            - name: PROJECT_NAME
              value: "metadata-extractor-project" 
            - name: APP_NAME
              value: "metadata-extractor-service"
            - name: LOGGING_LEVEL
              value: "INFO"
            - name: METADATA_EXTRACTOR_DEVICE
              value: "cuda:0" 

            # --- Model & Config Paths (To be read by refactored Python code or used by sub-modules) ---
            # These tell your Python code where the initContainer placed the files.
            # Your Python sub-modules (rfa, summary etc.) must be adapted to use these paths.
            - name: HANDWRITTEN_MODEL_PATH
              value: "/models/handwritten.pt"
            - name: LANG_DETECTOR_MODEL_PATH
              value: "/models/lang_detector.pt"
            - name: MEDICAL_RECORDS_LAYOUT_MODEL_PATH
              value: "/models/medical_records_title_and_text_layout_model.pt"
            - name: RFA_CHECKBOX_MODEL_PATH
              value: "/models/rfa_expedited_checkbox_model.pt"
            - name: RUSH_STAMP_MODEL_PATH
              value: "/models/stamp_is_rush_model.pt"
            - name: WORK_STATUS_CHECKBOX_MODEL_PATH
              value: "/models/work_status_summary_checkbox_model.pt"
            - name: WORK_STATUS_LAYOUT_MODEL_PATH
              value: "/models/work_status_title_and_text_layout_model.pt"
            - name: KEYWORDS_CONFIG_PATH # If Python code reads this path
              value: "/app/keywords_config.yaml" # If baked into image at /app, or /models/keywords_config.yaml if from S3
            - name: RABBITMQ_HOST
              value: rabbitmq.rabbitmq.svc.cluster.local
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME
              value: "to_extract_metadata"
            - name: RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME
              value: "to_postprocess_metadata"
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "Awwmm8FDzgcShYXS"
            - name: DB_PORT
              value: "5432"
            - name: MINIO_URI
              value: "minio.minio.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "YkDLavztCoiaMx5rpo32"
            - name: MINIO_SECRET_KEY
              value: "uA2IrRU9GoaI7BHSG2kfDvXtujgQiE6CqI9LaUrs"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-dev"
            - name: MINIO_SECURE
              value: "false"
            - name: SERVER_HOST
              value: "llm-server.apps.svc.cluster.local"
            - name: SERVER_PORT
              value: "11434"
            - name: PGSQL_HOST
              value: "backend.c54o0q20gxhu.us-east-2.rds.amazonaws.com"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "6M7CLXXeiWBHYUlN"
            - name: PGSQL_DB_NAME
              value: "rr"
          volumeMounts:
            - name: secrets-store-inline
              mountPath: "/secrets" 
              readOnly: true
            - name: models
              mountPath: "/models" 
            # - name: config-volume # If using ConfigMap for keywords_config.yaml
            #   mountPath: "/app/config" # Mount it where the app expects it (e.g., if app opens 'config/keywords_config.yaml')
            #   readOnly: true