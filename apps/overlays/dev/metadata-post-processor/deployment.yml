---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metadata-post-processor
  namespace: apps
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: metadata-post-processor
  template:
    metadata:
      labels:
        app: metadata-post-processor
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - "qa-tool"
      tolerations:
        - key: "qa-tool"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"
      serviceAccountName: metadata-post-processor
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: metadata-post-processor
      dnsPolicy: ClusterFirst
      containers:
        - name: metadata-post-processor
          image: ************.dkr.ecr.us-east-2.amazonaws.com/metadata-post-processor:d33ede377d8f758c428b1bf39fc804c6fa9a0a21
          imagePullPolicy: Always
          resources:
            limits:
              memory: 1000Mi
              cpu: 500m
            requests:
              memory: 200Mi
              cpu: 200m
          env:
            - name: MINIO_URI
              value: "minio.minio.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "YkDLavztCoiaMx5rpo32"
            - name: MINIO_SECRET_KEY
              value: "uA2IrRU9GoaI7BHSG2kfDvXtujgQiE6CqI9LaUrs"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-dev"
            - name: MINIO_SECURE
              value: "false"
            - name: RABBITMQ_HOST
              value: "rabbitmq.rabbitmq.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "Awwmm8FDzgcShYXS"
            - name: RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME
              value: "to_postprocess_metadata"
            - name: RABBITMQ_TO_VALIDATE_QUEUE_NAME
              value: "to_validate"
            - name: PGSQL_HOST
              value: "backend.c54o0q20gxhu.us-east-2.rds.amazonaws.com"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "6M7CLXXeiWBHYUlN"
            - name: PGSQL_DB_NAME
              value: "rr"
            # - name: API_HOST
            #   value: "qa-backend.apps.svc.cluster.local"
            # - name: API_PORT
            #   value: "80"
            - name: project_name
              value: "atom-advantage-packets-dev"
            - name: app_name
              value: "metadata-post-processor"
          volumeMounts:
            - name: secrets-store-inline
              mountPath: /secrets
