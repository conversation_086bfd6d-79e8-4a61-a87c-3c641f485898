from azure.servicebus import ServiceBusClient, ServiceBusMessage
import json

# Service Bus configuration
CONNECTION_STR = "Endpoint=sb://atom-advantage-dev-service-bus-1.servicebus.windows.net/;SharedAccessKeyName=atom-dev-downloader;SharedAccessKey=GHgu5dmVwaZtng5kXS4Xd4l3xx/T4p/W8+ASbGMN3Iw=;EntityPath=atom-dev-downloader"
QUEUE_NAME = "atom-dev-downloader"

def send_message_to_servicebus(sas_uri, blob_name):
    try:
        # Create a Service Bus client
        servicebus_client = ServiceBusClient.from_connection_string(
            conn_str=CONNECTION_STR,
            logging_enable=True
        )

        # Create the message content
        message_content = {
            "sasUri": sas_uri,
            "blobName": blob_name
        }

        # Convert to JSON string
        message_body = json.dumps(message_content)

        # Create a Service Bus message
        message = ServiceBusMessage(message_body)

        # Send to queue
        with servicebus_client:
            sender = servicebus_client.get_queue_sender(queue_name=QUEUE_NAME)
            with sender:
                sender.send_messages(message)
            print(f"Message sent to queue {QUEUE_NAME}")

    except Exception as e:
        print(f"Error sending message to Service Bus: {e}")

if __name__ == "__main__":
    # Example usage
    sas_uri = "*****************************************************************?sp=r&st=2025-05-27T11:00:19Z&se=2025-06-06T19:00:19Z&spr=https&sv=2024-11-04&sr=b&sig=DBZRs63sDwENSgdvPEvfMe3gurhVdFt8N11VfyvXNrs%3D"
    blob_name = "1093318_3695602.tif"
    
    send_message_to_servicebus(sas_uri, blob_name)
