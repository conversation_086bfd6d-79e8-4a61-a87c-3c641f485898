import os
import time
import boto3
from datetime import datetime, timedelta
from azure.servicebus import ServiceBusClient, ServiceBusMessage
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
import json

# Service Bus configuration
CONNECTION_STR = os.environ.get("SERVICE_BUS_CONNECTION_STRING", "Endpoint=sb://atom-advantage-dev-service-bus-1.servicebus.windows.net/;SharedAccessKeyName=kamil;SharedAccessKey=MQGjhOgipRmUEZIATcu1hFHTrUlNOFfgu+ASbKulQ10=;EntityPath=sbq-devstack-devname-use-dev-atom-advantage-dev-service-bus-1-queue-1")
QUEUE_NAME = os.environ.get("SERVICE_BUS_QUEUE_NAME", "sbq-devstack-devname-use-dev-atom-advantage-dev-service-bus-1-queue-1")

# Azure Blob Storage configuration
AZURE_STORAGE_CONNECTION_STRING = os.environ.get("AZURE_STORAGE_CONNECTION_STRING", "DefaultEndpointsProtocol=https;AccountName=stageatom;AccountKey=your_account_key;EndpointSuffix=core.windows.net")
AZURE_CONTAINER_NAME = os.environ.get("AZURE_CONTAINER_NAME", "files")

# AWS S3 configuration
S3_BUCKET_NAME = os.environ.get("S3_BUCKET_NAME", "your_s3_bucket")
S3_REGION = os.environ.get("S3_REGION", "us-east-1")

# Track processed files
processed_files = set()

def upload_to_blob_storage(file_path, blob_name):
    """Upload a file to Azure Blob Storage and generate a SAS URI"""
    try:
        # Create the BlobServiceClient
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        
        # Get a reference to a container
        container_client = blob_service_client.get_container_client(AZURE_CONTAINER_NAME)
        
        # Create a blob client using the local file name as the name for the blob
        blob_client = container_client.get_blob_client(blob_name)
        
        # Upload the file
        with open(file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)
        
        # Generate SAS token for the blob
        sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=AZURE_CONTAINER_NAME,
            blob_name=blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True),
            expiry=datetime.utcnow() + timedelta(days=7)
        )
        
        # Construct the SAS URI
        sas_uri = f"https://{blob_service_client.account_name}.blob.core.windows.net/{AZURE_CONTAINER_NAME}/{blob_name}?{sas_token}"
        
        print(f"File uploaded to Azure Blob Storage: {blob_name}")
        return sas_uri
        
    except Exception as e:
        print(f"Error uploading to Azure Blob Storage: {e}")
        return None

def send_message_to_servicebus(sas_uri, blob_name):
    try:
        # Create a Service Bus client
        servicebus_client = ServiceBusClient.from_connection_string(
            conn_str=CONNECTION_STR,
            logging_enable=True
        )

        # Create the message content
        message_content = {
            "sasUri": sas_uri,
            "blobName": blob_name
        }

        # Convert to JSON string
        message_body = json.dumps(message_content)

        # Create a Service Bus message
        message = ServiceBusMessage(message_body)

        # Send to queue
        with servicebus_client:
            sender = servicebus_client.get_queue_sender(queue_name=QUEUE_NAME)
            with sender:
                sender.send_messages(message)
            print(f"Message sent to queue {QUEUE_NAME}")

    except Exception as e:
        print(f"Error sending message to Service Bus: {e}")

def download_from_s3(s3_client, bucket_name, object_key, local_path):
    """Download a file from S3 to a local path"""
    try:
        s3_client.download_file(bucket_name, object_key, local_path)
        print(f"Downloaded {object_key} to {local_path}")
        return True
    except Exception as e:
        print(f"Error downloading from S3: {e}")
        return False

def check_s3_for_new_files():
    """Check S3 for new files and process them"""
    try:
        # Create S3 client using IAM role assigned to pod
        s3_client = boto3.client('s3')
        
        # List objects in the bucket
        response = s3_client.list_objects_v2(Bucket=S3_BUCKET_NAME)
        
        if 'Contents' not in response:
            print("No files found in S3 bucket")
            return
        
        # Process each file
        for item in response['Contents']:
            object_key = item['Key']
            
            # Skip if already processed
            if object_key in processed_files:
                continue
                
            print(f"Found new file in S3: {object_key}")
            
            # Create a temporary local file path
            local_file_path = f"/tmp/{object_key.split('/')[-1]}"
            
            # Download the file from S3
            if download_from_s3(s3_client, S3_BUCKET_NAME, object_key, local_file_path):
                # Upload to Azure Blob Storage
                blob_name = object_key.split('/')[-1]
                sas_uri = upload_to_blob_storage(local_file_path, blob_name)
                
                if sas_uri:
                    # Send message to Service Bus
                    send_message_to_servicebus(sas_uri, blob_name)
                    
                    # Mark as processed
                    processed_files.add(object_key)
                    
                # Clean up local file
                if os.path.exists(local_file_path):
                    os.remove(local_file_path)
    
    except Exception as e:
        print(f"Error checking S3 for new files: {e}")

def main():
    """Main function to continuously monitor S3 for new files"""
    print("Starting S3 to Azure file transfer service...")
    
    while True:
        print("Checking for new files in S3...")
        check_s3_for_new_files()
        
        # Wait before checking again
        time.sleep(60)  # Check every minute

if __name__ == "__main__":
    main()
