apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: qa-backend
  namespace: apps
  annotations:
    nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
    nginx.ingress.kubernetes.io/proxy-read-timeout:  "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout:  "3600"
spec:
  ingressClassName: nginx
  rules:
    - host: api.atomadvantage.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: qa-backend
                port:
                  number: 80
