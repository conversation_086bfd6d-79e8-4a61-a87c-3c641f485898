---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: classifier
  namespace: apps
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: classifier
  template:
    metadata:
      labels:
        app: classifier
    spec:
      serviceAccountName: classifier
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - "ml-workloads"
      tolerations:
        - key: "ml-workloads"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"

      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: classifier
        - name: models
          emptyDir: {}

      initContainers:
      - name: model-downloader
        image: amazon/aws-cli
        command:
          - "/bin/sh"
          - "-c"
          - |
            aws s3 cp s3://atom-advantage-ml-data-prod/general_model.pkl /models_init/general_model.pkl && \
            aws s3 cp s3://atom-advantage-ml-data-prod/packet_model.pkl /models_init/packet_model.pkl && \
            aws s3 cp s3://atom-advantage-ml-data-prod/header_content_model.pkl /models_init/header_content_model.pkl && \
            echo "Models downloaded"
        volumeMounts:
          - name: models
            mountPath: /models_init
        env:
          - name: AWS_REGION
            value: "us-east-2"
      containers:
        - name: classifier
          image: 112623991000.dkr.ecr.us-east-2.amazonaws.com/classifier:aed0d00f89510dfa0976140512ac86205b0b3666
          imagePullPolicy: Always
          resources:
            limits:
              nvidia.com/gpu: 1
              memory: "8Gi" 
              cpu: "2000m"
            requests:
              nvidia.com/gpu: 1
              memory: "1Gi"
              cpu: "500m"
          envFrom:
            - secretRef:
                name: classifier
          env:
            - name: PROJECT_NAME
              value: "classifier"
            - name: APP_NAME
              value: "classifier-service"
            - name: TENANT_NAME
              value: "ehs"
            - name: DOWNLOADER_TENANT_NAME
              value: "ehs"
            - name: LOGGING_LEVEL
              value: "INFO"
            - name: CLASSIFIER_DEVICE
              value: "cuda:0"
            - name: CLASSIFIER_GENERAL_MODEL_PATH
              value: "/models/general_model.pkl"
            - name: CLASSIFIER_PACKET_MODEL_PATH
              value: "/models/packet_model.pkl"
            - name: CLASSIFIER_HEADER_CONTENT_MODEL_PATH
              value: "/models/header_content_model.pkl"
            - name: RABBITMQ_HOST
              value: "rabbitmq.rabbitmq.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_DEFAULT_USER
              value: "user"
            - name: RABBITMQ_DEFAULT_PASS
              value: "grzHmuF5BPTA90ep"
            - name: DB_HOST
              value: "backend.czyisk8s0rfo.us-east-2.rds.amazonaws.com" 
            - name: DB_PORT
              value: "5432"
            - name: DB_USER
              value: "backend"
            - name: DB_PASSWORD
              value: "gpyM5NMKMRNEcyAJ"
            - name: DB_NAME
              value: "rr"
            - name: MINIO_URI
              value: "minio.minio.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "LcI2MjXEXvhlKHRIhnQ9"
            - name: MINIO_SECRET_KEY
              value: "A99tYUzzQ5wNVJwWbFqI6dkLfnO7ltXrfhPwAC9R"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-prod"
            - name: MINIO_SECURE
              value: "false"
            - name: MONITOR_HOST
              value: "http://127.0.0.1"
            - name: MONITOR_PORT
              value: "7795"
            - name: RABBITMQ_TO_CLASSIFY_QUEUE_NAME
              value : "to_classify"
            - name: RABBITMQ_TO_SPLIT_QUEUE_NAME
              value : "to_split"
          volumeMounts:
            - name: secrets-store-inline
              mountPath: "/secrets"
              readOnly: true
            - name: models
              mountPath: "/models"
