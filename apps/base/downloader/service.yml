---
apiVersion: v1
kind: Service
metadata:
  name: downloader
  namespace: apps
spec:
  selector:
    app: downloader
  ports:
    - port: 80
      targetPort: 8000
      name: "downloader"
  type: ClusterIP 

---
apiVersion: v1
kind: Service
metadata:
  name: monitor
  namespace: apps
spec:
  selector:
    app: downloader
  ports:
    - port: 7795
      targetPort: 7795
      name: "downloader"
  type: ClusterIP 