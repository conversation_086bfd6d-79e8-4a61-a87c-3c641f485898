apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
    name: validate-route
    namespace: apps
spec:
    provider: aws
    secretObjects:                                
    - secretName: validate-route
      type: Opaque
      data:
      - objectName: PGHOST
        key: PGSQL_HOST
      - objectName: PGPASSWORD
        key: PGSQL_PASSWORD
      - objectName: PGUSER
        key: PGSQL_USERNAME
      - objectName: PGPORT
        key: PGSQL_PORT
      - objectName: PGDATABASE
        key: PGSQL_DB_NAME
    parameters:
        objects: |
            - objectName: "/rds/backend/PGHOST"
              objectType: "ssmparameter"
              objectAlias: "PGHOST"
            - objectName: "/rds/backend/PGPASSWORD"
              objectType: "ssmparameter"
              objectAlias: "PGPASSWORD"
            - objectName: "/rds/backend/PGUSER"
              objectType: "ssmparameter"
              objectAlias: "PGUSER"
            - objectName: "/rds/backend/PGPORT"
              objectType: "ssmparameter"
              objectAlias: "PGPORT"
            - objectName: "/rds/backend/PGDATABASE"
              objectType: "ssmparameter"
              objectAlias: "PGDATABASE"
