apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgbouncer
  namespace: apps
  labels:
    app: pgbouncer
spec:
  revisionHistoryLimit: 0
  strategy:
    rollingUpdate:
      maxUnavailable: 0 
  selector:
    matchLabels:
      app: pgbouncer
  template:
    metadata:
      labels:
        app: pgbouncer
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: assignment
                operator: In
                values:
                - qa-tool
      tolerations:
      - key: "qa-tool"
        operator: Equal
        value: "true"
        effect: "NoExecute"
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: pgbouncer

      serviceAccountName: pgbouncer
      containers:
        - name: pgbouncer
          image: bitnami/pgbouncer:latest
          resources:
            limits:
              cpu: "200m"
              memory: "256Mi"
            requests:
              cpu: "50m"
              memory: "64Mi"
          ports:
            - containerPort: 5432
          envFrom:
            - secretRef:
               name: pgbouncer
          env:
            - name: POOL_MODE
              value: session
            - name: AUTH_TYPE
              value: trust
            - name: PGBOUNCER_PORT
              value: "5432"
            - name: PGBOUNCER_IGNORE_STARTUP_PARAMETERS
              value: extra_float_digits
          volumeMounts:
            - name: secrets-store-inline
              mountPath: /secrets
          livenessProbe:
            tcpSocket:
              port: 5432
            periodSeconds: 60
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "killall -INT pgbouncer && sleep 120"]
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop: ['all']
