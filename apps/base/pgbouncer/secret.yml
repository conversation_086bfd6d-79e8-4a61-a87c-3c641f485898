apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
    name: pgbouncer
    namespace: apps
spec:
    provider: aws
    secretObjects:                                
    - secretName: pgbouncer
      type: Opaque
      data:
      - objectName: PGHOST
        key: POSTGRESQL_HOST
      - objectName: PGUSER
        key: POSTGRESQL_USERNAME
      - objectName: PGPASSWORD
        key: POSTGRESQL_PASSWORD
      - objectName: PGDATABASE
        key: POSTGRESQL_DATABASE
      - objectName: PGDATABASE
        key: PGBOUNCER_DATABASE

    parameters:
        objects: |
            - objectName: "/rds/backend/PGHOST"
              objectType: "ssmparameter"
              objectAlias: "PGHOST"
            - objectName: "/rds/backend/PGPASSWORD"
              objectType: "ssmparameter"
              objectAlias: "PGPASSWORD"
            - objectName: "/rds/backend/PGPORT"
              objectType: "ssmparameter"
              objectAlias: "PGPORT"
            - objectName: "/rds/backend/PGUSER"
              objectType: "ssmparameter"
              objectAlias: "PGUSER"
            - objectName: "/rds/backend/PGDATABASE"
              objectType: "ssmparameter"
              objectAlias: "PGDATABASE"
