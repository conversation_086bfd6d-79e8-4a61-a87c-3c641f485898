apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
    name: data-cleaner
    namespace: apps
spec:
    provider: aws
    secretObjects:                                
    - secretName: data-cleaner
      type: Opaque
      data:
      - objectName: GOOGLE_APPLICATION_CREDENTIALS
        key: GOOGLE_APPLICATION_CREDENTIALS
    parameters:
        objects: |
            - objectName: "/atom-advantage/data-cleaner/GOOGLE_APPLICATION_CREDENTIALS"
              objectType: "ssmparameter"
              objectAlias: "GOOGLE_APPLICATION_CREDENTIALS" 