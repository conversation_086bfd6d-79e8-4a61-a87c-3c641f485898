apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
    name: qa-backend
    namespace: apps
spec:
    provider: aws
    secretObjects:                                
    - secretName: qa-backend
      type: Opaque
      data:
      - objectName: PGHOST
        key: DB_HOST
      - objectName: PGPASSWORD
        key: DB_PASSWORD
      - objectName: PGUSER
        key: DB_USER
      - objectName: PGPORT
        key: DB_PORT
      - objectName: PGDATABASE
        key: DB_NAME
      - objectName: API_KEY
        key: API_KEY
      - objectName: JWT_SECRET_KEY
        key: JWT_SECRET_KEY
      - objectName: JWT_REFRESH_SECRET_KEY
        key: JWT_REFRESH_SECRET_KEY
      - objectName: SOURCE_SECRET_KEY
        key: SOURCE_SECRET_KEY
      - objectName: MAILGUN_API_DOMAIN
        key: MAILGUN_API_DOMAIN
      - objectName: MAILGUN_API_KEY
        key: MAILGUN_API_KEY
      - objectName: MAIL_TOKEN
        key: MAIL_TOKEN
    parameters:
        objects: |
            - objectName: "/rds/backend/PGHOST"
              objectType: "ssmparameter"
              objectAlias: "PGHOST"
            - objectName: "/rds/backend/PGPASSWORD"
              objectType: "ssmparameter"
              objectAlias: "PGPASSWORD"
            - objectName: "/rds/backend/PGUSER"
              objectType: "ssmparameter"
              objectAlias: "PGUSER"
            - objectName: "/rds/backend/PGPORT"
              objectType: "ssmparameter"
              objectAlias: "PGPORT"
            - objectName: "/rds/backend/PGDATABASE"
              objectType: "ssmparameter"
              objectAlias: "PGDATABASE"
            - objectName: "/atom-advantage/qa-backend/API_KEY"
              objectType: "ssmparameter"
              objectAlias: "API_KEY"
            - objectName: "/atom-advantage/qa-backend/JWT_SECRET_KEY"
              objectType: "ssmparameter"
              objectAlias: "JWT_SECRET_KEY"
            - objectName: "/atom-advantage/qa-backend/JWT_REFRESH_SECRET_KEY"
              objectType: "ssmparameter"
              objectAlias: "JWT_REFRESH_SECRET_KEY"
            - objectName: "/atom-advantage/qa-backend/SOURCE_SECRET_KEY"
              objectType: "ssmparameter"
              objectAlias: "SOURCE_SECRET_KEY"
            - objectName: "/atom-advantage/qa-backend/MAILGUN_API_DOMAIN"
              objectType: "ssmparameter"
              objectAlias: "MAILGUN_API_DOMAIN"
            - objectName: "/atom-advantage/qa-backend/MAILGUN_API_KEY"
              objectType: "ssmparameter"
              objectAlias: "MAILGUN_API_KEY"
            - objectName: "/atom-advantage/qa-backend/MAIL_TOKEN"
              objectType: "ssmparameter"
              objectAlias: "MAIL_TOKEN"
