apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
    name: classifier
    namespace: apps
spec:
    provider: aws
    secretObjects:                                
    - secretName: classifier
      type: Opaque
      data:
      - objectName: PGHOST
        key: DB_HOST
      - objectName: PGPASSWORD
        key: DB_PASSWORD
      - objectName: PGUSER
        key: DB_USER
      - objectName: PGPORT
        key: DB_PORT
      - objectName: PGDATABASE
        key: DB_NAME
    parameters:
        objects: |
            - objectName: "/rds/backend/PGHOST"
              objectType: "ssmparameter"
              objectAlias: "PGHOST"
            - objectName: "/rds/backend/PGPASSWORD"
              objectType: "ssmparameter"
              objectAlias: "PGPASSWORD"
            - objectName: "/rds/backend/PGUSER"
              objectType: "ssmparameter"
              objectAlias: "PGUSER"
            - objectName: "/rds/backend/PGPORT"
              objectType: "ssmparameter"
              objectAlias: "PGPORT"
            - objectName: "/rds/backend/PGDATABASE"
              objectType: "ssmparameter"
              objectAlias: "PGDATABASE"
