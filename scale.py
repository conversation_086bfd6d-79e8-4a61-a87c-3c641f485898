import os
import subprocess
import sys
from pathlib import Path
from ruamel.yaml import YAM<PERSON>

def find_deployment_files(env):
    """Finds all deployment.yml files in the specified environment directory."""
    base_path = Path(f'apps/overlays/{env}')
    if not base_path.is_dir():
        print(f"Error: Environment directory not found at '{base_path}'")
        sys.exit(1)
    return list(base_path.glob('**/deployment.yml'))

def update_replicas(file_path, replicas):
    """Updates the replica count in a given deployment file."""
    yaml = YAML()
    yaml.preserve_quotes = True
    with open(file_path, 'r') as f:
        content = yaml.load(f)

    if 'spec' in content and 'replicas' in content['spec']:
        content['spec']['replicas'] = replicas
        with open(file_path, 'w') as f:
            yaml.dump(content, f)
        return True
    return False

def git_commit_and_push(env, action):
    """Commits and pushes changes to the git repository."""
    commit_message = f"feat(ops): scale {action} {env} environment"
    try:
        subprocess.run(['git', 'add', 'apps/overlays/stage-training/'], check=True)
        subprocess.run(['git', 'commit', '-m', commit_message], check=True)
        subprocess.run(['git', 'push'], check=True)
        print("Changes have been committed and pushed to the main branch.")
    except subprocess.CalledProcessError as e:
        print(f"An error occurred during git operation: {e}")
        print("Please check your git configuration and permissions.")
        sys.exit(1)

def main():
    """Main function to scale environments up or down."""
    if len(sys.argv) != 3:
        print("Usage: python scale.py <up|down> <environment>")
        print("Example: python scale.py up stage-training")
        sys.exit(1)

    action = sys.argv[1]
    env = sys.argv[2]

    if action not in ['up', 'down']:
        print("Error: Invalid action. Must be 'up' or 'down'.")
        sys.exit(1)
    
    if env != 'stage-training':
        print("Error: This script currently only supports the 'stage-training' environment.")
        sys.exit(1)

    replicas = 1 if action == 'up' else 0

    deployment_files = find_deployment_files(env)
    if not deployment_files:
        print(f"No 'deployment.yml' files found in '{env}' environment.")
        sys.exit(0)

    print(f"The following files will be modified to set replicas to {replicas}:")
    for f in deployment_files:
        print(f"  - {f}")

    confirm = input("Do you want to proceed? (y/n): ")
    if confirm.lower() != 'y':
        print("Operation cancelled.")
        sys.exit(0)

    for file_path in deployment_files:
        if update_replicas(file_path, replicas):
            print(f"Updated {file_path}")
        else:
            print(f"Warning: Could not find 'spec.replicas' in {file_path}. Skipping.")

    print("\nAll specified deployment files have been updated.")
    
    git_confirm = input("Do you want to commit and push these changes? (y/n): ")
    if git_confirm.lower() == 'y':
        git_commit_and_push(env, action)
    else:
        print("Changes have been saved locally but not pushed.")


if __name__ == "__main__":
    main() 