FROM 112623991000.dkr.ecr.us-east-2.amazonaws.com/paddle-gpu:2.6.1-gpu-cuda11.7-cudnn8.4-trt8.4

ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y poppler-utils

WORKDIR /app

COPY ./requirements.txt /app/requirements.txt
COPY pipeline_utils /app/pipeline_utils

RUN python3 -m pip install -r requirements.txt

COPY select_and_populate.py /app/select_and_populate.py

CMD ["python3", "-u", "select_and_populate.py"]
