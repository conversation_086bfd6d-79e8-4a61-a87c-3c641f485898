from enum import Enum
import json
import os
import time
from uuid6 import uuid7
import requests
from threading import Thread
from datetime import datetime

class Status(Enum):
    UP = 1
    DOWN = 0

class MonitorService:
    def __init__(self, project_name, application, module_name, api_host, api_port, uuid_file='service_uuid.json'):
        self.project_name = project_name
        self.application = application
        self.module_name = module_name
        self.api_host = api_host
        self.api_port = api_port
        self.uuid_file = uuid_file
        self.status = None
        self.service_uuid = self._get_or_create_uuid()
        self.status = Status.DOWN

    def _get_or_create_uuid(self):
        if os.path.exists(self.uuid_file):
            with open(self.uuid_file, 'r') as f:
                data = json.load(f)
                return data.get("SERVICE_UUID")
        else:
            service_uuid = uuid7()
            with open(self.uuid_file, 'w') as f:
                json.dump({"SERVICE_UUID": str(service_uuid)}, f)
            return str(service_uuid)


# {
#     "application": "pipeline",
#     "name": "downloader_status",
#     "replica_id": "01909261-90c6-7291-8835-60d32984c143",
#     "value": 1,
#     "type": "gauge",
#     "help": "The metric corresponds to status of downloader status",
#     "additional_parameters": {
#         "client_id": "01909261-90c6-7291-8835-60d32984c143"
#     }
# }

    def _check_status(self):
        while True:
            status_data = {
                "application": self.application,
                "type": "gauge",
                "replica_id": str(self.service_uuid),
                "name": f"{self.module_name}_status",
                "value": self.status.value,
                "help": f"The metric corresponds to status of {self.module_name} status",
                "additional_parameters": {
                    "client": self.project_name,
                }
            }
            try:
                r = requests.post(f"{self.api_host}:{self.api_port}/api/v1/update_or_create_service", json=status_data) 
                # print(r)
            except Exception as e:
                 pass #print(f'Call to monitoring api failed: {e}')
            time.sleep(30)

    def start_monitoring(self):
        Thread(target=self._check_status, daemon=True).start()

    def update_status(self, status: Status):
        if type(status) == Status:
            self.status = status
        else:
            raise ValueError(f"Invalid status: {status}. Valid statuses has to be of type {Status}")
