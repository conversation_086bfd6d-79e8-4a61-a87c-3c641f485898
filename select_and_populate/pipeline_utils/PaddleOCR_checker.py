from __future__ import annotations

import importlib
import subprocess
import sys


def ensure_numpy(target_version: str = "1.26.4") -> None:
    """Downgrade/upgrade NumPy in-place if the requested version is not active.

    Parameters
    ----------
    target_version : str, optional
        Exact NumPy version that matches Paddle’s ABI (default: ``1.26.4``).

    Notes
    -----
    • Must be called **before** importing PaddlePaddle, Torch, etc.
    • Adds ~2–3 s of cold-start overhead if a reinstall is required.
    • Prefer baking the correct version into the Docker image; use this
      helper only as a temporary workaround.
    """
    # 1) Short-circuit if the required version is already imported
    try:
        import numpy as _np  # noqa: WPS433 (allowed here)
        if _np.__version__.startswith(target_version):
            return
    except ModuleNotFoundError:
        pass  # NumPy not installed yet → will install next

    print(f"⚙️  Installing NumPy {target_version} …", file=sys.stderr)
    subprocess.run(
        [
            sys.executable,
            "-m",
            "pip",
            "install",
            "--no-cache-dir",
            "--force-reinstall",
            f"numpy=={target_version}",
        ],
        check=True,
    )

    # 2) Purge any NumPy already loaded into the interpreter
    importlib.invalidate_caches()
    for key in list(sys.modules):
        if key.startswith("numpy"):
            del sys.modules[key]

    # 3) Import the freshly installed NumPy so downstream libs see it
    importlib.import_module("numpy")  # noqa: WPS433