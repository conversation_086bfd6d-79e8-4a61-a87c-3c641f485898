from __future__ import annotations
import io, logging, time
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import List, Optional, Tuple

import boto3
from botocore.config import Config as BotoConfig
from botocore.exceptions import BotoCoreError, ClientError

_LOG = logging.getLogger("pipeline_utils.s3_inbox")


@dataclass(frozen=True, slots=True)
class S3Settings:
    bucket: str
    prefix: str
    region: str
    secure: bool
    endpoint: str | None = None
    access_key: str | None = None
    secret_key: str | None = None
    new_file_cutoff: int = 300
    download_timeout: int = 60
    max_attempts: int = 6


class _ClientSingleton:
    _c = None

    @classmethod
    def get(cls, s: S3Settings):
        if cls._c is None:
            cls._c = boto3.client(
                "s3",
                region_name=s.region,
                endpoint_url=s.endpoint or None,
                use_ssl=s.secure,
                aws_access_key_id=s.access_key,
                aws_secret_access_key=s.secret_key,
                config=BotoConfig(
                    retries={"max_attempts": s.max_attempts, "mode": "standard"},
                    connect_timeout=10,
                    read_timeout=s.download_timeout,
                ),
            )
        return cls._c


class S3Downloader:
    """Same public API as SFTPDownloader, so other code stays untouched."""

    def __init__(self, cfg: S3Settings):
        self.cfg = cfg
        self.cli = _ClientSingleton.get(cfg)
        self.queue: List[str] = []
        self.ignored: set[str] = set()
        self.cutoff = datetime.now(timezone.utc)

    def refresh_queue(self) -> None:
        try:
            for k in self._list_new():
                if k not in self.queue and k not in self.ignored:
                    self.queue.append(k)
        except (BotoCoreError, ClientError) as e:
            _LOG.exception("list_objects failed: %s", e)

    def queue_is_not_empty(self) -> bool:
        return bool(self.queue)

    def get_next_file(self) -> Tuple[Optional[bytes], str]:
        key = self.queue.pop()
        try:
            start = time.time()
            obj = self.cli.get_object(Bucket=self.cfg.bucket, Key=key)
            buf = io.BytesIO()
            for chunk in obj["Body"].iter_chunks():
                buf.write(chunk)
            dur = time.time() - start
            if dur > self.cfg.download_timeout:
                _LOG.warning("download %.1fs > timeout, skip %s", dur, key)
                self.ignored.add(key)
                return None, key
            return buf.getvalue(), key
        except (BotoCoreError, ClientError) as e:
            _LOG.error("download %s failed: %s", key, e)
            self.ignored.add(key)
            return None, key

    def tag_processed(self, key: str) -> None:
        try:
            self.cli.put_object_tagging(
                Bucket=self.cfg.bucket,
                Key=key,
                Tagging={"TagSet": [{"Key": "processed", "Value": "true"}]},
            )
        except (BotoCoreError, ClientError):
            pass

    def remove_file(self, key: str) -> None:
        try:
            self.cli.delete_object(Bucket=self.cfg.bucket, Key=key)
        except (BotoCoreError, ClientError):
            pass

    def _list_new(self) -> list[str]:
        paginator = self.cli.get_paginator("list_objects_v2")
        pages = paginator.paginate(
            Bucket=self.cfg.bucket,
            Prefix=self.cfg.prefix.lstrip("/"),
            PaginationConfig={"PageSize": 1000},
        )
        keys = []
        for p in pages:
            for obj in p.get("Contents", []):
                k = obj["Key"]
                if k.endswith("/"):
                    continue
                if k not in self.ignored:
                    keys.append(k)
        return keys
