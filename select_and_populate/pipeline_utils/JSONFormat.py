import copy

from requests.structures import CaseInsensitiveDict

### MAPPING_TUPLES: left is higher priority
MAPPING_TUPLES = [
    (
        ["claimNumber"],
        ["metaData", "claim", "claimNumber"],
        ["namingData", "claimNumber"],
    ),
    (
        ["patientName"],
        ["metaData", "claimant", "claimantName"],
        ["namingData", "patientName"],
    ),
    (["metaData", "senderName"], ["namingData", "senderName"]),
    (["metaData", "docDate"], ["metaData", "docDate"], ["namingData", "docDate"]),
    (["metaData", "docReceivedDate"], ["namingData", "docReceivedDate"]),
    (["metaData", "claimant", "firstName"], ["namingData", "patientFirstName"]),
    (["metaData", "claimant", "middleName"], ["namingData", "patientMiddleName"]),
    (["metaData", "claimant", "lastName"], ["namingData", "patientLastName"]),
    (
        ["docState"],
        ["metaData", "jurisdiction"],
        ["metaData", "claim", "jurisState"],
        ["metaData", "docState"],
        ["namingData", "docState"],
    ),
    (
        ["metaData", "physician", "facilityName"],
        ["namingData", "providerOrFacilityName"],
    ),
]


def fill_mapped_fields(data):
    """
    Populates the mapped fields in the provided data dictionary with the first non-empty value found in the mapping tuples.

    Args:
        data (dict): The dictionary containing data to be filled.

    Steps:
        1. Iterate over each tuple of field paths in MAPPING_TUPLES.
        2. Initialize a list to store values and a list to store existing field paths.
        3. For each field path in the tuple:
            a. Check if the nested key exists in the data dictionary.
            b. If the key exists, retrieve its value and append it to the values list.
            c. Append the field path to the existing field paths list.
        4. If there are values in the values list, set the first value to all existing field paths in the data dictionary.

    Note:
        This function ensures that the first found non-empty value is used to populate all the fields in the tuple.
    """

    for fields in MAPPING_TUPLES:
        values = []
        existing_field_paths = []

        for field_path in fields:
            if check_nested_key_exists(data, field_path):
                value = get_nested_value(data, field_path)
                if value:
                    values.append(value)
                existing_field_paths.append(field_path)

        if values:
            value_to_set = values[0]  # Take the first found value
            for field_path in existing_field_paths:
                set_nested_value(data, field_path, value_to_set)


def get_nested_value(data, keys):
    """
    Retrieves the value from a nested dictionary using a list of keys.

    Args:
        data (dict): The dictionary to retrieve the value from.
        keys (list): A list of keys representing the path to the desired value.

    Returns:
        The value found at the specified nested path, or None if any key in the path does not exist.

    Steps:
        1. Iterate through each key in the keys list.
        2. If the current data is None, return None.
        3. Update the data to the value associated with the current key.
        4. Return the final value after traversing all keys.
    """

    for key in keys:
        if data is None:
            return None
        data = data.get(key, None)
    return data


def set_nested_value(data, keys, value):
    """
    Sets a value in a nested dictionary using a list of keys to specify the path.

    Args:
        data (dict): The dictionary to set the value in.
        keys (list): A list of keys representing the path to the location where the value should be set.
        value: The value to set at the specified nested path.

    Steps:
        1. Iterate through each key in the keys list except the last one.
        2. If the current data is None, return without setting the value.
        3. Update the data to the value associated with the current key, if it exists.
        4. If the final key exists in the dictionary, set the value at that key.
    """

    for key in keys[:-1]:
        if data is None:
            return
        if key in data:
            data = data[key]
        else:
            return
    if data is not None and keys[-1] in data:
        data[keys[-1]] = value


def check_nested_key_exists(data, keys):
    """
    Checks if a nested key exists in a dictionary using a list of keys to specify the path.

    Args:
        data (dict): The dictionary to check for the nested key.
        keys (list): A list of keys representing the path to the key to check for existence.

    Returns:
        bool: True if the nested key exists, False otherwise.

    Steps:
        1. Iterate through each key in the keys list.
        2. If the current data is None, return False.
        3. If the current key exists in the dictionary, update the data to the value associated with the key.
        4. Return True if all keys exist in the specified path, otherwise return False.
    """

    for key in keys:
        if data is None:
            return False
        if key in data:
            data = data[key]
        else:
            return False
    return True


class JSONFormat:
    documents_fields = CaseInsensitiveDict(
        {
            "RFA": {
                "docName": "",
                "docLink": "",
                "docType": "RFA",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "expeditedFlag": None,
                    "rushFlag": None,
                    "jurisdiction": "",
                    "senderName": "",
                    "newSubmission": None,
                    "resubmission": None,
                    "writtenConfirmPriorOralRequest": None,
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "physician": {
                        "type": "",
                        "npi": "",
                        "firstName": "",
                        "lastName": "",
                        "facilityName": "",
                        "keyContactName": "",
                        "specialty": "",
                        "credentials": "",
                        "emailAddress": "",
                        "phoneNumber": "",
                        "faxNumber": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                    },
                    "adjuster": {
                        "adjusterId": "",
                        "company": "",
                        "keyContact": "",
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "phoneExt": "",
                        "faxNumber": "",
                        "emailAddress": "",
                    },
                    "attorney": {
                        "type": "",
                        "company": "",
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "phoneExt": "",
                        "faxNumber": "",
                        "emailAddress": "",
                    },
                    "employer": {
                        "name": "",
                        "phoneNumber": "",
                        "faxNumber": "",
                        "contactEmail": "",
                        "taxId": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                    },
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                    "treatments": [],
                    # treatments dict sample:
                    # {
                    # "diagnosisCode": "",
                    # "diagnosisDescription": "",
                    # "treatmentServiceRequested": "",
                    # "procedureCode": "",
                    # "frequency": "",
                    # "duration": "",
                    # "quantity": "",
                    # "otherInformation": "",
                    # "medicationFlag": "",
                    # "requestOfRefill": "",
                    # "bodyPart": "",
                    # "treatmentCategory": ""
                    # }
                },
            },
            "Determination - Med Auth": {
                "docName": "",
                "docLink": "",
                "docType": "Determination - Med Auth",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                    "summary": "",
                },
            },
            "Case Management Notes": {
                "docName": "",
                "docLink": "",
                "docType": "Case Managment Notes",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                    "summary": "",
                },
            },
            "Medical Records": {
                "docName": "",
                "docLink": "",
                "docType": "Medical Records",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                    "physician": {
                        "npi": "",
                        "firstName": "",
                        "lastName": "",
                        "facilityName": "",
                        "keyContactName": "",
                        "specialty": "",
                        "credentials": "",
                        "emailAddress": "",
                        "phoneNumber": "",
                        "faxNumber": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                    },
                    "summary": "",
                },
            },
            "Fax": {
                "docName": "",
                "docLink": "",
                "docType": "Fax",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "subjectLine": "",
                    "body": "",
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                },
            },
            "IMR/IME/QME": {
                "docName": "",
                "docLink": "",
                "docType": "IMR/IME/QME",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                    "summary": "",
                },
            },
            "Injury/Illness/FROI": {
                "docName": "",
                "docLink": "",
                "docType": "Injury/Illness/FROI",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                    "summary": "",
                },
            },
            "Supplemental/Work Status": {
                "docName": "",
                "docLink": "",
                "docType": "Supplemental/Work Status",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                    "physician": {
                        "npi": "",
                        "firstName": "",
                        "lastName": "",
                        "facilityName": "",
                        "keyContactName": "",
                        "specialty": "",
                        "credentials": "",
                        "emailAddress": "",
                        "phoneNumber": "",
                        "faxNumber": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                    },
                    "summary": "",
                },
            },
            "Misc Correspondence": {
                "docName": "",
                "docLink": "",
                "docType": "Misc Correspondence",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                },
                "summary": "",
            },
            "Other": {
                "docName": "",
                "docLink": "",
                "docType": "Other",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "ssn": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "phoneNumber": "",
                        "emailAddress": "",
                        "employeeId": "",
                    },
                },
                "summary": "",
            },
            "Physician Bill (HCFA)": {
                "docName": "",
                "docLink": "",
                "docType": "Physician Bill (HCFA)",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "dateSubmitted": "",
                    "carrier": {
                        "carrierName": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "phoneNumber": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "relatedCondition": {
                            "employmentFlag": "",
                            "autoAccidentFlag": "",
                            "otherAccidentFlag": "",
                            "claimCodes": "",
                        },
                        "relationshipToInsured": "",
                        "signature": "",
                        "dateSigned": "",
                        "reservedForNUCCUse": "",
                        "additionalClaimInfo": "",
                    },
                    "insurance": {
                        "insuranceType": "",
                        "insuranceId": "",
                        "insurancePlanName": "",
                    },
                    "insured": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "dateOfBirth": "",
                        "gender": "",
                        "phoneNumber": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "policyGroup": "",
                        "otherClaimId": "",
                        "anotherHealthBenefitPlanFlag": "",
                        "signature": "",
                    },
                    "otherInsured": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "policyGroup": "",
                        "reservedNuccUseb": "",
                        "reservedNuccUsec": "",
                        "insurancePlanName": "",
                    },
                    "physician": [
                        {
                            "type": "referring",
                            "name": "",
                            "npi": "",
                            "otherId": "",
                            "credentials": "",
                        },
                        {
                            "type": "billing",
                            "name": "",
                            "npi": "",
                            "otherId": "",
                            "credentials": "",
                            "phoneNumber": "",
                            "address": {
                                "address1": "",
                                "address2": "",
                                "city": "",
                                "state": "",
                                "zip": "",
                            },
                        },
                    ],
                    "facility": {
                        "facilityName": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "npi": "",
                        "otherId": "",
                    },
                    "diagnosis": [{"icdInd": "", "diagnosisCode": ""}],
                    "reference": {"code": "", "originalRefNumber": ""},
                    "priorAuthNumber": "",
                    "federalTaxIdNo": "",
                    "federalTaxIdType": "",
                    "currentIllnessDate": {"date": "", "qual": ""},
                    "otherDate": {"date": "", "qual": ""},
                    "unableToWorkDates": {"from": "", "to": ""},
                    "hospitalizationDates": {"from": "", "to": ""},
                    "billing": [
                        {
                            "datesOfService": {"from": "", "to": ""},
                            "placeOfService": "",
                            "emergencyFlag": "",
                            "procedureCode": "",
                            "diagnosisPointer": "",
                            "modifier": "",
                            "charges": "",
                            "daysOrUnits": "",
                            "epsdtFamilyPlan": "",
                            "idQual": "",
                            "renderingProviderID": "",
                            "renderingProviderNpi": "",
                        }
                    ],
                    "outsideLab": {"outsideLabFlag": "", "charges": ""},
                    "totalChange": "",
                    "ammountPaid": "",
                    "receivedForNUCCUse": "",
                    "patientAccountNumber": "",
                    "acceptAssigmentFlag": "",
                    "physicianSignature": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "id": "",
                        "date": "",
                    },
                },
            },
            "Hospital Bill (UB)": {
                "docName": "",
                "docLink": "",
                "docType": "Hospital Bill (UB)",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "patientName": "",
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "suffix": "",
                        "dateOfBirth": "",
                        "gender": None,
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "relationshipToInsured": "",
                    },
                    "patientControlNumber": "",
                    "medicalRecordNumber": "",
                    "taxID": "",
                    "billType": "",
                    "administrativeNecessaryDays": "",
                    "statementCoverPeriod": {"from": "", "through": ""},
                    "physician": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "credentials": "",
                        "phoneNumber": "",
                        "npi": "",
                        "otherProviderIdentifier": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "physicianName": "",
                    },
                    "payToProvider": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "credentials": "",
                        "phoneNumber": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                        "payToProviderName": "",
                    },
                    "providers": [
                        {
                            "type": "Attending",
                            "firstName": "",
                            "lastName": "",
                            "npi": "",
                            "qual": "",
                        },
                        {
                            "type": "Operating",
                            "firstName": "",
                            "lastName": "",
                            "npi": "",
                            "qual": "",
                        },
                        {
                            "type": "Other",
                            "firstName": "",
                            "lastName": "",
                            "npi": "",
                            "qual": "",
                        },
                    ],
                    "admission": {"date": "", "hours": "", "type": "", "source": ""},
                    "discharge": {"hours": "", "status": ""},
                    "condition": {"conditionCodes": ""},
                    "accident": {"state": "", "date": ""},
                    "occurance": {"code": "", "date": ""},
                    "occuranceSpan": {"code": "", "from": "", "through": ""},
                    "responsibleParty": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                    },
                    "valueCodes": {"code": "", "amount": ""},
                    "insured": {
                        "insuredName": "",
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "address": {
                            "address1": "",
                            "address2": "",
                            "city": "",
                            "state": "",
                            "zip": "",
                        },
                    },
                    "insurance": {
                        "insuranceId": "",
                        "insuranceGroupName": "",
                        "insuranceGroupNumber": "",
                    },
                    "billing": {
                        "revenueCode": "",
                        "description": "",
                        "hcpc": "",
                        "serviceDate": "",
                        "serviceDayUnits": "",
                        "serviceUnitsNo": "",
                        "totalCharges": "",
                        "nonCoveredCharges": "",
                        "sumTotalCharges": "",
                        "sumNonCoveredCharges": "",
                    },
                    "payer": [{"payerName": "", "payerCarrierCode": ""}],
                    "creationDate": "",
                    "healthPlanId": "",
                    "releaseOfInformation": "",
                    "assignmentOfBenefits": "",
                    "priorPayments": "",
                    "estimatedAmountDue": "",
                    "treatmentAuthCodes": "",
                    "docControlNumber": "",
                    "diagnosis": {
                        "diagnosisCode": "",
                        "principalDiagnosisAdmission": "",
                        "otherDiagnosisCodes": "",
                        "admittingDiagnosisCode": "",
                        "patientReasonDiagnosis": "",
                        "ppsCode": "",
                        "externalCauseOfInjuryCode": "",
                    },
                    "principalProcedure": {"code": "", "date": ""},
                    "otherProcedure": [{"code": "", "date": ""}],
                    "employerName": "",
                    "remarks": "",
                    "codeCodeField": "",
                },
            },
        }
    )

    metadata_required_fileds = CaseInsensitiveDict(
        {
            "RFA": {
                "docDate": "",
                "expeditedFlag": None,
                "rushFlag": None,
                "jurisdiction": "",
                "senderName": "",
                "docState": "",
                "docReceivedDate": "",
                "physician": {
                    "type": "",
                    "firstName": "",
                    "lastName": "",
                    "specialty": "",
                    "phoneNumber": "",
                    "faxNumber": "",
                },
                "adjuster": {
                    "company": "",
                    "firstName": "",
                    "lastName": "",
                    "phoneNumber": "",
                    "phoneExt": "",
                    "faxNumber": "",
                },
                "employer": {"name": ""},
                "claim": {
                    "claimNumber": "",
                    "dateOfInjuryFrom": "",
                    "dateOfInjuryThrough": "",
                },
                "claimant": {
                    "firstName": "",
                    "lastName": "",
                    "claimantName": "",
                    "dateOfBirth": "",
                },
                "attorney": {
                    "type": "",
                    "company": "",
                    "firstName": "",
                    "lastName": "",
                },
                "treatments": [
                    {
                        "diagnosisCode": "",
                        "treatmentServiceRequested": "",
                        "procedureCode": "",
                        "medicationFlag": "",
                    }
                ],
            },
            "Determination - Med Auth": {
                "docReceivedDate": "",
                "docState": "",
                "senderName": "",
                "claim": {
                    "claimNumber": "",
                    "dateOfInjuryFrom": "",
                    "dateOfInjuryThrough": "",
                },
                "claimant": {},
                "docDate": "",
            },
            "Case Management Notes": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "claim": {
                    "claimNumber": "",
                    "dateOfInjuryFrom": "",
                    "dateOfInjuryThrough": "",
                },
                "claimant": {},
            },
            "Medical Records": {
                "docReceivedDate": "",
                "senderName": "",
                "claim": {
                    "claimNumber": "",
                    "dateOfInjuryFrom": "",
                    "dateOfInjuryThrough": "",
                },
                "claimant": {"dateOfBirth": ""},
                "physician": {
                    "firstName": "",
                    "lastName": "",
                    "facilityName": "",
                    "phoneNumber": "",
                    "faxNumber": "",
                },
            },
            "Fax Coversheet": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "claim": {"claimNumber": ""},
                "claimant": {
                    "dateOfBirth": "",
                },
            },
            "Fax": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "claim": {"claimNumber": ""},
                "claimant": {},
            },
            "IMR IME QME": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "docState": "",
                "claim": {
                    "claimNumber": "",
                    "dateOfInjuryFrom": "",
                    "dateOfInjuryThrough": "",
                },
                "claimant": {},
            },
            "IMR/IME/QME": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "docState": "",
                "claim": {
                    "claimNumber": "",
                    "dateOfInjuryFrom": "",
                    "dateOfInjuryThrough": "",
                },
                "claimant": {},
            },
            "Injury Illness FROI": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "docState": "",
                "claim": {"claimNumber": ""},
                "claimant": {},
            },
            "Injury/Illness/FROI": {
                "docDate": "",
                "docReceivedDate": "",
                "docState": "",
                "senderName": "",
                "claim": {"claimNumber": ""},
                "claimant": {},
            },
            "Supplemental Work Status": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "docState": "",
                "claim": {
                    "claimNumber": "",
                    "dateOfInjuryFrom": "",
                    "dateOfInjuryThrough": "",
                },
                "claimant": {},
                "physician": {"firstName": "", "lastName": "", "facilityName": ""},
            },
            "Supplemental/Work Status": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "docState": "",
                "claim": {
                    "claimNumber": "",
                    "dateOfInjuryFrom": "",
                    "dateOfInjuryThrough": "",
                },
                "claimant": {},
                "physician": {"firstName": "", "lastName": "", "facilityName": ""},
            },
            "Other": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "claim": {"claimNumber": ""},
                "claimant": {},
            },
            "Physician Bill (HCFA)": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "claim": {"claimNumber": ""},
                "claimant": {},
            },
            "Hospital Bill (UB)": {
                "docDate": "",
                "docReceivedDate": "",
                "senderName": "",
                "claim": {"claimNumber": ""},
                "claimant": {},
            },
        }
    )

    @classmethod
    def GetFieldsForDocument(self, document_type):
        try:
            return copy.deepcopy(self.documents_fields[document_type])
        except:
            print(
                "GetFieldsForDocument: unknown document_type: {document_type} -> return empty dictionary."
            )
            return {}

    @classmethod
    def GetRequiredFields(self, document_type):
        try:
            return copy.deepcopy(self.metadata_required_fileds[document_type])
        except:
            print(
                "GetRequiredFields: unknown document_type: {document_type} -> return empty dictionary."
            )
            return {}
