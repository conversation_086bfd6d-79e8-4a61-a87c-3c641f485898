def replace_last(string, delimiter=",", replacement=" "):
    # replace the last occurrence of comma with the a space
    start, found_sep, end = string.rpartition(delimiter)
    if found_sep:
        return start + replacement + end
    return start + end

def perform_name_splitting(entity_name, entity):
    if not entity_name:
        return {
            f"{entity}FirstName": "",
            f"{entity}MiddleName": "",
            f"{entity}LastName": "",
        }

    entity_name = entity_name.strip()

    if entity_name.count(",") > 1:
        # if there are more than one comma, then replace the last comma with a space
        entity_name = replace_last(entity_name)

    if len(entity_name.split()) == 1:
        # if there is only one word in the patient name, then return the name as it is
        return {
            f"{entity}FirstName": entity_name,
            f"{entity}MiddleName": "",
            f"{entity}LastName": "",
        }

    try:
        # check for "last_name, first_name" format
        if "," in entity_name:

            # split the full name by the comma
            last_name, first_name = entity_name.split(",")

            # strip the possible first name and last name
            first_name = first_name.strip()
            last_name = last_name.strip()

            # extract the middle name (if present)
            name_parts = first_name.split()

            if len(name_parts) > 1:
                # if there are more than one parts in the first name, then the middle name is the second part
                middle_name = " ".join(name_parts[1:])
                first_name = name_parts[0]
            else:
                middle_name = ""
        else:
            # if no comma is present, then split the name by spaces
            name_parts = entity_name.split()

            first_name = name_parts[0]
            last_name = name_parts[-1]
            middle_name = " ".join(name_parts[1:-1])

        return {
            f"{entity}FirstName": first_name,
            f"{entity}MiddleName": middle_name,
            f"{entity}LastName": last_name,
        }
    except Exception as e:
        return {
            f"{entity}FirstName": "",
            f"{entity}MiddleName": "",
            f"{entity}LastName": "",
        }
