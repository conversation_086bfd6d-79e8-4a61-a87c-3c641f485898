name: Terra<PERSON>

# Required secrets to work: TF_API_TOKEN, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY

env:
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  TF_API_TOKEN: ${{ secrets.TF_API_TOKEN }}
  TF_VERSION: 1.11.1
  AWS_DEFAULT_REGION: us-east-2

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: 'Terraform Environment'
        required: true
        options:
          - dev
          - build
          - stage
          - master
          - prod
      job_to_run:
        type: choice
        description: 'Select which job to run'
        required: true
        options:
          - plan
          - apply
  push:
    branches:
      - "main"
  pull_request:
    branches:
      - "main"
    types: [opened, synchronize]

jobs:
  ######################################
  # 1) Pull Request Plan for ALL ENVs  #
  ######################################
  pull_request_plan:
    name: Pull request plan
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'pull_request' }}
    strategy:
      matrix:
        TERRAFORM_ENV: [dev, build, stage, master, prod]
    steps:
      - name: Check out
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}
          cli_config_credentials_token: ${{ env.TF_API_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Terraform init (PR)
        working-directory: envs/${{ matrix.TERRAFORM_ENV }}
        run: terraform init -backend-config=${{ matrix.TERRAFORM_ENV }}.tfcloud.tfbackend -input=false

      - name: Terraform Plan (PR)
        working-directory: envs/${{ matrix.TERRAFORM_ENV }}
        run: terraform plan -var-file=${{ matrix.TERRAFORM_ENV }}.auto.tfvars -input=false

  #########################################
  # 2) Manual Plan for ONE chosen ENV     #
  #########################################
  plan:
    name: Manual plan
    runs-on: ubuntu-latest
    if: ${{ (github.event_name == 'workflow_dispatch' && github.event.inputs.job_to_run == 'plan') }}
    steps:
      - name: Check out
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}
          cli_config_credentials_token: ${{ env.TF_API_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Terraform init
        working-directory: envs/${{ github.event.inputs.environment }}
        run: terraform init -backend-config=${{ github.event.inputs.environment }}.tfcloud.tfbackend -input=false

      - name: Terraform Plan
        working-directory: envs/${{ github.event.inputs.environment }}
        run: terraform plan -var-file=${{ github.event.inputs.environment }}.auto.tfvars -input=false

  ##########################################
  # 3) Manual Apply for ONE chosen ENV     #
  ##########################################
  apply:
    name: Manual apply
    runs-on: ubuntu-latest
    if: ${{ (github.event_name == 'workflow_dispatch' && github.event.inputs.job_to_run == 'apply') }}
    steps:
      - name: Check out
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}
          cli_config_credentials_token: ${{ env.TF_API_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Terraform init
        working-directory: envs/${{ github.event.inputs.environment }}
        run: terraform init -backend-config=${{ github.event.inputs.environment }}.tfcloud.tfbackend -input=false

      - name: Terraform Apply
        working-directory: envs/${{ github.event.inputs.environment }}
        run: terraform apply -var-file=${{ github.event.inputs.environment }}.auto.tfvars -auto-approve -input=false

  ###################################################
  # 4) On-push Auto-Apply for ONLY 'dev' (example)  #
  ###################################################
  on_push_auto_apply:
    name: Auto-Apply on Push
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'push' }}
    strategy:
      matrix:
        # Adjust if you want more environments automatically deployed on push
        TERRAFORM_ENV: [build, dev]
    steps:
      - name: Check out
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}
          cli_config_credentials_token: ${{ env.TF_API_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Terraform init (push)
        working-directory: envs/${{ matrix.TERRAFORM_ENV }}
        run: terraform init -backend-config=${{ matrix.TERRAFORM_ENV }}.tfcloud.tfbackend -input=false

      - name: Terraform Apply (push)
        working-directory: envs/${{ matrix.TERRAFORM_ENV }}
        run: terraform apply -var-file=${{ matrix.TERRAFORM_ENV }}.auto.tfvars -auto-approve -input=false
