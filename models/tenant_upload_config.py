"""
Tenant Upload Configuration Models
Defines database models for storing tenant-specific upload configurations.
"""

import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Any, Optional

from sqlalchemy import Column, String, DateTime, JSON, Boolean, ForeignKey, Text, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class UploadChannelType(Enum):
    """Supported upload channel types"""
    SFTP = "sftp"
    SERVICEBUS_QUEUE = "servicebus_queue"
    SERVICEBUS_TOPIC = "servicebus_topic"
    API_S3 = "api_s3"
    API_REST = "api_rest"


class TenantUploadConfiguration(Base):
    """
    Model for storing tenant-specific upload configurations.
    Each tenant can have multiple upload configurations for different channels.
    """
    __tablename__ = 'tenant_upload_configurations'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    tenant_id = Column(String(128), ForeignKey('tenants.tenant_id'), nullable=False)
    subtenant_id = Column(String(128), nullable=True)
    
    # Configuration details
    configuration_name = Column(String(256), nullable=False)
    channel_type = Column(String(50), nullable=False)  # UploadChannelType enum value
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)  # Default configuration for this tenant
    
    # Channel-specific configuration stored as JSON
    channel_config = Column(JSON, nullable=False)
    
    # Metadata
    description = Column(Text, nullable=True)
    created_date = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_date = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    created_by = Column(String(128), nullable=True)
    updated_by = Column(String(128), nullable=True)
    
    # Relationships - commented out to avoid circular import issues
    # tenant = relationship("Tenant", back_populates="upload_configurations")
    
    # Indexes for better performance
    __table_args__ = (
        Index('idx_tenant_upload_config_tenant', 'tenant_id', 'subtenant_id'),
        Index('idx_tenant_upload_config_active', 'is_active'),
        Index('idx_tenant_upload_config_channel', 'channel_type'),
        Index('idx_tenant_upload_config_default', 'tenant_id', 'subtenant_id', 'is_default'),
    )


class UploadConfigurationTemplate(Base):
    """
    Model for storing reusable upload configuration templates.
    """
    __tablename__ = 'upload_configuration_templates'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    template_name = Column(String(256), nullable=False, unique=True)
    template_description = Column(Text, nullable=True)
    channel_type = Column(String(50), nullable=False)
    
    # Template configuration
    template_config = Column(JSON, nullable=False)
    
    # Metadata
    is_active = Column(Boolean, default=True)
    created_date = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_date = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    created_by = Column(String(128), nullable=True)
    
    __table_args__ = (
        Index('idx_upload_template_active', 'is_active'),
        Index('idx_upload_template_channel', 'channel_type'),
    )


# Configuration schemas for different channel types
class SFTPConfig:
    """Configuration schema for SFTP uploads"""
    
    def __init__(self, config: Dict[str, Any]):
        self.host = config.get('host', '')
        self.port = config.get('port', 22)
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.private_key_path = config.get('private_key_path', '')
        self.upload_path = config.get('upload_path', 'Documents')
        self.folder_structure = config.get('folder_structure', '{tenant_id}/{subtenant_id}/{date}/{transaction_id}')
        self.file_naming_convention = config.get('file_naming_convention', '{claim_number}_{sender_name}_{document_type}_{patient_name}')
        self.sort_by_date = config.get('sort_by_date', False)
        self.create_metadata_file = config.get('create_metadata_file', True)
        self.metadata_filename = config.get('metadata_filename', 'index.json')
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'host': self.host,
            'port': self.port,
            'username': self.username,
            'password': self.password,
            'private_key_path': self.private_key_path,
            'upload_path': self.upload_path,
            'folder_structure': self.folder_structure,
            'file_naming_convention': self.file_naming_convention,
            'sort_by_date': self.sort_by_date,
            'create_metadata_file': self.create_metadata_file,
            'metadata_filename': self.metadata_filename
        }


class ServiceBusConfig:
    """Configuration schema for Azure Service Bus uploads"""
    
    def __init__(self, config: Dict[str, Any]):
        self.connection_string = config.get('connection_string', '')
        self.topic_name = config.get('topic_name', '')
        self.queue_name = config.get('queue_name', '')
        self.blob_storage_connection_string = config.get('blob_storage_connection_string', '')
        self.blob_container_name = config.get('blob_container_name', '')
        self.message_format = config.get('message_format', 'json')
        self.include_blob_urls = config.get('include_blob_urls', True)
        self.sas_token_expiry_days = config.get('sas_token_expiry_days', 14)
        self.folder_structure = config.get('folder_structure', '{tenant_id}/{subtenant_id}/{date}/{transaction_id}')
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'connection_string': self.connection_string,
            'topic_name': self.topic_name,
            'queue_name': self.queue_name,
            'blob_storage_connection_string': self.blob_storage_connection_string,
            'blob_container_name': self.blob_container_name,
            'message_format': self.message_format,
            'include_blob_urls': self.include_blob_urls,
            'sas_token_expiry_days': self.sas_token_expiry_days,
            'folder_structure': self.folder_structure
        }


class APIS3Config:
    """Configuration schema for API with S3 uploads"""
    
    def __init__(self, config: Dict[str, Any]):
        self.api_endpoint = config.get('api_endpoint', '')
        self.api_key = config.get('api_key', '')
        self.s3_bucket_name = config.get('s3_bucket_name', '')
        self.s3_access_key = config.get('s3_access_key', '')
        self.s3_secret_key = config.get('s3_secret_key', '')
        self.s3_region = config.get('s3_region', 'us-east-1')
        self.s3_endpoint_url = config.get('s3_endpoint_url', '')
        self.folder_structure = config.get('folder_structure', '{tenant_id}/{subtenant_id}/{date}/{transaction_id}')
        self.upload_method = config.get('upload_method', 'multipart')  # multipart, presigned_url
        self.include_metadata = config.get('include_metadata', True)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'api_endpoint': self.api_endpoint,
            'api_key': self.api_key,
            's3_bucket_name': self.s3_bucket_name,
            's3_access_key': self.s3_access_key,
            's3_secret_key': self.s3_secret_key,
            's3_region': self.s3_region,
            's3_endpoint_url': self.s3_endpoint_url,
            'folder_structure': self.folder_structure,
            'upload_method': self.upload_method,
            'include_metadata': self.include_metadata
        }


class APIRestConfig:
    """Configuration schema for REST API uploads"""
    
    def __init__(self, config: Dict[str, Any]):
        self.api_endpoint = config.get('api_endpoint', '')
        self.api_key = config.get('api_key', '')
        self.auth_method = config.get('auth_method', 'bearer')  # bearer, basic, custom
        self.content_type = config.get('content_type', 'multipart/form-data')
        self.upload_method = config.get('upload_method', 'multipart')  # multipart, json
        self.include_metadata = config.get('include_metadata', True)
        self.retry_attempts = config.get('retry_attempts', 3)
        self.timeout_seconds = config.get('timeout_seconds', 30)
        self.custom_headers = config.get('custom_headers', {})
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'api_endpoint': self.api_endpoint,
            'api_key': self.api_key,
            'auth_method': self.auth_method,
            'content_type': self.content_type,
            'upload_method': self.upload_method,
            'include_metadata': self.include_metadata,
            'retry_attempts': self.retry_attempts,
            'timeout_seconds': self.timeout_seconds,
            'custom_headers': self.custom_headers
        }


def get_config_class(channel_type: str):
    """Get the appropriate configuration class for a channel type"""
    config_classes = {
        UploadChannelType.SFTP.value: SFTPConfig,
        UploadChannelType.SERVICEBUS_QUEUE.value: ServiceBusConfig,
        UploadChannelType.SERVICEBUS_TOPIC.value: ServiceBusConfig,
        UploadChannelType.API_S3.value: APIS3Config,
        UploadChannelType.API_REST.value: APIRestConfig,
    }
    return config_classes.get(channel_type)


def validate_configuration(channel_type: str, config: Dict[str, Any]) -> tuple[bool, list[str]]:
    """
    Validate configuration for a specific channel type.
    
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    if channel_type == UploadChannelType.SFTP.value:
        if not config.get('host'):
            errors.append("SFTP host is required")
        if not config.get('username'):
            errors.append("SFTP username is required")
        if not config.get('password') and not config.get('private_key_path'):
            errors.append("Either SFTP password or private key path is required")
    
    elif channel_type in [UploadChannelType.SERVICEBUS_QUEUE.value, UploadChannelType.SERVICEBUS_TOPIC.value]:
        if not config.get('connection_string'):
            errors.append("Service Bus connection string is required")
        if channel_type == UploadChannelType.SERVICEBUS_TOPIC.value and not config.get('topic_name'):
            errors.append("Service Bus topic name is required")
        if channel_type == UploadChannelType.SERVICEBUS_QUEUE.value and not config.get('queue_name'):
            errors.append("Service Bus queue name is required")
    
    elif channel_type == UploadChannelType.API_S3.value:
        if not config.get('api_endpoint'):
            errors.append("API endpoint is required")
        if not config.get('s3_bucket_name'):
            errors.append("S3 bucket name is required")
    
    elif channel_type == UploadChannelType.API_REST.value:
        if not config.get('api_endpoint'):
            errors.append("API endpoint is required")
    
    else:
        errors.append(f"Unsupported channel type: {channel_type}")
    
    return len(errors) == 0, errors 