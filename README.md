# Flux Configuration Repository

This repository contains the Flux configuration for deploying applications across different environments.

## Environment Scaling Script

This project includes a Python script, `scale.py`, to automate the scaling of replicas for all deployments within a specified environment. This is useful for spinning down non-production environments to conserve resources.

### Prerequisites

- Python 3.x
- A Python virtual environment is recommended.

### Setup

1.  **Create and activate a virtual environment:**
    ```bash
    python3 -m venv venv
    source venv/bin/activate
    ```

2.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

### Usage

The script takes two arguments: an action (`up` or `down`) and an environment name.

**Currently, only the `stage-training` environment is supported.**

-   **To scale up (set replicas to 1):**
    ```bash
    python scale.py up stage-training
    ```

-   **To scale down (set replicas to 0):**
    ```bash
    python scale.py down stage-training
    ```

The script will first show which files it intends to modify and ask for your confirmation. After updating the files, it will ask for a second confirmation before committing and pushing the changes to the `main` branch. 