"""
Tenant Upload Configuration Manager
Manages tenant-specific upload configurations and provides upload services.
"""

import json
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import text

from models.tenant_upload_config import (
    TenantUploadConfiguration, 
    UploadChannelType, 
    validate_configuration
)
from uploader.upload_services import create_upload_service, UploadService

logger = logging.getLogger(__name__)


class TenantUploadManager:
    """
    Manages tenant-specific upload configurations and provides upload services.
    """
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        self._upload_services_cache: Dict[str, UploadService] = {}
    
    def get_tenant_upload_config(
        self, 
        tenant_id: str, 
        subtenant_id: Optional[str] = None,
        channel_type: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get upload configuration for a tenant/subtenant combination.
        
        Args:
            tenant_id: The tenant ID
            subtenant_id: The subtenant ID (optional)
            channel_type: Specific channel type to filter by (optional)
        
        Returns:
            Upload configuration dictionary or None if not found
        """
        try:
            print(f"🔍 Searching for upload config: tenant_id={tenant_id}, subtenant_id={subtenant_id}, channel_type={channel_type}")
            
            query = self.db_session.query(TenantUploadConfiguration).filter(
                TenantUploadConfiguration.tenant_id == tenant_id,
                TenantUploadConfiguration.is_active == True
            )
            
            if subtenant_id:
                query = query.filter(TenantUploadConfiguration.subtenant_id == subtenant_id)
                print(f"🔍 Filtering by subtenant_id: {subtenant_id}")
            else:
                query = query.filter(TenantUploadConfiguration.subtenant_id.is_(None))
                print(f"🔍 Filtering for NULL subtenant_id")
            
            if channel_type:
                query = query.filter(TenantUploadConfiguration.channel_type == channel_type)
                print(f"🔍 Filtering by channel_type: {channel_type}")
            
            # Get default configuration first, then any other active configuration
            config = query.filter(TenantUploadConfiguration.is_default == True).first()
            if config:
                print(f"✅ Found default configuration: {config.configuration_name}")
            else:
                print(f"🔍 No default configuration found, looking for any active config...")
                config = query.first()
                if config:
                    print(f"✅ Found active configuration: {config.configuration_name}")
                else:
                    print(f"❌ No active configuration found")
            
            if config:
                return {
                    'id': config.id,
                    'tenant_id': config.tenant_id,
                    'subtenant_id': config.subtenant_id,
                    'configuration_name': config.configuration_name,
                    'channel_type': config.channel_type,
                    'channel_config': config.channel_config,
                    'description': config.description,
                    'is_default': config.is_default
                }
            
            return None
            
        except Exception as e:
            print(f"❌ Error getting upload config for tenant {tenant_id}: {e}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            return None
    
    def get_upload_service(
        self, 
        tenant_id: str, 
        subtenant_id: Optional[str] = None,
        channel_type: Optional[str] = None
    ) -> Optional[UploadService]:
        """
        Get upload service for a tenant/subtenant combination.
        
        Args:
            tenant_id: The tenant ID
            subtenant_id: The subtenant ID (optional)
            channel_type: Specific channel type to filter by (optional)
        
        Returns:
            UploadService instance or None if not found
        """
        cache_key = f"{tenant_id}:{subtenant_id}:{channel_type}"
        print(f"🔍 Looking for upload service with cache key: {cache_key}")
        
        # Check cache first
        if cache_key in self._upload_services_cache:
            print(f"✅ Found cached upload service for {cache_key}")
            return self._upload_services_cache[cache_key]
        
        print(f"🔍 Cache miss, getting configuration...")
        # Get configuration
        config = self.get_tenant_upload_config(tenant_id, subtenant_id, channel_type)
        if not config:
            print(f"❌ No upload configuration found for tenant {tenant_id}, subtenant {subtenant_id}")
            return None
        
        try:
            print(f"🔍 Validating configuration for channel type: {config['channel_type']}")
            # Validate configuration
            is_valid, errors = validate_configuration(
                config['channel_type'], 
                config['channel_config']
            )
            
            if not is_valid:
                print(f"❌ Invalid upload configuration for tenant {tenant_id}: {errors}")
                return None
            
            print(f"✅ Configuration validation passed")
            print(f"🔍 Creating upload service for channel type: {config['channel_type']}")
            # Create upload service
            upload_service = create_upload_service(
                config['channel_type'], 
                config['channel_config']
            )
            
            # Cache the service
            self._upload_services_cache[cache_key] = upload_service
            
            print(f"✅ Created upload service for tenant {tenant_id}, subtenant {subtenant_id}, channel {config['channel_type']}")
            return upload_service
            
        except Exception as e:
            print(f"❌ Error creating upload service for tenant {tenant_id}: {e}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            return None
    
    def set_tenant_upload_config(
        self,
        tenant_id: str,
        channel_type: str,
        channel_config: Dict[str, Any],
        subtenant_id: Optional[str] = None,
        configuration_name: Optional[str] = None,
        description: Optional[str] = None,
        is_default: bool = False,
        created_by: Optional[str] = None
    ) -> Tuple[bool, List[str]]:
        """
        Set upload configuration for a tenant/subtenant.
        
        Args:
            tenant_id: The tenant ID
            channel_type: The upload channel type
            channel_config: The channel-specific configuration
            subtenant_id: The subtenant ID (optional)
            configuration_name: Name for this configuration (optional)
            description: Description for this configuration (optional)
            is_default: Whether this is the default configuration (optional)
            created_by: User who created this configuration (optional)
        
        Returns:
            Tuple of (success, list_of_errors)
        """
        try:
            # Validate configuration
            is_valid, errors = validate_configuration(channel_type, channel_config)
            if not is_valid:
                return False, errors
            
            # If this is a default configuration, unset other defaults for this tenant/channel
            if is_default:
                self.db_session.query(TenantUploadConfiguration).filter(
                    TenantUploadConfiguration.tenant_id == tenant_id,
                    TenantUploadConfiguration.subtenant_id == subtenant_id,
                    TenantUploadConfiguration.channel_type == channel_type,
                    TenantUploadConfiguration.is_default == True
                ).update({'is_default': False})
            
            # Create new configuration
            config = TenantUploadConfiguration(
                tenant_id=tenant_id,
                subtenant_id=subtenant_id,
                configuration_name=configuration_name or f"{channel_type}_config",
                channel_type=channel_type,
                channel_config=channel_config,
                description=description,
                is_default=is_default,
                created_by=created_by,
                updated_by=created_by
            )
            
            self.db_session.add(config)
            self.db_session.commit()
            
            # Clear cache for this tenant
            self._clear_cache_for_tenant(tenant_id, subtenant_id)
            
            logger.info(f"Created upload configuration for tenant {tenant_id}, subtenant {subtenant_id}, channel {channel_type}")
            return True, []
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Error setting upload config for tenant {tenant_id}: {e}")
            return False, [f"Database error: {str(e)}"]
    
    def update_tenant_upload_config(
        self,
        config_id: str,
        channel_config: Dict[str, Any],
        description: Optional[str] = None,
        is_default: Optional[bool] = None,
        updated_by: Optional[str] = None
    ) -> Tuple[bool, List[str]]:
        """
        Update existing upload configuration.
        
        Args:
            config_id: The configuration ID to update
            channel_config: The updated channel configuration
            description: Updated description (optional)
            is_default: Whether this should be the default (optional)
            updated_by: User who updated this configuration (optional)
        
        Returns:
            Tuple of (success, list_of_errors)
        """
        try:
            # Get existing configuration
            config = self.db_session.query(TenantUploadConfiguration).filter(
                TenantUploadConfiguration.id == config_id
            ).first()
            
            if not config:
                return False, ["Configuration not found"]
            
            # Validate new configuration
            is_valid, errors = validate_configuration(config.channel_type, channel_config)
            if not is_valid:
                return False, errors
            
            # If setting as default, unset other defaults for this tenant/channel
            if is_default and not config.is_default:
                self.db_session.query(TenantUploadConfiguration).filter(
                    TenantUploadConfiguration.tenant_id == config.tenant_id,
                    TenantUploadConfiguration.subtenant_id == config.subtenant_id,
                    TenantUploadConfiguration.channel_type == config.channel_type,
                    TenantUploadConfiguration.is_default == True,
                    TenantUploadConfiguration.id != config_id
                ).update({'is_default': False})
            
            # Update configuration
            config.channel_config = channel_config
            config.updated_date = datetime.now(timezone.utc)
            config.updated_by = updated_by
            
            if description is not None:
                config.description = description
            
            if is_default is not None:
                config.is_default = is_default
            
            self.db_session.commit()
            
            # Clear cache for this tenant
            self._clear_cache_for_tenant(config.tenant_id, config.subtenant_id)
            
            logger.info(f"Updated upload configuration {config_id}")
            return True, []
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Error updating upload config {config_id}: {e}")
            return False, [f"Database error: {str(e)}"]
    
    def delete_tenant_upload_config(
        self,
        config_id: str,
        deleted_by: Optional[str] = None
    ) -> bool:
        """
        Delete upload configuration (soft delete).
        
        Args:
            config_id: The configuration ID to delete
            deleted_by: User who deleted this configuration (optional)
        
        Returns:
            True if successful, False otherwise
        """
        try:
            config = self.db_session.query(TenantUploadConfiguration).filter(
                TenantUploadConfiguration.id == config_id
            ).first()
            
            if not config:
                logger.warning(f"Upload configuration {config_id} not found for deletion")
                return False
            
            # Soft delete
            config.is_active = False
            config.updated_date = datetime.now(timezone.utc)
            config.updated_by = deleted_by
            
            self.db_session.commit()
            
            # Clear cache for this tenant
            self._clear_cache_for_tenant(config.tenant_id, config.subtenant_id)
            
            logger.info(f"Deleted upload configuration {config_id}")
            return True
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Error deleting upload config {config_id}: {e}")
            return False
    
    def list_tenant_upload_configs(
        self,
        tenant_id: str,
        subtenant_id: Optional[str] = None,
        channel_type: Optional[str] = None,
        active_only: bool = True
    ) -> List[Dict[str, Any]]:
        """
        List upload configurations for a tenant.
        
        Args:
            tenant_id: The tenant ID
            subtenant_id: The subtenant ID (optional)
            channel_type: Filter by channel type (optional)
            active_only: Only return active configurations (optional)
        
        Returns:
            List of configuration dictionaries
        """
        try:
            query = self.db_session.query(TenantUploadConfiguration).filter(
                TenantUploadConfiguration.tenant_id == tenant_id
            )
            
            if subtenant_id:
                query = query.filter(TenantUploadConfiguration.subtenant_id == subtenant_id)
            else:
                query = query.filter(TenantUploadConfiguration.subtenant_id.is_(None))
            
            if channel_type:
                query = query.filter(TenantUploadConfiguration.channel_type == channel_type)
            
            if active_only:
                query = query.filter(TenantUploadConfiguration.is_active == True)
            
            configs = query.order_by(
                TenantUploadConfiguration.is_default.desc(),
                TenantUploadConfiguration.created_date.desc()
            ).all()
            
            return [
                {
                    'id': config.id,
                    'tenant_id': config.tenant_id,
                    'subtenant_id': config.subtenant_id,
                    'configuration_name': config.configuration_name,
                    'channel_type': config.channel_type,
                    'channel_config': config.channel_config,
                    'description': config.description,
                    'is_default': config.is_default,
                    'is_active': config.is_active,
                    'created_date': config.created_date.isoformat() if config.created_date else None,
                    'updated_date': config.updated_date.isoformat() if config.updated_date else None,
                    'created_by': config.created_by,
                    'updated_by': config.updated_by
                }
                for config in configs
            ]
            
        except Exception as e:
            logger.error(f"Error listing upload configs for tenant {tenant_id}: {e}")
            return []
    
    def _clear_cache_for_tenant(self, tenant_id: str, subtenant_id: Optional[str] = None):
        """Clear upload service cache for a specific tenant"""
        keys_to_remove = []
        for key in self._upload_services_cache.keys():
            if key.startswith(f"{tenant_id}:{subtenant_id}:"):
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self._upload_services_cache[key]
    
    def clear_cache(self):
        """Clear all cached upload services"""
        self._upload_services_cache.clear()
        logger.info("Cleared upload service cache") 