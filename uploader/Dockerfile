FROM 112623991000.dkr.ecr.us-east-2.amazonaws.com/python:3.10.15

RUN apt-get update && apt-get install -y poppler-utils

WORKDIR /app

COPY uploader/requirements.txt /app/requirements.txt
COPY models /app/models

RUN python3 -m pip install -r requirements.txt

COPY uploader/uploader.py /app/uploader.py
COPY uploader/tenant_upload_manager.py /app/tenant_upload_manager.py
COPY uploader/upload_services.py /app/upload_services.py
COPY pipeline_utils/ /app/pipeline_utils/

# Start the application
CMD ["python3", "-u", "uploader.py"]
