#!/usr/bin/env python3
"""
Tenant Upload Configuration Management Script
Command-line utility for managing tenant-specific upload configurations.
"""

import argparse
import json
import sys
import os
from typing import Dict, Any, Optional

# Add parent directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from pipeline_utils.database_connector import DBConnector
from uploader.tenant_upload_manager import TenantUploadManager
from models.tenant_upload_config import UploadChannelType, validate_configuration


def get_db_session():
    """Get database session"""
    db_connector = DBConnector(
        pgsql_host=os.environ.get('PGSQL_HOST', 'localhost'),
        pgsql_port=int(os.environ.get('PGSQL_PORT', '5438')),
        pgsql_username=os.environ.get('PGSQL_USERNAME', ''),
        pgsql_password=os.environ.get('PGSQL_PASSWORD', ''),
        pgsql_db_name=os.environ.get('PGSQL_DB_NAME', '')
    )
    return db_connector.get_session()


def list_configs(args):
    """List upload configurations for a tenant"""
    session = get_db_session()
    try:
        manager = TenantUploadManager(session)
        configs = manager.list_tenant_upload_configs(
            tenant_id=args.tenant_id,
            subtenant_id=args.subtenant_id,
            channel_type=args.channel_type,
            active_only=not args.show_inactive
        )
        
        if not configs:
            print(f"No configurations found for tenant {args.tenant_id}")
            return
        
        print(f"\nUpload configurations for tenant: {args.tenant_id}")
        if args.subtenant_id:
            print(f"Subtenant: {args.subtenant_id}")
        print("-" * 80)
        
        for config in configs:
            status = "ACTIVE" if config['is_active'] else "INACTIVE"
            default = " (DEFAULT)" if config['is_default'] else ""
            print(f"ID: {config['id']}")
            print(f"Name: {config['configuration_name']}{default}")
            print(f"Channel: {config['channel_type']}")
            print(f"Status: {status}")
            print(f"Description: {config['description'] or 'No description'}")
            print(f"Created: {config['created_date']}")
            print(f"Updated: {config['updated_date']}")
            print("-" * 40)
    except Exception as e:
        print(f"Error listing configurations: {e}")
        sys.exit(1)


def show_config(args):
    """Show detailed configuration"""
    session = get_db_session()
    try:
        manager = TenantUploadManager(session)
        config = manager.get_tenant_upload_config(
            tenant_id=args.tenant_id,
            subtenant_id=args.subtenant_id,
            channel_type=args.channel_type
        )
        
        if not config:
            print(f"No configuration found for tenant {args.tenant_id}")
            return
        
        print(f"\nConfiguration for tenant: {args.tenant_id}")
        if args.subtenant_id:
            print(f"Subtenant: {args.subtenant_id}")
        print("-" * 80)
        print(json.dumps(config, indent=2))
    except Exception as e:
        print(f"Error showing configuration: {e}")
        sys.exit(1)


def add_config(args):
    """Add new upload configuration"""
    session = get_db_session()
    try:
        # Load configuration from file if provided
        if args.config_file:
            with open(args.config_file, 'r') as f:
                channel_config = json.load(f)
        else:
            # Create basic configuration based on channel type
            channel_config = create_basic_config(args.channel_type)
        
        manager = TenantUploadManager(session)
        success, errors = manager.set_tenant_upload_config(
            tenant_id=args.tenant_id,
            channel_type=args.channel_type,
            channel_config=channel_config,
            subtenant_id=args.subtenant_id,
            configuration_name=args.name,
            description=args.description,
            is_default=args.default,
            created_by=args.created_by
        )
        
        if success:
            print(f"Successfully created upload configuration for tenant {args.tenant_id}")
        else:
            print(f"Failed to create configuration: {errors}")
            sys.exit(1)
    except Exception as e:
        print(f"Error adding configuration: {e}")
        sys.exit(1)


def update_config(args):
    """Update existing upload configuration"""
    session = get_db_session()
    try:
        # Load configuration from file if provided
        if args.config_file:
            with open(args.config_file, 'r') as f:
                channel_config = json.load(f)
        else:
            channel_config = None
        
        manager = TenantUploadManager(session)
        success, errors = manager.update_tenant_upload_config(
            config_id=args.config_id,
            channel_config=channel_config,
            description=args.description,
            is_default=args.default,
            updated_by=args.updated_by
        )
        
        if success:
            print(f"Successfully updated upload configuration {args.config_id}")
        else:
            print(f"Failed to update configuration: {errors}")
            sys.exit(1)
    except Exception as e:
        print(f"Error updating configuration: {e}")
        sys.exit(1)


def delete_config(args):
    """Delete upload configuration"""
    session = get_db_session()
    try:
        manager = TenantUploadManager(session)
        success = manager.delete_tenant_upload_config(
            config_id=args.config_id,
            deleted_by=args.deleted_by
        )
        
        if success:
            print(f"Successfully deleted upload configuration {args.config_id}")
        else:
            print(f"Failed to delete configuration {args.config_id}")
            sys.exit(1)
    except Exception as e:
        print(f"Error deleting configuration: {e}")
        sys.exit(1)


def create_example_config(args):
    """Create example configuration file"""
    config = create_basic_config(args.channel_type)
    
    if args.output_file:
        with open(args.output_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"Example configuration written to {args.output_file}")
    else:
        print(json.dumps(config, indent=2))


def create_basic_config(channel_type: str) -> Dict[str, Any]:
    """Create basic configuration for a channel type"""
    if channel_type == 'sftp':
        return {
            "host": "sftp.example.com",
            "port": 22,
            "username": "upload_user",
            "password": "secure_password",
            "upload_path": "Documents",
            "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}",
            "file_naming_convention": "{claim_number}_{sender_name}_{document_type}_{patient_name}",
            "sort_by_date": False,
            "create_metadata_file": True,
            "metadata_filename": "index.json"
        }
    elif channel_type in ['servicebus_topic', 'servicebus_queue']:
        return {
            "connection_string": "Endpoint=sb://example.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your_key_here",
            "topic_name": "document-uploads" if channel_type == 'servicebus_topic' else None,
            "queue_name": "document-uploads" if channel_type == 'servicebus_queue' else None,
            "blob_storage_connection_string": "DefaultEndpointsProtocol=https;AccountName=example;AccountKey=your_key_here;EndpointSuffix=core.windows.net",
            "blob_container_name": "documents",
            "message_format": "json",
            "include_blob_urls": True,
            "sas_token_expiry_days": 14,
            "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}"
        }
    elif channel_type == 'api_s3':
        return {
            "api_endpoint": "https://api.example.com/upload",
            "api_key": "your_api_key_here",
            "s3_bucket_name": "example-documents",
            "s3_access_key": "your_s3_access_key",
            "s3_secret_key": "your_s3_secret_key",
            "s3_region": "us-east-1",
            "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}",
            "upload_method": "multipart",
            "include_metadata": True
        }
    elif channel_type == 'api_rest':
        return {
            "api_endpoint": "https://api.example.com/upload",
            "api_key": "your_api_key_here",
            "auth_method": "bearer",
            "content_type": "multipart/form-data",
            "upload_method": "multipart",
            "include_metadata": True,
            "retry_attempts": 3,
            "timeout_seconds": 30,
            "custom_headers": {}
        }
    else:
        raise ValueError(f"Unsupported channel type: {channel_type}")


def validate_config_file(args):
    """Validate configuration file"""
    try:
        with open(args.config_file, 'r') as f:
            config = json.load(f)
        
        is_valid, errors = validate_configuration(args.channel_type, config)
        
        if is_valid:
            print(f"Configuration file {args.config_file} is valid for channel type {args.channel_type}")
        else:
            print(f"Configuration file {args.config_file} is invalid:")
            for error in errors:
                print(f"  - {error}")
            sys.exit(1)
            
    except FileNotFoundError:
        print(f"Configuration file {args.config_file} not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in configuration file: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description='Manage tenant upload configurations')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List upload configurations')
    list_parser.add_argument('tenant_id', help='Tenant ID')
    list_parser.add_argument('--subtenant-id', help='Subtenant ID')
    list_parser.add_argument('--channel-type', choices=[c.value for c in UploadChannelType], help='Filter by channel type')
    list_parser.add_argument('--show-inactive', action='store_true', help='Show inactive configurations')
    list_parser.set_defaults(func=list_configs)
    
    # Show command
    show_parser = subparsers.add_parser('show', help='Show detailed configuration')
    show_parser.add_argument('tenant_id', help='Tenant ID')
    show_parser.add_argument('--subtenant-id', help='Subtenant ID')
    show_parser.add_argument('--channel-type', choices=[c.value for c in UploadChannelType], help='Channel type')
    show_parser.set_defaults(func=show_config)
    
    # Add command
    add_parser = subparsers.add_parser('add', help='Add new upload configuration')
    add_parser.add_argument('tenant_id', help='Tenant ID')
    add_parser.add_argument('channel_type', choices=[c.value for c in UploadChannelType], help='Channel type')
    add_parser.add_argument('--subtenant-id', help='Subtenant ID')
    add_parser.add_argument('--name', help='Configuration name')
    add_parser.add_argument('--description', help='Configuration description')
    add_parser.add_argument('--config-file', help='Configuration file (JSON)')
    add_parser.add_argument('--default', action='store_true', help='Set as default configuration')
    add_parser.add_argument('--created-by', help='User who created this configuration')
    add_parser.set_defaults(func=add_config)
    
    # Update command
    update_parser = subparsers.add_parser('update', help='Update existing configuration')
    update_parser.add_argument('config_id', help='Configuration ID')
    update_parser.add_argument('--config-file', help='Configuration file (JSON)')
    update_parser.add_argument('--description', help='Configuration description')
    update_parser.add_argument('--default', action='store_true', help='Set as default configuration')
    update_parser.add_argument('--updated-by', help='User who updated this configuration')
    update_parser.set_defaults(func=update_config)
    
    # Delete command
    delete_parser = subparsers.add_parser('delete', help='Delete configuration')
    delete_parser.add_argument('config_id', help='Configuration ID')
    delete_parser.add_argument('--deleted-by', help='User who deleted this configuration')
    delete_parser.set_defaults(func=delete_config)
    
    # Create example command
    example_parser = subparsers.add_parser('create-example', help='Create example configuration file')
    example_parser.add_argument('channel_type', choices=[c.value for c in UploadChannelType], help='Channel type')
    example_parser.add_argument('--output-file', help='Output file path')
    example_parser.set_defaults(func=create_example_config)
    
    # Validate command
    validate_parser = subparsers.add_parser('validate', help='Validate configuration file')
    validate_parser.add_argument('config_file', help='Configuration file to validate')
    validate_parser.add_argument('channel_type', choices=[c.value for c in UploadChannelType], help='Channel type')
    validate_parser.set_defaults(func=validate_config_file)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    try:
        args.func(args)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main() 