"""
Upload Service Classes
Provides tenant-specific upload services for different channel types.
"""

import json
import os
import ssl
from abc import ABC, abstractmethod
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
from io import BytesIO

import paramiko
import pika
import requests
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from azure.servicebus import ServiceBusClient, ServiceBusMessage
from minio import Minio
from minio.error import S3Error

from models.tenant_upload_config import UploadChannelType


class UploadService(ABC):
    """Abstract base class for upload services"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    @abstractmethod
    def upload_file(self, file_id: str, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload a file and return the upload URL or identifier"""
        pass
    
    @abstractmethod
    def upload_metadata(self, metadata: Dict[str, Any], file_path: str) -> str:
        """Upload metadata and return the upload URL or identifier"""
        pass
    
    @abstractmethod
    def get_upload_url(self, file_path: str) -> str:
        """Get the upload URL for a file"""
        pass


class SFTPUploadService(UploadService):
    """SFTP upload service"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.host = config.get('host', '')
        self.port = config.get('port', 22)
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.private_key_path = config.get('private_key_path', '')
        self.upload_path = config.get('upload_path', 'Documents')
        self.folder_structure = config.get('folder_structure', '{tenant_id}/{subtenant_id}/{date}/{transaction_id}')
        self.file_naming_convention = config.get('file_naming_convention', '{claim_number}_{sender_name}_{document_type}_{patient_name}')
        self.sort_by_date = config.get('sort_by_date', False)
        self.create_metadata_file = config.get('create_metadata_file', True)
        self.metadata_filename = config.get('metadata_filename', 'index.json')
    
    def _get_sftp_connection(self) -> paramiko.SFTPClient:
        """Create SFTP connection"""
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        if self.private_key_path and os.path.exists(self.private_key_path):
            private_key = paramiko.RSAKey.from_private_key_file(self.private_key_path)
            ssh_client.connect(
                hostname=self.host,
                port=self.port,
                username=self.username,
                pkey=private_key
            )
        else:
            ssh_client.connect(
                hostname=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                look_for_keys=False
            )
        
        return ssh_client.open_sftp()
    
    def _mkdir_p(self, sftp: paramiko.SFTPClient, remote_directory: str):
        """Create remote directory recursively if it does not exist"""
        dirs_ = []
        dir_ = remote_directory
        while len(dir_) > 1:
            dirs_.append(dir_)
            dir_, _ = os.path.split(dir_)
        if len(dir_) == 1 and not dir_.startswith("/"):
            dirs_.append(dir_)  # For a remote path like 'dirname/subdir/'
        while len(dirs_):
            dir_ = dirs_.pop()
            try:
                sftp.listdir(dir_)
            except IOError:
                sftp.mkdir(dir_)
    
    def _ensure_unique_filename(self, sftp: paramiko.SFTPClient, remote_path: str) -> str:
        """Ensure unique filename by appending index if needed"""
        base_path, extension = os.path.splitext(remote_path)
        index = 1
        final_path = f"{base_path}{extension}"
        
        while True:
            try:
                sftp.stat(final_path)  # This will raise an IOError if the file does not exist
                final_path = f"{base_path} {index}{extension}"  # File exists, modify the name
                index += 1
            except IOError:
                break  # The file does not exist, safe to upload
        
        return final_path
    
    def upload_file(self, file_id: str, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload a file to SFTP"""
        # Get file from MinIO
        minio_client = self._get_minio_client()
        response = minio_client.get_object('from-sftp', file_id)
        file_stream = BytesIO(response.read())
        
        # Setup SFTP connection
        sftp = self._get_sftp_connection()
        
        try:
            # Ensure unique filename
            remote_path = f"{self.upload_path}/{file_path}"
            remote_path = self._ensure_unique_filename(sftp, remote_path)
            
            # Create directory structure
            self._mkdir_p(sftp, os.path.dirname(remote_path))
            
            # Upload the file
            sftp.putfo(file_stream, remote_path)
            
            print(f"File uploaded to SFTP: {remote_path}")
            return remote_path
            
        finally:
            file_stream.close()
            sftp.close()
    
    def upload_metadata(self, metadata: Dict[str, Any], file_path: str) -> str:
        """Upload metadata to SFTP"""
        # Prepare file stream object contains json with metadata
        json_str = json.dumps(metadata, sort_keys=False, indent=4)
        json_bytes = json_str.encode('utf-8')
        file_stream = BytesIO(json_bytes)
        
        # Setup SFTP connection
        sftp = self._get_sftp_connection()
        
        try:
            # Ensure unique filename
            remote_path = f"{self.upload_path}/{file_path}"
            remote_path = self._ensure_unique_filename(sftp, remote_path)
            
            # Create directory structure
            self._mkdir_p(sftp, os.path.dirname(remote_path))
            
            # Upload the file
            sftp.putfo(file_stream, remote_path)
            
            print(f"Metadata uploaded to SFTP: {remote_path}")
            return remote_path
            
        finally:
            file_stream.close()
            sftp.close()
    
    def get_upload_url(self, file_path: str) -> str:
        """Get SFTP file path (SFTP doesn't have URLs)"""
        return f"sftp://{self.host}:{self.port}/{self.upload_path}/{file_path}"
    
    def _get_minio_client(self) -> Minio:
        """Get MinIO client for file retrieval"""
        return Minio(
            os.environ.get('MINIO_URI', '127.0.0.1:9015'),
            access_key=os.environ.get('MINIO_ACCESS_KEY', ''),
            secret_key=os.environ.get('MINIO_SECRET_KEY', ''),
            secure=os.environ.get('MINIO_SECURE', 'true').lower() == 'true'
        )


class ServiceBusUploadService(UploadService):
    """Azure Service Bus upload service"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.connection_string = config.get('connection_string', '')
        self.topic_name = config.get('topic_name', '')
        self.queue_name = config.get('queue_name', '')
        self.blob_storage_connection_string = config.get('blob_storage_connection_string', '')
        self.blob_container_name = config.get('blob_container_name', '')
        self.message_format = config.get('message_format', 'json')
        self.include_blob_urls = config.get('include_blob_urls', True)
        self.sas_token_expiry_days = config.get('sas_token_expiry_days', 14)
        self.folder_structure = config.get('folder_structure', '{tenant_id}/{subtenant_id}/{date}/{transaction_id}')
        
        # Initialize clients
        self.service_bus_client = ServiceBusClient.from_connection_string(self.connection_string)
        if self.blob_storage_connection_string:
            self.blob_service_client = BlobServiceClient.from_connection_string(self.blob_storage_connection_string)
    
    def upload_file(self, file_id: str, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload file to Azure Blob Storage and return blob URL"""
        if not self.blob_storage_connection_string:
            raise ValueError("Blob storage connection string not configured")
        
        # Get file from MinIO
        minio_client = self._get_minio_client()
        response = minio_client.get_object('from-sftp', file_id)
        file_stream = BytesIO(response.read())
        
        # Upload to Azure Blob Storage
        blob_client = self.blob_service_client.get_blob_client(
            container=self.blob_container_name, 
            blob=file_path
        )
        blob_client.upload_blob(file_stream, overwrite=True)
        
        print(f"File uploaded to Azure Blob Storage: {file_path}")
        return blob_client.url
    
    def upload_metadata(self, metadata: Dict[str, Any], file_path: str) -> str:
        """Upload metadata to Azure Blob Storage"""
        if not self.blob_storage_connection_string:
            raise ValueError("Blob storage connection string not configured")
        
        # Prepare metadata JSON
        json_str = json.dumps(metadata, sort_keys=False, indent=4)
        json_bytes = json_str.encode('utf-8')
        file_stream = BytesIO(json_bytes)
        
        # Upload to Azure Blob Storage
        blob_client = self.blob_service_client.get_blob_client(
            container=self.blob_container_name, 
            blob=file_path
        )
        blob_client.upload_blob(file_stream, overwrite=True)
        
        print(f"Metadata uploaded to Azure Blob Storage: {file_path}")
        return blob_client.url
    
    def publish_message(self, message_data: Dict[str, Any]) -> None:
        """Publish message to Service Bus topic or queue"""
        if self.topic_name:
            topic_client = self.service_bus_client.get_topic_sender(topic_name=self.topic_name)
            message = ServiceBusMessage(json.dumps(message_data))
            topic_client.send_messages(message)
            print(f"Message published to Service Bus Topic: {self.topic_name}")
        elif self.queue_name:
            queue_client = self.service_bus_client.get_queue_sender(queue_name=self.queue_name)
            message = ServiceBusMessage(json.dumps(message_data))
            queue_client.send_messages(message)
            print(f"Message published to Service Bus Queue: {self.queue_name}")
    
    def get_upload_url(self, file_path: str) -> str:
        """Generate SAS URL for blob"""
        if not self.blob_storage_connection_string:
            return ""
        
        blob_client = self.blob_service_client.get_blob_client(
            container=self.blob_container_name, 
            blob=file_path
        )
        sas_token = generate_blob_sas(
            account_name=blob_client.account_name,
            container_name=blob_client.container_name,
            blob_name=blob_client.blob_name,
            account_key=self.blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True),
            expiry=datetime.utcnow() + timedelta(days=self.sas_token_expiry_days)
        )
        return f"{blob_client.url}?{sas_token}"
    
    def _get_minio_client(self) -> Minio:
        """Get MinIO client for file retrieval"""
        return Minio(
            os.environ.get('MINIO_URI', '127.0.0.1:9015'),
            access_key=os.environ.get('MINIO_ACCESS_KEY', ''),
            secret_key=os.environ.get('MINIO_SECRET_KEY', ''),
            secure=os.environ.get('MINIO_SECURE', 'true').lower() == 'true'
        )


class APIS3UploadService(UploadService):
    """API with S3 upload service"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_endpoint = config.get('api_endpoint', '')
        self.api_key = config.get('api_key', '')
        self.s3_bucket_name = config.get('s3_bucket_name', '')
        self.s3_access_key = config.get('s3_access_key', '')
        self.s3_secret_key = config.get('s3_secret_key', '')
        self.s3_region = config.get('s3_region', 'us-east-1')
        self.s3_endpoint_url = config.get('s3_endpoint_url', '')
        self.folder_structure = config.get('folder_structure', '{tenant_id}/{subtenant_id}/{date}/{transaction_id}')
        self.upload_method = config.get('upload_method', 'multipart')  # multipart, presigned_url
        self.include_metadata = config.get('include_metadata', True)
    
    def upload_file(self, file_id: str, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload file via API"""
        # Get file from MinIO
        minio_client = self._get_minio_client()
        response = minio_client.get_object('from-sftp', file_id)
        file_data = response.read()
        
        if self.upload_method == 'multipart':
            return self._upload_multipart(file_data, file_path, metadata)
        elif self.upload_method == 'presigned_url':
            return self._upload_presigned_url(file_data, file_path, metadata)
        else:
            raise ValueError(f"Unsupported upload method: {self.upload_method}")
    
    def _upload_multipart(self, file_data: bytes, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload using multipart form data"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'multipart/form-data'
        }
        
        files = {'file': (os.path.basename(file_path), file_data, 'application/pdf')}
        data = {'path': file_path}
        
        if metadata and self.include_metadata:
            data['metadata'] = json.dumps(metadata)
        
        response = requests.post(
            f"{self.api_endpoint}/upload",
            headers=headers,
            files=files,
            data=data
        )
        response.raise_for_status()
        
        result = response.json()
        return result.get('url', '')
    
    def _upload_presigned_url(self, file_data: bytes, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload using presigned URL"""
        # First, get presigned URL from API
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'path': file_path,
            'content_type': 'application/pdf'
        }
        
        if metadata and self.include_metadata:
            data['metadata'] = metadata
        
        response = requests.post(
            f"{self.api_endpoint}/presigned-url",
            headers=headers,
            json=data
        )
        response.raise_for_status()
        
        result = response.json()
        presigned_url = result.get('presigned_url', '')
        
        if presigned_url:
            # Upload file using presigned URL
            upload_response = requests.put(
                presigned_url,
                data=file_data,
                headers={'Content-Type': 'application/pdf'}
            )
            upload_response.raise_for_status()
        
        return result.get('url', '')
    
    def upload_metadata(self, metadata: Dict[str, Any], file_path: str) -> str:
        """Upload metadata via API"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'path': file_path,
            'metadata': metadata
        }
        
        response = requests.post(
            f"{self.api_endpoint}/metadata",
            headers=headers,
            json=data
        )
        response.raise_for_status()
        
        result = response.json()
        return result.get('url', '')
    
    def get_upload_url(self, file_path: str) -> str:
        """Get upload URL from API"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f"{self.api_endpoint}/file-url",
            headers=headers,
            params={'path': file_path}
        )
        response.raise_for_status()
        
        result = response.json()
        return result.get('url', '')
    
    def _get_minio_client(self) -> Minio:
        """Get MinIO client for file retrieval"""
        return Minio(
            os.environ.get('MINIO_URI', '127.0.0.1:9015'),
            access_key=os.environ.get('MINIO_ACCESS_KEY', ''),
            secret_key=os.environ.get('MINIO_SECRET_KEY', ''),
            secure=os.environ.get('MINIO_SECURE', 'true').lower() == 'true'
        )


class APIRestUploadService(UploadService):
    """REST API upload service"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_endpoint = config.get('api_endpoint', '')
        self.api_key = config.get('api_key', '')
        self.auth_method = config.get('auth_method', 'bearer')  # bearer, basic, custom
        self.content_type = config.get('content_type', 'multipart/form-data')
        self.upload_method = config.get('upload_method', 'multipart')  # multipart, json
        self.include_metadata = config.get('include_metadata', True)
        self.retry_attempts = config.get('retry_attempts', 3)
        self.timeout_seconds = config.get('timeout_seconds', 30)
        self.custom_headers = config.get('custom_headers', {})
    
    def upload_file(self, file_id: str, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload file via REST API"""
        # Get file from MinIO
        minio_client = self._get_minio_client()
        response = minio_client.get_object('from-sftp', file_id)
        file_data = response.read()
        
        if self.upload_method == 'multipart':
            return self._upload_multipart(file_data, file_path, metadata)
        elif self.upload_method == 'json':
            return self._upload_json(file_data, file_path, metadata)
        else:
            raise ValueError(f"Unsupported upload method: {self.upload_method}")
    
    def _upload_multipart(self, file_data: bytes, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload using multipart form data"""
        headers = self._get_headers()
        
        files = {'file': (os.path.basename(file_path), file_data, 'application/pdf')}
        data = {'path': file_path}
        
        if metadata and self.include_metadata:
            data['metadata'] = json.dumps(metadata)
        
        for attempt in range(self.retry_attempts):
            try:
                response = requests.post(
                    f"{self.api_endpoint}/upload",
                    headers=headers,
                    files=files,
                    data=data,
                    timeout=self.timeout_seconds
                )
                response.raise_for_status()
                break
            except requests.exceptions.RequestException as e:
                if attempt == self.retry_attempts - 1:
                    raise e
                continue
        
        result = response.json()
        return result.get('url', '')
    
    def _upload_json(self, file_data: bytes, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Upload using JSON payload with base64 encoded file"""
        import base64
        
        headers = self._get_headers()
        headers['Content-Type'] = 'application/json'
        
        data = {
            'path': file_path,
            'file_data': base64.b64encode(file_data).decode('utf-8'),
            'file_name': os.path.basename(file_path)
        }
        
        if metadata and self.include_metadata:
            data['metadata'] = metadata
        
        for attempt in range(self.retry_attempts):
            try:
                response = requests.post(
                    f"{self.api_endpoint}/upload",
                    headers=headers,
                    json=data,
                    timeout=self.timeout_seconds
                )
                response.raise_for_status()
                break
            except requests.exceptions.RequestException as e:
                if attempt == self.retry_attempts - 1:
                    raise e
                continue
        
        result = response.json()
        return result.get('url', '')
    
    def upload_metadata(self, metadata: Dict[str, Any], file_path: str) -> str:
        """Upload metadata via REST API"""
        headers = self._get_headers()
        headers['Content-Type'] = 'application/json'
        
        data = {
            'path': file_path,
            'metadata': metadata
        }
        
        for attempt in range(self.retry_attempts):
            try:
                response = requests.post(
                    f"{self.api_endpoint}/metadata",
                    headers=headers,
                    json=data,
                    timeout=self.timeout_seconds
                )
                response.raise_for_status()
                break
            except requests.exceptions.RequestException as e:
                if attempt == self.retry_attempts - 1:
                    raise e
                continue
        
        result = response.json()
        return result.get('url', '')
    
    def get_upload_url(self, file_path: str) -> str:
        """Get upload URL from REST API"""
        headers = self._get_headers()
        
        for attempt in range(self.retry_attempts):
            try:
                response = requests.get(
                    f"{self.api_endpoint}/file-url",
                    headers=headers,
                    params={'path': file_path},
                    timeout=self.timeout_seconds
                )
                response.raise_for_status()
                break
            except requests.exceptions.RequestException as e:
                if attempt == self.retry_attempts - 1:
                    raise e
                continue
        
        result = response.json()
        return result.get('url', '')
    
    def _get_headers(self) -> Dict[str, str]:
        """Get request headers with authentication"""
        headers = self.custom_headers.copy()
        
        if self.auth_method == 'bearer':
            headers['Authorization'] = f'Bearer {self.api_key}'
        elif self.auth_method == 'basic':
            import base64
            auth_string = base64.b64encode(f":{self.api_key}".encode()).decode()
            headers['Authorization'] = f'Basic {auth_string}'
        elif self.auth_method == 'custom':
            headers['X-API-Key'] = self.api_key
        
        return headers
    
    def _get_minio_client(self) -> Minio:
        """Get MinIO client for file retrieval"""
        return Minio(
            os.environ.get('MINIO_URI', '127.0.0.1:9015'),
            access_key=os.environ.get('MINIO_ACCESS_KEY', ''),
            secret_key=os.environ.get('MINIO_SECRET_KEY', ''),
            secure=os.environ.get('MINIO_SECURE', 'true').lower() == 'true'
        )


def create_upload_service(channel_type: str, config: Dict[str, Any]) -> UploadService:
    """Factory function to create upload service based on channel type"""
    if channel_type == UploadChannelType.SFTP.value:
        return SFTPUploadService(config)
    elif channel_type in [UploadChannelType.SERVICEBUS_QUEUE.value, UploadChannelType.SERVICEBUS_TOPIC.value]:
        return ServiceBusUploadService(config)
    elif channel_type == UploadChannelType.API_S3.value:
        return APIS3UploadService(config)
    elif channel_type == UploadChannelType.API_REST.value:
        return APIRestUploadService(config)
    else:
        raise ValueError(f"Unsupported channel type: {channel_type}") 