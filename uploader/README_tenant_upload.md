# Tenant Upload Configuration System

This document describes the tenant-specific upload configuration system that allows different tenants to have their own upload destinations and configurations.

## Overview

The tenant upload configuration system enables:
- **Multi-tenant upload isolation**: Each tenant can have their own upload destinations
- **Multiple upload channels**: Support for SFTP, Azure Service Bus, API with S3, and REST API
- **Flexible configuration**: Tenant-specific settings for each upload channel
- **Fallback support**: Legacy global configuration as fallback when tenant config is not available

## Supported Upload Channels

1. **SFTP** (`sftp`)
   - Traditional file transfer protocol
   - Supports folder structure customization
   - File naming conventions
   - Date-based sorting

2. **Azure Service Bus Topic** (`servicebus_topic`)
   - Publish messages to Azure Service Bus topics
   - Upload files to Azure Blob Storage
   - Generate SAS URLs for file access

3. **Azure Service Bus Queue** (`servicebus_queue`)
   - Send messages to Azure Service Bus queues
   - Upload files to Azure Blob Storage
   - Generate SAS URLs for file access

4. **API with S3** (`api_s3`)
   - Upload files via REST API
   - Store files in S3-compatible storage
   - Support for multipart and presigned URL uploads

5. **REST API** (`api_rest`)
   - Upload files via REST API endpoints
   - Support for multipart and JSON uploads
   - Custom authentication methods

## Database Setup

### 1. Run the Migration

Execute the migration script to create the required table:

```sql
-- Run the migration script
\i migrations/add_tenant_upload_config.sql
```

### 2. Verify Table Creation

```sql
-- Check if the table was created
\d tenant_upload_configurations

-- Check if example configurations were inserted
SELECT tenant_id, configuration_name, channel_type, is_default 
FROM tenant_upload_configurations;
```

## Configuration Management

### Using the Management Utility

The system provides a command-line utility for managing tenant upload configurations:

```bash
# List configurations for a tenant
python uploader/manage_tenant_upload_config.py list tenant_123

# Show detailed configuration
python uploader/manage_tenant_upload_config.py show tenant_123 --channel-type sftp

# Create example configuration
python uploader/manage_tenant_upload_config.py example sftp --output-file sftp_config.json

# Add new configuration
python uploader/manage_tenant_upload_config.py add tenant_123 sftp \
  --name "Production SFTP" \
  --config-file sftp_config.json \
  --default

# Update configuration
python uploader/manage_tenant_upload_config.py update config_id_123 \
  --config-file updated_config.json

# Delete configuration
python uploader/manage_tenant_upload_config.py delete config_id_123
```

### Configuration Examples

#### SFTP Configuration

```json
{
  "host": "sftp.example.com",
  "port": 22,
  "username": "upload_user",
  "password": "secure_password",
  "upload_path": "Documents",
  "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}",
  "file_naming_convention": "{claim_number}_{sender_name}_{document_type}_{patient_name}",
  "sort_by_date": false,
  "create_metadata_file": true,
  "metadata_filename": "index.json"
}
```

#### Azure Service Bus Topic Configuration

```json
{
  "connection_string": "Endpoint=sb://example.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your_key_here",
  "topic_name": "document-uploads",
  "blob_storage_connection_string": "DefaultEndpointsProtocol=https;AccountName=example;AccountKey=your_key_here;EndpointSuffix=core.windows.net",
  "blob_container_name": "documents",
  "message_format": "json",
  "include_blob_urls": true,
  "sas_token_expiry_days": 14,
  "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}"
}
```

#### API S3 Configuration

```json
{
  "api_endpoint": "https://api.example.com/upload",
  "api_key": "your_api_key_here",
  "s3_bucket_name": "example-documents",
  "s3_access_key": "your_s3_access_key",
  "s3_secret_key": "your_s3_secret_key",
  "s3_region": "us-east-1",
  "folder_structure": "{tenant_id}/{subtenant_id}/{date}/{transaction_id}",
  "upload_method": "multipart",
  "include_metadata": true
}
```

#### REST API Configuration

```json
{
  "api_endpoint": "https://api.example.com/upload",
  "api_key": "your_api_key_here",
  "auth_method": "bearer",
  "content_type": "multipart/form-data",
  "upload_method": "multipart",
  "include_metadata": true,
  "retry_attempts": 3,
  "timeout_seconds": 30,
  "custom_headers": {}
}
```

## Integration with Uploader

### How It Works

1. **Message Processing**: When the uploader receives a message, it extracts tenant information
2. **Configuration Lookup**: The system looks up tenant-specific upload configuration
3. **Service Creation**: Creates the appropriate upload service based on configuration
4. **File Upload**: Uploads files using tenant-specific settings
5. **Fallback**: If no tenant configuration is found, falls back to legacy global configuration

### Code Example

```python
# In the uploader callback
def callback(msg_tuple):
    ch, method, properties, body = msg_tuple
    queue_item = json.loads(body.decode('UTF-8'))
    
    tenant_id = queue_item.get('tenant_id')
    subtenant_id = queue_item.get('subtenant_id')
    
    # The upload_data function will automatically use tenant-specific configuration
    upload_data(splitted_documents, incoming_package)
```

### Environment Variables

The system still supports legacy environment variables as fallback:

```bash
# Legacy SFTP configuration (fallback)
SFTP_UPLOAD_HOST=127.0.0.1
SFTP_UPLOAD_PORT=2222
SFTP_UPLOAD_USER=user
SFTP_UPLOAD_PASSWORD=password
SFTP_UPLOAD_PATH=Documents

# Legacy Azure configuration (fallback)
AZURE_STORAGE_CONNECTION_STRING=your_connection_string
AZURE_SERVICE_BUS_CONNECTION_STRING=your_connection_string
AZURE_SERVICE_BUS_TOPIC_NAME=your_topic_name

# Uploader channel (fallback)
UPLOADER_CHANNEL=sftp
```

## Folder Structure Templates

The system supports customizable folder structures using template variables:

- `{tenant_id}`: Tenant identifier
- `{subtenant_id}`: Subtenant identifier
- `{date}`: Date in YYYYMMDD format
- `{transaction_id}`: Unique transaction identifier
- `{claim_number}`: Claim number from document metadata
- `{sender_name}`: Sender name from document metadata
- `{document_type}`: Document type
- `{patient_name}`: Patient name from document metadata

### Example Folder Structures

```
# Tenant-isolated structure
{tenant_id}/{subtenant_id}/{date}/{transaction_id}/

# Date-based structure
{date}/{tenant_id}/{transaction_id}/

# Document-type based structure
{tenant_id}/{document_type}/{date}/{transaction_id}/
```

## File Naming Conventions

Customizable file naming using template variables:

```
# Default convention
{claim_number}_{sender_name}_{document_type}_{patient_name}

# Simple convention
{document_type}_{date}_{transaction_id}

# Patient-focused convention
{patient_name}_{document_type}_{date}
```

## Monitoring and Logging

The system provides comprehensive logging:

```python
# Log messages include tenant information
print(f"Processing upload for tenant: {tenant_id}, subtenant: {subtenant_id}")
print(f"Using upload service for tenant {tenant_id}, subtenant {subtenant_id}")
print(f"Uploading file {file_path} using tenant upload service...")
```

## Error Handling

The system includes robust error handling:

1. **Configuration Not Found**: Falls back to legacy configuration
2. **Upload Service Errors**: Logs errors and continues with other files
3. **Validation Errors**: Validates configurations before use
4. **Database Errors**: Handles database connection issues gracefully

## Best Practices

### 1. Configuration Management

- Use descriptive configuration names
- Set default configurations for each tenant/channel combination
- Document configuration changes
- Use environment-specific configurations

### 2. Security

- Store sensitive credentials securely
- Use environment variables for production credentials
- Implement proper access controls
- Regularly rotate credentials

### 3. Monitoring

- Monitor upload success rates
- Track configuration usage
- Set up alerts for upload failures
- Log configuration changes

### 4. Testing

- Test configurations in development environment
- Validate file uploads for each channel type
- Test fallback scenarios
- Verify tenant isolation

## Troubleshooting

### Common Issues

1. **No Configuration Found**
   - Check if tenant configuration exists in database
   - Verify tenant_id and subtenant_id in incoming messages
   - Check if configuration is marked as active

2. **Upload Failures**
   - Verify connection strings and credentials
   - Check network connectivity
   - Validate file permissions
   - Review error logs

3. **Configuration Validation Errors**
   - Check required fields for each channel type
   - Validate JSON format
   - Ensure proper data types

### Debug Commands

```bash
# Check tenant configurations
python uploader/manage_tenant_upload_config.py list tenant_123

# Validate configuration
python uploader/manage_tenant_upload_config.py show tenant_123 --channel-type sftp

# Test upload service creation
python -c "
from uploader.tenant_upload_manager import TenantUploadManager
from pipeline_utils.database_connector import DBConnector
db = DBConnector()
session = db.get_session()
manager = TenantUploadManager(session)
service = manager.get_upload_service('tenant_123')
print('Service created:', service is not None)
"
```

## Migration from Legacy System

### Step 1: Create Tenant Configurations

```bash
# Create SFTP configuration for existing tenant
python uploader/manage_tenant_upload_config.py add existing_tenant sftp \
  --name "Legacy SFTP Migration" \
  --config-json '{
    "host": "'$SFTP_UPLOAD_HOST'",
    "port": '$SFTP_UPLOAD_PORT',
    "username": "'$SFTP_UPLOAD_USER'",
    "password": "'$SFTP_UPLOAD_PASSWORD'",
    "upload_path": "'$SFTP_UPLOAD_PATH'"
  }' \
  --default
```

### Step 2: Test Configuration

```bash
# Verify configuration is working
python uploader/manage_tenant_upload_config.py show existing_tenant --channel-type sftp
```

### Step 3: Update Application

- Ensure tenant_id is included in incoming messages
- Deploy updated uploader code
- Monitor for any issues

### Step 4: Remove Legacy Configuration

- Once confirmed working, remove legacy environment variables
- Update documentation
- Clean up old configuration files

## API Reference

### TenantUploadManager

Main class for managing tenant upload configurations.

```python
class TenantUploadManager:
    def get_upload_service(tenant_id, subtenant_id=None, channel_type=None) -> UploadService
    def get_tenant_upload_config(tenant_id, subtenant_id=None, channel_type=None) -> Dict
    def set_tenant_upload_config(tenant_id, channel_type, channel_config, ...) -> Tuple[bool, List[str]]
    def update_tenant_upload_config(config_id, channel_config, ...) -> Tuple[bool, List[str]]
    def delete_tenant_upload_config(config_id, deleted_by=None) -> bool
    def list_tenant_upload_configs(tenant_id, subtenant_id=None, ...) -> List[Dict]
```

### UploadService

Base class for upload services.

```python
class UploadService:
    def upload_file(file_id, file_path, metadata=None) -> str
    def upload_metadata(metadata, file_path) -> str
    def get_upload_url(file_path) -> str
```

### Configuration Validation

```python
def validate_configuration(channel_type, config) -> Tuple[bool, List[str]]
```

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review error logs
3. Validate configurations
4. Test with example configurations
5. Contact the development team 