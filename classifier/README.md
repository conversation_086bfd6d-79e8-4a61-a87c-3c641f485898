# Docker manual for classifier

## Building a Docker for classifier module

1. Start build by 
```docker build -t dx-classifier-app .```

## Using a Docker for metadata_extractor module
1. Mount weights for running module by adding `-v` option to docker in docker run command (name is example and is configured in `config.yml`)
```-v ./{local_path}/model.pkl:/app/model.pkl```
2. Mount local configuration and secret keys for external services 
```-v ./{local_path}/config.yml:/app/config.yml```
3. Mount pgSQL models configuration 
```-v ./{local_path}/models/:/models/```
4. Pass ports required for external services using `-p` option or use `--network host` to use host network
```-p 5438:5438 -p 5682:5682 -p ...```

5. Run `docker run {options ... } --gpus all dx-classifier-app ` with all required mounts and options 

## Classifier models  
The first 2 models are used directly to classify document types, while the third is used separately by engineers  
1. ```general_model``` - detects (on a page-by-page basis) almost all classes except Medical Records and Work Status - instead, it detects the page as the beginning of the document (header) or the continuation of the document (content).  
2. ```packet_model``` - combines together the text from the header pages + all content, and defines a single class for them together, Medical Records or Work Status.  
3. ```header_content_model``` - detects (page by page) for all pages whether it is the beginning of any document (header) or continuation (content).  
  
To train the classifier model use:  
```from classifier import Model  
   import pandas as pd  
  
   model = Model(save_path=<folder to save>)  
   data = pd.read_csv(<data file>, sep=';')  
   model.train_with_confidences(data, <y_name>, <test_size>, <max_class_count>, <random_state>, <neurons>, <n_epochs>, <save_path>)
```  
  
Args:  
- train_data (pd.DataFrame): train data.  
- y_name (str): name of target column (doc type).  
- test_size (float): test size - use 0.2.  
- max_class_count (int): max count for every class - use 3000.
- random_state (int): random state - use random number, there is no reason to find an optimal value, the model fits with any number.  
- neurons (int): number of neurons - use 70.  
- n_epochs (int): max number of epochs - use 2000.  
- save_path (str): path to save model file.  
