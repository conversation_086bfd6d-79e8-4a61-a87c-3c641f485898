docker run --restart=always --network host -v "/e/DeepX/record ranger/dx_record_ranger_ml_pipeline/config.yml":/app/config.yml -v ".\classifier.py":/app/classifier.py -v "/e/DeepX/record ranger/dx_record_ranger_ml_pipeline/classifier/data/paddleocr/":/root/.paddleocr/ -v "/e/DeepX/record ranger/dx_record_ranger_ml_pipeline/classifier/data/nltk_data/":/root/nltk_data/ -v "/e/DeepX/record ranger/dx_record_ranger_ml_pipeline/models/":/app/ -v ".\model.pkl":/app/model.pkl -it --gpus all classifier-app
