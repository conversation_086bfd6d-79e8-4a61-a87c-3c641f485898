docker run \
  --network host \
  -v /home/<USER>/dx_record_ranger_ml_pipeline/config.yml:/app/config.yml -v ./:/app/ \
  -v /home/<USER>/dx_record_ranger_ml_pipeline/models/:/models/  \
  -v /home/<USER>/dx_record_ranger_ml_pipeline/pipeline_utils/:/pipeline_utils/ \
  -v ./classifier.py:/app/classifier.py \
  -v ./data/paddleocr/:/root/.paddleocr/ \
  -v ./data/nltk_data/:/root/nltk_data/ \
  -v ./model.pkl:/app/model.pkl \
  -it --gpus all dx-classifier-app
