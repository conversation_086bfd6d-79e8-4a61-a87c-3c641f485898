#!/bin/bash
# howto prepare host system

sudo apt-get update && sudo apt-get install -y poppler-utils

conda create -n atom_classifier python=3.9

conda install -y -c conda-forge gcc=12.2.0

#conda install nvidia/label/cuda-12.0.0::libcublas #?
#conda install nvidia/label/cuda-12.0.0::cuda-toolkit #?

python3 -m pip install paddlepaddle-gpu==2.6.0.post120 -f https://www.paddlepaddle.org.cn/whl/linux/cudnnin/stable.html

python3 -m pip install -r requirements.txt

### download cudnn 8.9.1 here https://developer.nvidia.com/rdp/cudnn-archive
# sudo apt install ./<filename.deb>
# sudo cp /var/cudnn-local-repo-ubuntu2204-********/cudnn-local-E7A7D88D-keyring.gpg /usr/share/keyrings/
# sudo apt update
# sudo apt install libcudnn8 libcudnn8-dev libcudnn8-samples
### download cuda here https://developer.nvidia.com/cuda-12-0-0-download-archive?target_os=Linux&target_arch=x86_64&Distribution=Ubuntu&target_version=22.04&target_type=deb_network
#wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-ubuntu2204.pin
#sudo mv cuda-ubuntu2204.pin /etc/apt/preferences.d/cuda-repository-pin-600
#wget https://developer.download.nvidia.com/compute/cuda/12.0.0/local_installers/cuda-repo-ubuntu2204-12-0-local_12.0.0-525.60.13-1_amd64.deb
#sudo dpkg -i cuda-repo-ubuntu2204-12-0-local_12.0.0-525.60.13-1_amd64.deb
#sudo cp /var/cuda-repo-ubuntu2204-12-0-local/cuda-*-keyring.gpg /usr/share/keyrings/
#sudo apt-get update
#sudo apt-get -y install cuda

### unload nvidia modules
#sudo rmmod nvidia_drm
#sudo rmmod nvidia_modeset
#sudo rmmod nvidia_uvm
#sudo rmmod nvidia

### is not needed
#conda install anaconda::cudnn=********
#python3 -m pip install paddlepaddle-gpu==2.6.0.post120 -f https://www.paddlepaddle.org.cn/whl/linux/mkl/avx/stable.html
