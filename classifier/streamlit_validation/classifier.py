import pandas as pd
import numpy as np
import pickle
import time
import os
import argparse

import re
import nltk
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer

nltk.download('stopwords')
nltk.download('wordnet')

from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.neural_network import MLPClassifier

from paddleocr import PaddleOCR
ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)

import matplotlib.pyplot as plt
import seaborn as sns


class Model:
    def __init__(self, model_path=None, save_path=None):
        """ Init model.

        Args:
            model_path (str): path to model file.
            save_path (str): path to save model file.
        """

        self._model_path = model_path
        self._save_path = save_path

        self._model = None

        #  10: 'Utilization Review',
        #  11: 'Peer Review',
        self._classes = {0:  'Other',
                         1:  'RFA',
                         2:  'Medical Records',
                         3:  'Misc Correspondence',
                         4:  'Legal Correspondence',
                         5:  'State forms',
                         6:  'Ratings',
                         7:  'Subpoena',
                         8:  'EOB/EOR',
                         9:  'IMR/IME/QME',
                         12: 'Supplemental/Work Status',
                         13: 'Physician Bill (HCFA)',
                         14: 'Hospital Bill (UB)',
                         15: 'Translation Bills',
                         16: 'Determination - Med Auth',
                         17: 'Vocational Evaluations',
                         18: 'Case Management Notes',
                         19: 'Fax'}
        self._key_words = {
            'Other':                     None, 
            'RFA':                      'Request for Authorization, RFA, UR, Utilization Review, ' + \
                                        'Peer Review, DWC, Doctor 1st report, Doctor First Report, DWC FORM, 5021',
            'Medical Records':          'Progress Notes, Medical records, Objective, Notes, Exam, Findings, PR2',
            'Misc Correspondence':       None,
            'Legal Correspondence':     'Attorney, Firm, Bureau, Legal',
            'State forms':              'C4, MG1, MG2, LA1022, DWC026, W9',
            'Ratings':                  'Disability Raiting, Medical raitings, ratings',
            'Subpoena':                 'Subpoena',
            'EOB/EOR':                  'Explanation of benefits, Explanation of Review',
            'IMR/IME/QME':              'IMR, IME,QME (Independent Medical Revw/Evaluation or Qualified Medical Evaluator/Evaluation)',
            'Utilization Review':       'UR Utilization Review',
            'Peer Review':              'Peer Review, Claims Eval, Dane Street',
            'Supplemental/Work Status': 'work status, return to work status, suplemental work status, modified duty, full duty',
            'Physician Bill (HCFA)':    None,
            'Hospital Bill (UB)':       None,
            'Translation Bills':        'translation, interpretation',
            'Determination - Med Auth': 'RFA Determination, UR Determination, Peer Determination',
            'Vocational Evaluations':   'Vocational Evaluation',
            'Case Management Notes':    None,
            'Fax':                      'Fax'
                              }
        self._train_info = {'name': 'MLPClassifier', 'accuracy': 0, 'train_time': 0, 'classes_trained': 4,
                            'confusion_matrix': None, 'classification_report': None, 
                            'files_amount': 0, 'pages_amount': 0}
        self._vectorizer = None

    
    def __str__(self):
        train_info_str = "\n".join(f"{key}: {value}" for key, value in self._train_info.items())
        return f"Model: \nmodel_path={self._model_path}, save_path={self._save_path}, train_info:\n{train_info_str}"
    

    def import_model(self, model_path):
        """ Import model from file. 
        
        Args:
            model_path (str): path to model file.        
        """

        # check if model file exists
        if self._model_path is None:
            raise ValueError('Model path is not specified')
        
        # check if model file is pkl file format
        if not self._model_path.endswith('.pkl'):
            raise ValueError('Model path is not in .pkl format')
        
        # try to import model and vectorizer
        try:
            with open(model_path, 'rb') as f:
                data = pickle.load(f)
                self._model = data['model']
                self._vectorizer = data['vectorizer']
                self._classes = data['classes']
                self._key_words = data['key_words']
                self._train_info = data['train_info']
        except:
            raise ValueError('Import model error: wrong model path or file corrupted')


    def save_model(self):
        """ Save model and vectorizer to file. 
        
        Args:
            model_path (str): path to model file.
        """

        # check if save path is specified
        if self._save_path is None:
            raise ValueError('Save path is not specified')
        
        # check if save path is pkl file format and add it if not
        if not self._save_path.endswith('.pkl'):
            self._save_path += '.pkl'
        
        # save model and vectorizer
        with open(self._save_path, 'wb') as f:
            pickle.dump({'model': self._model, 
                         'vectorizer': self._vectorizer, 
                         'classes': self._classes,
                         'key_words': self._key_words,
                         'train_info': self._train_info}, f)
        

    def preprocess(self, train_data, y_name, test_size, random_state, mode='predict'):
        """
        Preprocess data.

        Args:
            train_data (pd.DataFrame): train data.
            y_name (str): name of target column.
            test_size (float): test size.
            random_state (int): random state.

        Returns:
            X_train, X_test, y_train, y_test (pd.DataFrame): train and test data.
        """

        stop_words = stopwords.words('english')
        lemmatizer = WordNetLemmatizer()

        # preprocess text
        text = train_data['Text'].copy()
        # fill NaN or None values with an empty string
        text = text.fillna('')
        # remove numbers
        text = text.apply(lambda x: re.sub('[^a-zA-Z]', ' ', x))
        # small letters
        text = text.apply(lambda x: x.lower())
        # remove punctuation
        punctuations = '''!()-[]{};:'"\,<>./?@#$%^&*_~'''
        text = text.apply(lambda x: ''.join([char for char in x if char not in punctuations]))
        # remove stop words
        text = text.apply(lambda x: ' '.join([word for word in x.split() if word not in stop_words]))
        # lemmatization
        text = text.apply(lambda x: ' '.join([lemmatizer.lemmatize(word) for word in x.split()]))
        # remove short and long words
        text = text.apply(lambda x: ' '.join([word for word in x.split() if word != ' ' and len(word) > 1 and len(word) < 20]))
        train_data['Text'] = text

        if mode == 'train':
            # check if y_name is in train data
            if y_name not in train_data.columns:
                raise ValueError(f'Column {y_name} is not in train data')

            # check if there are enough data
            if len(train_data) < 2:
                raise ValueError(f'There are not enough data to train model')

            # select features and target
            X = train_data.drop(y_name, axis=1)
            y = train_data[y_name].reset_index(drop=True)
        
        else:
            X = train_data
            y = None

        if mode == 'train':
            # split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, 
                                                                random_state=random_state)
            # word vectorization
            X_train = self._vectorizer.fit_transform(X_train['Text']).toarray()
            X_test = self._vectorizer.transform(X_test['Text']).toarray()

            return X_train, X_test, y_train, y_test
        
        # word vectorization
        X = self._vectorizer.transform(X['Text']).toarray()
        return X


    def train(self, train_data, y_name='Class', 
              test_size=0.2, random_state=42, 
              neurons=30, n_epochs=1000, 
              save_path=''):
        """
        Train MLPRegressor model.

        Args:
            train_data (pd.DataFrame): train data.
            y_name (str): name of target column.
            test_size (float): test size.
            random_state (int): random state.
            neurons (int): number of neurons.
            n_epochs (int): max number of epochs.
            save_path (str): path to save model file.
        """
        self._train_info['files_amount'] = len(train_data['File_name'].unique())
        self._train_info['pages_amount'] = len(train_data)
        # drop columns
        train_data.drop(['File_name', 'Format', 'Page'], axis=1, inplace=True)
        
        # Preprocess data
        self._vectorizer = TfidfVectorizer()
        X_train, X_test, y_train, y_test = self.preprocess(train_data, y_name, test_size, random_state, mode='train')
        
        # Init model
        self._model = MLPClassifier(hidden_layer_sizes=(neurons,), max_iter=n_epochs)
        
        # Train model
        start_time = time.time()
        self._model.fit(X_train, y_train)
        end_time = time.time()

        # Make predictions and calculate metrics
        y_pred = self._model.predict(X_test)
        self._train_info['accuracy'] = self._model.score(X_test, y_test)
        self._train_info['train_time'] = end_time - start_time
        self._train_info['confusion_matrix'] = confusion_matrix(y_test, y_pred)
        self._train_info['classification_report'] = classification_report(y_test, y_pred)

        # Print info and metrics
        print(self)

        # Save model
        if save_path:
            self.save_model()
            # Save confusion matrix
            plt.figure(figsize=(10, 10))
            sns.heatmap(confusion_matrix(y_test, y_pred), annot=True, fmt='d', cmap='Blues')
            plt.xlabel('Predicted')
            plt.ylabel('Actual')
            plt.savefig(f'{save_path}/confusion_matrix.png')


    def train_with_confidences(self, train_data, y_name='Class',
                               test_size=0.2, random_state=42, 
                               neurons=30, n_epochs=1000, 
                               save_path=''):
        """
        Train MLPRegressor model.

        Args:
            train_data (pd.DataFrame): train data.
            y_name (str): name of target column.
            test_size (float): test size.
            random_state (int): random state.
            neurons (int): number of neurons.
            n_epochs (int): max number of epochs.
            save_path (str): path to save model file.
        """

        change_classes = {v: k for k, v in self._classes.items()}
        train_data[y_name] = train_data[y_name].apply(lambda x: change_classes[x])

        self._train_info['files_amount'] = len(train_data['File_name'].unique())
        self._train_info['pages_amount'] = len(train_data)
        # drop columns
        extra_info = train_data[['File_name', 'Format', 'Page']]
        extra_info_train, extra_info_test = train_test_split(extra_info, test_size=test_size, random_state=random_state)
        train_data.drop(['File_name', 'Format', 'Page'], axis=1, inplace=True)
        
        # Preprocess data
        self._vectorizer = TfidfVectorizer()
        X_train, X_test, y_train, y_test = self.preprocess(train_data, y_name, test_size, random_state, mode='train')
        unique_test = np.unique(y_test)
        
        # Init model
        self._model = MLPClassifier(hidden_layer_sizes=(neurons,), max_iter=n_epochs)
        
        # Train model
        start_time = time.time()
        self._model.fit(X_train, y_train)
        end_time = time.time()

        # Make predictions and calculate metrics
        y_pred = self._model.predict(X_test)
        y_pred_proba = self._model.predict_proba(X_test)

        convert_classes = {0: 1, 1: 2, 2: 16, 3: 18}
        y_pred_proba_df = pd.DataFrame(y_pred_proba, columns=convert_classes.values())

        self._train_info['accuracy'] = self._model.score(X_test, y_test)
        self._train_info['train_time'] = end_time - start_time
        self._train_info['classification_report'] = classification_report(y_test, y_pred)
        
        # Convert y_pred and y_test to DataFrame for easier manipulation
        y_pred_df = pd.DataFrame(y_pred, columns=['Predicted'])
        y_test_df = pd.DataFrame(y_test.values, columns=['Actual'])
        # Concatenate y_pred, y_test and extra_info to a DataFrame
        result_df = pd.concat([y_pred_df, y_test_df, extra_info_test.reset_index(drop=True)], axis=1)
        # Save to CSV
        result_df.to_csv(f'{save_path}/results.csv', index=False)

        # combine y_pred_df, y_test_df and y_pred_proba_df
        general_df = pd.concat([y_pred_df, y_test_df, y_pred_proba_df], axis=1)

        class_pred_proba = {}

        confusion_matrices = {}
        true_positives = {}
        false_positives = {}
        true_negatives = {}
        false_negatives = {}

        # Split class_pred_proba into four groups
        for i in list(self._classes.keys()):
            no_samples = True
            if i in y_pred_proba_df.columns:
                class_pred_proba[i] = y_pred_proba_df[i].values
                
                y_test_binary = (y_test == i).astype(int)
                y_pred_binary = (y_pred == i).astype(int)
                confusion_matrices[i] = confusion_matrix(y_test_binary, y_pred_binary)
                
                true_positives[i] = general_df[(general_df['Actual'] == i) & (general_df['Predicted'] == i)][i].values
                false_positives[i] = general_df[(general_df['Actual'] != i) & (general_df['Predicted'] == i)][i].values
                true_negatives[i] = general_df[(general_df['Actual'] != i) & (general_df['Predicted'] != i)][i].values
                false_negatives[i] = general_df[(general_df['Actual'] == i) & (general_df['Predicted'] != i)][i].values

                # print accuracy for each class
                if (y_test == i).any():
                    no_samples = False
                    print(f'Accuracy for class {self._classes[i]}: {round(self._model.score(X_test[y_test == i], y_test[y_test == i]), 2)} ' + \
                        f'with {len(y_test[y_test == i])} samples in test and {len(y_train[y_train == i])} samples in train')
            else:
                class_pred_proba[i] = np.zeros(len(y_test))
                confusion_matrices[i] = np.zeros((2, 2))
            if no_samples:
                print(f'Accuracy for class {self._classes[i]}: N/A with 0 samples in test and {len(y_train[y_train == i])} samples in train')


        # Create a DataFrame for seaborn
        data = pd.concat([
            pd.DataFrame({"Confidence": true_positives[i], "Group": "True Positives", "Class": i}) for i in list(self._classes.keys()) if i in true_positives]+
            [pd.DataFrame({"Confidence": false_positives[i], "Group": "False Positives", "Class": i}) for i in list(self._classes.keys()) if i in false_positives]+
            [pd.DataFrame({"Confidence": true_negatives[i], "Group": "True Negatives", "Class": i}) for i in list(self._classes.keys()) if i in true_negatives]+
            [pd.DataFrame({"Confidence": false_negatives[i], "Group": "False Negatives", "Class": i}) for i in list(self._classes.keys()) if i in false_negatives]
        )
        data["Confidence"] = np.round(data["Confidence"], 2)

        # Print info and metrics
        print(self)

        # Save model
        if save_path:
            self.save_model()
            fig, axs = plt.subplots(len(unique_test), 5, figsize=(25, len(unique_test)*5))
            row_num = 0
            for i in unique_test:
                # Save confusion matrix for each class
                sns.heatmap(confusion_matrices[i].astype(int), annot=True, fmt='d', cmap='Blues', ax=axs[row_num, 0])
                axs[row_num, 0].set_xlabel('Predicted')
                axs[row_num, 0].set_ylabel('Actual')
                axs[row_num, 0].set_title(f'Confusion matrix for class {self._classes[i]}')
                # Save histogram for each class and for each group
                groups = ["True Negatives", "False Positives", "False Negatives", "True Positives"]
                for ax, group in zip(axs[row_num, 1:], groups):
                    sns.histplot(data[(data["Group"] == group) & (data["Class"] == i)], x="Confidence", ax=ax, bins=20)
                    ax.set_title(group)
                    ax.set_xlim([0, 1])
                row_num += 1
            plt.savefig(f'{save_path}/diagrams.png')
            plt.close()

            # general confusion matrix
            class_names = [self._classes[i] for i in unique_test]
            plt.figure(figsize=(10, 10))
            sns.heatmap(confusion_matrix(y_test, y_pred), annot=True, fmt='d', cmap='Blues', xticklabels=class_names, yticklabels=class_names)
            plt.xlabel('Predicted')
            plt.ylabel('Actual')
            plt.tight_layout()
            plt.savefig(f'{save_path}/confusion_matrix.png')
            plt.close()


    def predict(self, data):
        """
        Predict target values.

        Args:
            data (pd.DataFrame): data.

        Returns:
            predictions (np.array): predictions.
        """
        
        self.import_model(self._model_path)

        # Preprocess data
        data = self.preprocess(data, y_name=None, test_size=None, random_state=None, mode='predict')

        # Make predictions
        predictions = self._model.predict(data)
        return predictions
    
    def predict_on_text(self, text):
        """
        Predict target values.

        Args:
            text (str): preprocessed text.

        Returns:
            predictions (np.array): predictions.
        """
        # Preprocess data
        data = pd.DataFrame({'Text': [text]})
        # vectorize text
        data_vectorized = self._vectorizer.transform(data['Text']).toarray()
        # Make predictions
        predictions = self._model.predict_proba(data_vectorized)
        # select biggest probability
        max_pred = np.max(predictions)
        if max_pred < 0.8:
            return 0
        # find class with max probability and convert it to original class
        max_class = np.argmax(predictions)
        convert_classes = {0: 1, 1: 2, 2: 16, 3: 18}
        max_class = convert_classes[max_class]
        return max_class
    

def split_pages(data_path):
    """ Split data into pages.

    Args:
        data_path (str): path to data file.

    Returns:
        pages (list): list of pages.
    """

    def split_pdf(path):
        """ Split pdf file into pages (images)
        
        Args:
            path (str): path to pdf file.
            
        Returns:
            pages (list): list of pages.
        """
        from pdf2image import convert_from_path
        pages = convert_from_path(path)

    formats = {'pdf': split_pdf}
    extension = data_path.split('.')[-1]
    if extension not in formats.keys():
        raise ValueError(f'Wrong data file format: {extension}')
    pages = formats[extension](data_path)
    return pages


def perform_ocr(page):
    """ Perform OCR on page.

    Args:
        page (PIL.Image): page.

    Returns:
        text (str): recognized text.
    """
    numpydata = np.asarray(page)
    result = ocr.ocr(numpydata, cls=True)
    result = result[0]
    text = ''
    if result:
        text = ' '.join([line_info[-1][0] for line_info in result])
    return text


def main(train, predict, save_path, model_path, data_path, 
         y_name, test_size, random_state, neurons, n_epochs):
    """ Main function.
    
    Args:
        train (bool): train model flag.
        predict (bool): predict target values flag.
        save_path (str): path to save model file.
        model_path (str): path to model file.
        data_path (str): path to data file.
        y_name (str): name of target column.
        test_size (float): test size.
        random_state (int): random state.
        neurons (int): number of neurons.
        n_epochs (int): max number of epochs.
    """
    model = Model(model_path, f'{save_path}/model.pkl')

    if train:
        # load data
        # train data: csv with columns 'File_Name', 'Format', 'Page', 'Text', 'Class'
        # 'Text -> 'Class'
        data = pd.read_csv(data_path, sep=';')
        # train model
        # model.train(data, y_name, test_size, random_state, neurons, n_epochs, save_path)
        model.train_with_confidences(data, y_name, test_size, random_state, neurons, n_epochs, save_path)

    if predict:
        # load data
        # predict data: pdf file
        # pdf -> split pages -> perform ocr -> predict
        pages = split_pages(data_path)
        # perform ocr
        text = [perform_ocr(page) for page in pages]
        # create dataframe
        data = pd.DataFrame({'text': text})
        # predict target values
        predictions = model.predict(data)
        # save predictions
        np.savetxt('predictions.txt', predictions, fmt='%d')


if __name__ == '__main__':
    argparser = argparse.ArgumentParser()
    argparser.add_argument('--train', action='store_true', help='train model')
    argparser.add_argument('--predict', action='store_true', help='predict target values')

    argparser.add_argument('--model_path', type=str, help='path to model file')
    argparser.add_argument('--save_path', type=str, help='path to save model file')
    argparser.add_argument('--data_path', type=str, help='path to data file')

    argparser.add_argument('--y_name', type=str, default='Class', help='name of target column')
    argparser.add_argument('--test_size', type=float, default=0.2, help='test size')
    argparser.add_argument('--random_state', type=int, default=42, help='random state')
    argparser.add_argument('--neurons', type=int, default=30, help='number of neurons')
    argparser.add_argument('--n_epochs', type=int, default=1000, help='max number of epochs')
    args = argparser.parse_args()

    main(args.train, args.predict, args.save_path, args.model_path, args.data_path,
         args.y_name, args.test_size, args.random_state, args.neurons, args.n_epochs)
    
