import pickle
import streamlit_authenticator as stauth


names = ['admin', 'usertest', 'pamela_telfer', 'marisa_jackson', 'riley_jones']
usernames = ['admin', 'usertest', 'pamela_telfer', 'marisa_jackson', 'riley_jones']
passwords = ['xxxxxxx', 'xxxxxxx', 'xxxxxxx', 'xxxxxxx', 'xxxxxxx']
hashed_passwords = stauth.Hasher(passwords).generate()
file_path = 'credentials.pkl'
with open(file_path, 'wb') as f:
    credentials = {"usernames":{}}
    for uname, name, pwd in zip(usernames, names, hashed_passwords):
        user_dict = {"name": name, "password": pwd}
        credentials["usernames"].update({uname: user_dict})
    pickle.dump(credentials, f)
