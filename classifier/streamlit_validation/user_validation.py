import pandas as pd
import numpy as np
import pickle
import time

import streamlit as st
# pip install streamlit-option-menu
from streamlit_option_menu import option_menu
# pip install streamlit-authenticator
import streamlit_authenticator as stauth

import sqlite3

import fitz
from pdf2image import convert_from_path
from classifier import Model
from paddleocr import PaddleOCR
from PIL import Image
import re
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer

#nltk.download('stopwords')
#nltk.download('wordnet')


# ------------------ Database ------------------
conn = sqlite3.connect('database.db')
cursor = conn.cursor()
# ------------------ Database ------------------


st.set_page_config(page_title='Document Validation', page_icon=':memo:', layout='wide')


try:
    # ------------------ Init auth variable and import model ------------------
    if 'authorized' not in st.session_state:
        st.session_state['authorized'] = False
        model_path = 'models/model.pkl'
        st.session_state['model'] = Model(model_path)
        st.session_state['model'].import_model(model_path)
        st.session_state['classes_dict'] = st.session_state['model']._classes
        st.session_state['classes'] = list(st.session_state['classes_dict'].values())
        st.session_state['keywords'] = st.session_state['model']._key_words

        st.session_state['ocr'] = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
        st.session_state['lemmatizer'] = WordNetLemmatizer()
        st.session_state['stopwords'] = stopwords.words('english')
    # ------------------ User authorization ------------------
    if st.session_state['authorized'] == False:
        # load credentials
        file_path = 'credentials.pkl'
        with open(file_path, 'rb') as f:
            credentials = pickle.load(f)

        # authorization
        st.session_state['authenticator'] = stauth.Authenticate(credentials, "cookies_new", "geruklsdjlsdfddaft", 
                                                                cookie_expiry_days=7)
        st.session_state['name'], st.session_state['authentication_status'], st.session_state['username'] = st.session_state['authenticator'].login("Login", "main")

        if st.session_state['authentication_status'] == False:
            st.error("Username or password is incorrect")
        if st.session_state['authentication_status'] == None:
            st.warning("Please enter your username and password")
        if st.session_state['authentication_status'] == True:
            # load user info
            cursor.execute('SELECT user_id, user_type, current_file, current_page FROM users WHERE username = ?', (st.session_state['username'],))
            result = cursor.fetchone()

            if result:
                st.session_state['user_id'] = result[0]
                st.session_state['user_type'] = result[1]
                st.session_state['current_file'] = result[2]
                st.session_state['current_page'] = result[3]
            else:
                st.session_state['authorized'] = False

            #TODO: add new user to database
            
            # load from data user_label_id
            cursor.execute('SELECT user_label_id FROM data WHERE user_id = ? ORDER BY user_label_id DESC LIMIT 1', (st.session_state['user_id'],))
            result = cursor.fetchone()
            if result:
                st.session_state['user_label_id'] = result[0] + 1
            else:
                st.session_state['user_label_id'] = 1
            st.session_state['last_user_label_id'] = st.session_state['user_label_id']

            st.session_state['authorized'] = True
    # ------------------ User authorization ------------------

    # ------------------ Main page ------------------
    if st.session_state['authorized']:
        # variables
        if 'prediction' not in st.session_state:
            st.session_state['pdf_document'] = None
            st.session_state['pdf_page'] = None
            st.session_state['prediction'] = None
            st.session_state['comment'] = None
            st.session_state['session_id'] = 0
            
        # ------------------ Side bar ------------------
        with st.sidebar:
            sidebar_list = ['Validation', 'Validation info', 'Log out']
            if st.session_state['user_type'] == 'admin':
                sidebar_list = ['Validation', 'Validation info', 'Retrain model', 'Log out']

            selected_sidebar = option_menu(
                menu_title = "Atom AI Document Classifier",
                options = sidebar_list,
                default_index = 0,
            )
        # ------------------ Side bar ------------------

        if selected_sidebar == 'Validation':
            st.title('Document Validation')
            st.markdown('---')

            # ------------------ navigation buttons ------------------
            # previous and next buttons
            navigation_buttons = st.empty()
            with navigation_buttons:
                # 10 columns for navigation buttons
                column1, column2, column3, column4, column5, column6, column7, column8 = st.columns(8)
                if st.session_state['user_label_id'] > 1:
                    if column1.button('Previous page'):
                        # select from data by user_label_id - 1
                        previous_validation = cursor.execute('SELECT * FROM data WHERE user_id = ? AND user_label_id = ?',
                                                            (st.session_state['user_id'], st.session_state['user_label_id'] - 1)).fetchone()
                        if previous_validation:
                            st.session_state['user_label_id'] = previous_validation[2]
                            st.session_state['current_file'] = f"{previous_validation[4]}.{previous_validation[5]}"
                            st.session_state['current_page'] = previous_validation[6]
                            st.session_state['pdf_document'] = None
                            st.session_state['pdf_page'] = None
                            st.session_state['prediction'] = previous_validation[8]
                            st.session_state['prediction_text'] = f"Previous label: {st.session_state['prediction']}"
                            st.session_state['comment'] = previous_validation[9]
                            st.session_state['submitted_prediction'] = st.session_state['prediction']
                            st.session_state['session_id'] += 1
                            st.rerun()
                if st.session_state['user_label_id'] < st.session_state['last_user_label_id']:
                    if column2.button('Next page'):
                        next_validation = cursor.execute('SELECT * FROM data WHERE user_id = ? AND user_label_id = ?',
                                                            (st.session_state['user_id'], st.session_state['user_label_id'] + 1)).fetchone()
                        if next_validation:
                            st.session_state['user_label_id'] = next_validation[2]
                            st.session_state['current_file'] = f"{next_validation[4]}.{next_validation[5]}"
                            st.session_state['current_page'] = next_validation[6]
                            st.session_state['pdf_document'] = None
                            st.session_state['pdf_page'] = None
                            st.session_state['prediction'] = next_validation[8]
                            st.session_state['prediction_text'] = f"Previous label: {st.session_state['prediction']}"
                            st.session_state['comment'] = next_validation[9]
                            st.session_state['submitted_prediction'] = st.session_state['prediction']
                            st.session_state['session_id'] += 1
                            st.rerun()
                        else:
                            # like continue button
                            continue_validation = cursor.execute('SELECT current_file, current_page FROM users WHERE username = ?',
                                                                (st.session_state['username'],)).fetchone()
                            st.session_state['user_label_id'] = st.session_state['last_user_label_id']
                            st.session_state['current_file'] = continue_validation[0]
                            st.session_state['current_page'] = continue_validation[1]
                            st.session_state['pdf_document'] = None
                            st.session_state['pdf_page'] = None
                            st.session_state['prediction'] = None
                            st.session_state['submitted_prediction'] = None
                            st.session_state['session_id'] += 1
                            st.rerun()
                    if column6.button('Continue validating'):
                        continue_validation = cursor.execute('SELECT current_file, current_page FROM users WHERE username = ?', 
                                                                (st.session_state['username'],)).fetchone()
                        st.session_state['user_label_id'] = st.session_state['last_user_label_id']
                        st.session_state['current_file'] = continue_validation[0]
                        st.session_state['current_page'] = continue_validation[1]
                        st.session_state['pdf_document'] = None
                        st.session_state['pdf_page'] = None
                        st.session_state['prediction'] = None
                        st.session_state['submitted_prediction'] = None
                        st.session_state['session_id'] += 1
                        st.rerun()
                else:
                    if column8.button('Skip file'):
                        # modify pdf2validate and users tables
                        cursor.execute('UPDATE pdf2validate SET status = "skipped", page_stopped = ?, username = ? WHERE file_name = ?',
                                        (st.session_state['current_page'], st.session_state['username'], st.session_state['current_file']))
                        cursor.execute('UPDATE users SET current_file = NULL, current_page = 1 WHERE username = ?', 
                                        (st.session_state['username'],))
                        conn.commit()
                        st.session_state['submitted_previous'] = f"File {st.session_state['current_file']} was skipped"
                        st.session_state['current_file'] = None
                        st.session_state['current_page'] = 1
                        st.session_state['pdf_document'] = None
                        st.session_state['pdf_page'] = None
                        st.session_state['prediction'] = None
                        st.session_state['submitted_prediction'] = None
                        st.session_state['session_id'] += 1
                        st.rerun()
                # if its user add the button to delegate validation to coordinator
                if st.session_state['user_type'] == 'user':
                    if column4.button('Delegate validation'):
                        # modify pdf2validate and users tables
                        cursor.execute('UPDATE pdf2validate SET status = "delegated", page_stopped = ?, username = ? WHERE file_name = ?',
                                        (st.session_state['current_page'], st.session_state['username'], st.session_state['current_file']))
                        cursor.execute('UPDATE users SET current_file = NULL, current_page = 1 WHERE username = ?', 
                                        (st.session_state['username'],))
                        conn.commit()
                        st.session_state['submitted_previous'] = f"File {st.session_state['current_file']} was delegated " + \
                                                                    f"from page {st.session_state['current_page']}"
                        st.session_state['current_file'] = None
                        st.session_state['current_page'] = 1
                        st.session_state['pdf_document'] = None
                        st.session_state['pdf_page'] = None
                        st.session_state['prediction'] = None
                        st.session_state['submitted_prediction'] = None
                        st.session_state['session_id'] += 1
                        st.rerun()

            # ------------------ navigation buttons ------------------

            # print previous prediction
            if 'submitted_previous' in st.session_state:
                st.write(st.session_state['submitted_previous'])

            # ------------------ select file to validate ------------------
            if st.session_state['current_file'] == None:
                # if its coordinator check for delegated files
                validate_new_file = True
                if st.session_state['user_type'] == 'coordinator':
                    cursor.execute('SELECT file_name, page_stopped, username FROM pdf2validate WHERE status = "delegated" LIMIT 1')
                    result = cursor.fetchone()
                    if result:
                        st.session_state['current_file'] = result[0]
                        st.session_state['current_page'] = result[1]
                        cursor.execute('UPDATE users SET current_file = ?, current_page = ? WHERE username = ?', 
                                        (st.session_state['current_file'], st.session_state['current_page'], st.session_state['username']))
                        conn.commit()
                        validate_new_file = False

                if validate_new_file:
                    # modify pdf2validate and users tables
                    cursor.execute('SELECT file_name FROM pdf2validate WHERE status = "pending" LIMIT 1')
                    result = cursor.fetchone()
                    if result:
                        st.session_state['current_file'] = result[0]
                        cursor.execute('UPDATE pdf2validate SET status = "processing", username = ? WHERE file_name = ?', 
                                        (st.session_state['username'], st.session_state['current_file']))
                        cursor.execute('UPDATE users SET current_file = ? WHERE username = ?', 
                                        (st.session_state['current_file'], st.session_state['username']))
                        conn.commit()
                    else:
                        st.write('No files to validate')
                        st.stop()
            
            # check if file is delegated
            if st.session_state['user_type'] == 'coordinator':
                cursor.execute('SELECT username, page_stopped FROM pdf2validate WHERE file_name = ? AND status = "delegated"',
                                (st.session_state['current_file'],))
                result = cursor.fetchone()
                if result:
                    st.write(f"File {st.session_state['current_file']} was delegated from page {result[1]} by {result[0]}")
                    # select info from data
                    cursor.execute('SELECT class, comment FROM data WHERE file_name = ? AND page = ?', 
                                    ('.'.join(st.session_state['current_file'].split('.')[:-1]), 
                                    st.session_state['current_page']))
                    result = cursor.fetchone()
                    if result:
                        st.session_state['prediction'] = result[0]
                        st.session_state['prediction_text'] = f"Previous label: {st.session_state['prediction']}"
                        st.session_state['comment'] = result[1]
                        st.session_state['submitted_prediction'] = st.session_state['prediction']

            # ------------------ select file to validate ------------------

            # ------------------ load file ------------------
            if st.session_state['pdf_document'] == None:
                st.session_state['pdf_document'] = fitz.open(f"pdfs/{st.session_state['current_file']}")
                st.session_state['pdf_pages_number'] = st.session_state['pdf_document'].page_count
            # ------------------ load file ------------------
                
            # ------------------ load page and predict ------------------
            if st.session_state['pdf_page'] == None:
                st.session_state['pdf_page'] = convert_from_path(f"pdfs/{st.session_state['current_file']}", 
                                                                first_page=st.session_state['current_page'], 
                                                                last_page=st.session_state['current_page'])[0]

            if st.session_state['prediction'] == None:
                np_image = np.array(st.session_state['pdf_page'])
                ocr_result = st.session_state['ocr'].ocr(np_image, cls=True)[0]
                ocr_text = ' '.join([line_info[-1][0] for line_info in ocr_result]) if ocr_result else ''
                # text preprocessing
                if ocr_text:
                    ocr_text = re.sub('[^a-zA-Z]', ' ', ocr_text)
                    ocr_text = ocr_text.lower()
                    ocr_text = ''.join([char for char in ocr_text if char not in '''!()-[]{};:'"\,<>./?@#$%^&*_~'''])
                    ocr_text = ' '.join([word for word in ocr_text.split() if word not in st.session_state['stopwords']])
                    ocr_text = ' '.join([st.session_state['lemmatizer'].lemmatize(word) for word in ocr_text.split()])
                    ocr_text = ' '.join([word for word in ocr_text.split() if word != ' ' and len(word) > 1 and len(word) < 20])
                st.session_state['ocr_text'] = ocr_text
                # predict
                st.session_state['prediction_num'] = st.session_state['model'].predict_on_text(ocr_text)
                st.session_state['prediction'] = st.session_state['classes_dict'][st.session_state['prediction_num']]
                st.session_state['submitted_prediction'] = st.session_state['prediction']
                st.session_state['prediction_text'] = f"Prediction: {st.session_state['prediction']}"

            # validation form
            with st.form("validation form", clear_on_submit=True):
                st.image(st.session_state['pdf_page'], use_column_width=True, caption=f"Page {st.session_state['current_page']} " + \
                                                                                    f"of {st.session_state['pdf_pages_number']}")
                "---"
                st.write(st.session_state['prediction_text'])
                keywords_block = st.empty()
                "---"
                placeholder_for_option = st.empty()
                placeholder_for_selectbox = st.empty()

                with st.expander("Comment"):
                    text_before_comment = " "
                    if st.session_state['comment'] != None:
                        text_before_comment = f"Previous comment: {st.session_state['comment']}"
                    comment = st.text_area(text_before_comment, placeholder="Enter your comment here ...")

                submitted = st.form_submit_button("Submit")
                if submitted:
                    date = time.strftime('%Y-%m-%d %H:%M:%S')
                    st.session_state['comment'] = comment
                    # check if it is modified or added
                    if cursor.execute('SELECT * FROM data WHERE file_name = ? AND page = ?', 
                                        ('.'.join(st.session_state['current_file'].split('.')[:-1]), 
                                        st.session_state['current_page'])).fetchone():
                        cursor.execute('UPDATE data SET user_id = ?, user_label_id = ?, date = ?, class = ?, comment = ? WHERE file_name = ? AND page = ?', 
                                        (st.session_state['user_id'], st.session_state['user_label_id'], date,
                                        st.session_state['submitted_prediction'], st.session_state['comment'],
                                        '.'.join(st.session_state['current_file'].split('.')[:-1]),
                                        st.session_state['current_page']))
                    else:
                        cursor.execute('''INSERT INTO data (user_id, user_label_id, date, file_name, format, page, text, class, comment) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)''', 
                                        (st.session_state['user_id'], st.session_state['user_label_id'], date, 
                                        '.'.join(st.session_state['current_file'].split('.')[:-1]), 'pdf', 
                                        st.session_state['current_page'], st.session_state['ocr_text'], 
                                        st.session_state['submitted_prediction'], st.session_state['comment']))
                    conn.commit()
                    st.session_state['submitted_previous'] = f"Page {st.session_state['current_page']} " + \
                                                            f"of file {st.session_state['current_file']} was submitted " + \
                                                            f"with class {st.session_state['submitted_prediction']} " + \
                                                            f"and comment: {st.session_state['comment']}"
                    st.session_state['pdf_page'] = None
                    st.session_state['user_label_id'] += 1
                    st.session_state['last_user_label_id'] = st.session_state['user_label_id']
                    st.session_state['current_page'] += 1
                    if st.session_state['current_page'] > st.session_state['pdf_pages_number']:
                        # modify pdf2validate and users tables
                        cursor.execute('UPDATE pdf2validate SET status = "done" WHERE file_name = ?', 
                                        (st.session_state['current_file'],))
                        cursor.execute('UPDATE users SET current_file = NULL, current_page = 1 WHERE username = ?', 
                                        (st.session_state['username'],))
                        conn.commit()
                        st.session_state['current_file'] = None
                        st.session_state['current_page'] = 1
                        st.session_state['pdf_document'] = None
                    else:
                        cursor.execute('UPDATE users SET current_page = ? WHERE username = ?', 
                                        (st.session_state['current_page'], st.session_state['username']))
                        conn.commit()
                    st.session_state['prediction'] = None
                    st.session_state['comment'] = None
                    st.session_state['ocr_text'] = None
                    st.session_state['session_id'] += 1
                    st.rerun()

            # mutable blocks
            with placeholder_for_option:
                options = ["Yes", "No"]
                st.session_state['classifier_correct'] = st.radio("Is the prediction is correct?", 
                                                                options, key=st.session_state['session_id'])
            with placeholder_for_selectbox:
                if st.session_state['classifier_correct'] == 'No':
                    st.session_state['submitted_prediction'] = st.selectbox('Select class', st.session_state['classes'])
                else:
                    st.session_state['submitted_prediction'] = st.session_state['prediction']
            with keywords_block:
                st.write(f"Keywords for class {st.session_state['submitted_prediction']}: " + \
                        f"{st.session_state['keywords'][st.session_state['submitted_prediction']]}")

        if selected_sidebar == 'Validation info':
            st.title('Validation info')
            st.markdown('---')
            remaining_files = cursor.execute('SELECT COUNT(*) FROM pdf2validate WHERE status = "pending"').fetchone()[0]
            # unique file names
            docs_in_training = cursor.execute('SELECT COUNT(DISTINCT file_name) FROM data').fetchone()[0]
            pages_in_training = cursor.execute('SELECT COUNT(*) FROM data').fetchone()[0]
            st.write(f"Remaining files to validate: {remaining_files}")
            if st.session_state['user_type'] == 'admin':
                st.write(f"Docs in training data: {docs_in_training}")
                st.write(f"Pages in training data: {pages_in_training}")
                st.write(f"Model trained on: {st.session_state['model']._train_info['files_amount']} files")
                st.write(f"Model trained on: {st.session_state['model']._train_info['pages_amount']} pages")
            st.write(f"Current model accuracy: {st.session_state['model']._train_info['accuracy']:.2f}% on "
                    f"{st.session_state['model']._train_info['classes_trained']} classes")
        
        if selected_sidebar == 'Retrain model':
            st.title('Model retraining')
        
        if selected_sidebar == 'Log out':
            st.session_state['authenticator'].cookie_manager.delete("user_cookies")
            st.session_state['name'] = None
            st.session_state['username'] = None
            st.session_state['authentication_status'] = None
            st.session_state['authorized'] = False
            st.session_state.clear()
            st.session_state['logout'] = True
            st.rerun()
# print error and rerun app
except Exception as e:
    print(e)
    st.session_state['authorized'] = False
    st.rerun()

# hide streamlit style
hide_streamlit_style = """
            <style>
            #MainMenu {visibility: hidden;}
            footer {visibility: hidden;}
            header {visibility: hidden;}
            </style>
            """
st.markdown(hide_streamlit_style, unsafe_allow_html=True)
conn.close()
