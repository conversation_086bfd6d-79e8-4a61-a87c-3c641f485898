FROM 112623991000.dkr.ecr.us-east-2.amazonaws.com/paddle-gpu:2.6.1-gpu-cuda11.7-cudnn8.4-trt8.4


ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y poppler-utils

WORKDIR /app

COPY classifier/requirements.txt /app/requirements.txt
COPY models /app/models
COPY pipeline_utils /app/pipeline_utils

RUN python3 -m pip install -r /app/requirements.txt

COPY classifier/classifier.py /app/classifier.py

# Start the application
CMD ["python3", "-u", "classifier.py"]
