import base64
import io

import psycopg2
import os
import json
import sys
import yaml
import fitz
import pika
import ssl
import urllib3
import re

from io import Bytes<PERSON>
from minio import Minio
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import joinedload
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy_utils import UUIDType
from uuid6 import uuid7
from datetime import datetime
from datetime import timezone
from pprint import pprint
from cryptography.fernet import Fernet, InvalidToken
import re
from uuid import UUID

from pipeline_utils.rabbitmq_connector import PikaServiceFactory
from pipeline_utils.database_connector import DBConnector

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import IncomingPackage
from models import Document
from models import Splitted_Document
from pipeline_utils.monitoring import MonitorService, Status

# Project and app configuration
PROJECT_NAME = os.environ.get('PROJECT_NAME', '')
APP_NAME = os.environ.get('APP_NAME', '')

# PostgreSQL configuration
PGSQL_HOST = os.environ.get('PGSQL_HOST', 'localhost')
PGSQL_PORT = int(os.environ.get('PGSQL_PORT', '5438'))
PGSQL_USERNAME = os.environ.get('PGSQL_USERNAME', '')
PGSQL_PASSWORD = os.environ.get('PGSQL_PASSWORD', '')
PGSQL_DB_NAME = os.environ.get('PGSQL_DB_NAME', '')

# RabbitMQ configuration
RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST', 'localhost')
RABBITMQ_PORT = int(os.environ.get('RABBITMQ_PORT', '5682'))
RABBITMQ_USERNAME = os.environ.get('RABBITMQ_USERNAME', '')
RABBITMQ_PASSWORD = os.environ.get('RABBITMQ_PASSWORD', '')
RABBITMQ_TO_UPLOAD_QUEUE_NAME = os.environ.get('RABBITMQ_TO_UPLOAD_QUEUE_NAME', 'to_upload')
RABBITMQ_TO_QA_POSTPROCESS_QUEUE_NAME = os.environ.get('RABBITMQ_TO_QA_POSTPROCESS_QUEUE_NAME', 'to_qa_postprocess')

# Remote RabbitMQ configuration
REMOTE_RABBITMQ_HOST = os.environ.get('REMOTE_RABBITMQ_HOST', '')
REMOTE_RABBITMQ_PORT = int(os.environ.get('REMOTE_RABBITMQ_PORT', '5672'))
REMOTE_RABBITMQ_VHOST = os.environ.get('REMOTE_RABBITMQ_VHOST', '/')
REMOTE_RABBITMQ_USERNAME = os.environ.get('REMOTE_RABBITMQ_USERNAME', '')
REMOTE_RABBITMQ_PASSWORD = os.environ.get('REMOTE_RABBITMQ_PASSWORD', '')
REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME = os.environ.get('REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME', 'from_backend_qa')
TENANT_NAME = os.environ.get('TENANT_NAME', '')

# Dead Letter Queue configuration
DEAD_LETTER_QUEUE_NAME = os.environ.get('DEAD_LETTER_QUEUE_NAME', 'qa_postprocessor_dlq')
MAX_RETRY_ATTEMPTS = int(os.environ.get('MAX_RETRY_ATTEMPTS', '3'))
MINIO_ERROR_MAX_RETRIES = int(os.environ.get('MINIO_ERROR_MAX_RETRIES', '1'))

# MinIO configuration
MINIO_URI = os.environ.get('MINIO_URI', '127.0.0.1:9015')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY', '')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY', '')
MINIO_FILES_BUCKET = os.environ.get('MINIO_FILES_BUCKET', 'from-sftp')
MINIO_OBJECT_URI_PREFIX = os.environ.get('MINIO_OBJECT_URI_PREFIX', '')
MINIO_SECURE = os.environ.get('MINIO_SECURE', 'true').lower() == 'true'

# Monitoring configuration
API_HOST = os.environ.get('MONITOR_HOST', 'http://_address_here_')
API_PORT = os.environ.get('MONITOR_PORT', '')

# Cryptography configuration
KEY = os.environ.get('CRYPTOGRAPHY_KEY', '')

Base = declarative_base()

db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)
Base.metadata.create_all(db_connector.get_engine())
session = None

httpClient = urllib3.PoolManager()

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

REMOTE_SSL_CAFILE_PATH = os.environ.get('REMOTE_SSL_CAFILE_PATH', '')
REMOTE_SSL_CERTFILE_PATH = os.environ.get('REMOTE_SSL_CERTFILE_PATH', '')
REMOTE_SSL_KEYFILE_PATH = os.environ.get('REMOTE_SSL_KEYFILE_PATH', '')

context = None
if REMOTE_SSL_CAFILE_PATH and REMOTE_SSL_CERTFILE_PATH and REMOTE_SSL_KEYFILE_PATH:
    try:
        context = ssl.create_default_context(cafile=REMOTE_SSL_CAFILE_PATH)
        context.load_cert_chain(certfile=REMOTE_SSL_CERTFILE_PATH,
                                keyfile=REMOTE_SSL_KEYFILE_PATH)
        print("SSL context for RabbitMQ configured successfully.")
    except Exception as e:
        print(f"Error configuring SSL context for RabbitMQ: {e}")
        context = None
else:
    print("SSL configuration not found in config. Proceeding without SSL for RabbitMQ.")

# **THIS is the important line** – only wrap the context when it exists
ssl_options = pika.SSLOptions(context) if context else None


remote_rmq_service_factory = PikaServiceFactory(
    host=REMOTE_RABBITMQ_HOST,
    port=REMOTE_RABBITMQ_PORT,
    virtual_host=REMOTE_RABBITMQ_VHOST,
    username=REMOTE_RABBITMQ_USERNAME,
    password=REMOTE_RABBITMQ_PASSWORD,
    ssl_options=ssl_options
)
remote_rmq_service = remote_rmq_service_factory.create_service()
remote_rmq_service.start()

from_backend_qa_queue_name = REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME
print("""Listening to queue: """ + from_backend_qa_queue_name)


# Dead letter queue is assumed to exist and will be used as configured in DEAD_LETTER_QUEUE_NAME




def delete_splitted_documents(document_uuid):
    global session

    try:
        # Retrieve the Splitted_Document records related to the Document UUID
        splitted_documents = (
            session.query(Splitted_Document)
            .filter(Splitted_Document.parent_document_uuid == document_uuid)
            .all()
        )

        # Delete the Splitted_Document records
        for splitted_doc in splitted_documents:
            session.delete(splitted_doc)

        # Commit the changes to the database
        session.commit()

        # Remove the files from Minio
        for splitted_doc in splitted_documents:
            try:
                minio_client.remove_object(
                    bucket_name=MINIO_FILES_BUCKET,
                    object_name=str(splitted_doc.uuid)
                )
            except Exception as e:
                print(f"Error removing file from Minio: {e}")

        print("Splitted documents and their files deleted successfully.")

    except Exception as e:
        session.rollback()
        print(f"Error deleting splitted documents: {e}")


def get_parent_document(parent_document_file_id):
    global session

    document = session.query(Document).filter_by(uuid=parent_document_file_id).first()
    return document


def enqueue_splitted_document(file_id_str, document_type, tenant_id=None, subtenant_id=None):
    """
    Publishes a message to the specified RabbitMQ queue with the given file ID and document type.

    Args:
        file_id_str (str): The unique identifier of the file to be processed.
        document_type (str): The type of document associated with the file.
        tenant_id (str, optional): The tenant ID for tenant-specific processing.
        subtenant_id (str, optional): The subtenant ID for tenant-specific processing.
    """
    queue_item = {
        'file_id': file_id_str, 
        'document_type': document_type
    }
    
    # Add tenant information if provided
    if tenant_id:
        queue_item['tenant_id'] = tenant_id
    if subtenant_id:
        queue_item['subtenant_id'] = subtenant_id
    
    rmq_service_factory = PikaServiceFactory(
        host=RABBITMQ_HOST,
        port=RABBITMQ_PORT,
        username=RABBITMQ_USERNAME,
        password=RABBITMQ_PASSWORD,
        ssl_options=None
    )
    rmq_service = rmq_service_factory.create_service()
    rmq_service.start()
    try:
        rmq_service.send_message(routing_key=RABBITMQ_TO_UPLOAD_QUEUE_NAME, message=json.dumps(queue_item))
        print(f"Send message to RabbitMQ for tenant: {tenant_id}, subtenant: {subtenant_id}")
    finally:
        rmq_service.stop()


def add_splitted_document_record_to_db(file_id, splitted_document_metadata, parent_document):
    global session
    splitted_document = Splitted_Document()
    splitted_document.uuid = file_id
    splitted_document.parent_document_uuid = parent_document.uuid
    splitted_document.parent_document_pages = splitted_document_metadata['pagesRef']
    splitted_document.parent_document = parent_document
    splitted_document.document_type = splitted_document_metadata[
        "docType"] if "docType" in splitted_document_metadata else "Other"
    splitted_document.metadata_human = splitted_document_metadata
    splitted_document.status = 'to_upload'
    splitted_document.splitted = datetime.now(timezone.utc)
    
    # Inherit tenant information from parent document
    splitted_document.tenant_id = parent_document.tenant_id
    splitted_document.subtenant_id = parent_document.subtenant_id
    
    session.add(splitted_document)
    session.commit()


def add_splitted_document_to_minio(pdf_stream, file_id_str):
    file_size = pdf_stream.getbuffer().nbytes
    minio_client.put_object(MINIO_FILES_BUCKET, file_id_str, pdf_stream, file_size)


def save_splitted_document(pdf_stream, splitted_document_metadata, parent_document):
    file_id = uuid7()
    file_id_str = f'{file_id}'

    add_splitted_document_record_to_db(file_id, splitted_document_metadata, parent_document)
    add_splitted_document_to_minio(pdf_stream, file_id_str)


def create_new_splitted_documents(parent_document_in_memory_file, parent_document, parent_document_data):
    """
    Creates new splitted documents from a parent PDF document based on provided metadata.

    Args:
        parent_document_in_memory_file (BytesIO): A BytesIO object containing the in-memory representation of the parent PDF document.
        parent_document (Document): The parent document object which contains metadata and text OCR information.
        parent_document_data (dict): A dictionary containing metadata for the parent document, including 'metadata_human' which has details for splitting the document.

    Steps:
        1. Open the in-memory PDF file using PyMuPDF (fitz).
        2. Iterate over each set of splitted document metadata in the parent document data's 'metadata' field.
        3. For each set of splitted document metadata:
            a. Create a list of page indices to extract.
            b. Open the parent document again and select only the specified pages.
            c. Save the selected pages to a new in-memory PDF stream with compression and cleanup.
            d. Pass the new subdocument and metadata to the save_splitted_document function.
    """

    for splitted_document_metadata in parent_document_data["metadata"]:
        pages = []
        splitted_document_metadata['pagesRef'].sort(key=lambda x: x[0])
        for page_range in splitted_document_metadata['pagesRef']:
            start_page = int(page_range[0])
            end_page = int(page_range[1])
            pages.extend(range(start_page - 1, end_page))
        parent_document_in_memory_file.seek(0)
        doc = fitz.open(stream=parent_document_in_memory_file, filetype="pdf")
        try:
            doc.select(pages)
        except RuntimeError as e:
            print(f"Error selecting pages {pages} for splitted_document_metadata {splitted_document_metadata}: {e}")
            doc.close()
            continue  # Skip this one if selection fails
        pdf_stream = io.BytesIO()
        doc.save(pdf_stream, garbage=4, deflate=True)
        doc.close()
        pdf_stream.seek(0)
        save_splitted_document(pdf_stream, splitted_document_metadata, parent_document)


def get_all_splitted_documents(parent_document):
    global session
    # target IncomingPackage UUID
    target_uuid = parent_document.uuid

    # Query to find all Splitted_Document items related to the IncomingPackage with the given UUID
    splitted_documents = session.query(Splitted_Document). \
        join(Document, Document.uuid == Splitted_Document.parent_document_uuid). \
        options(joinedload(Splitted_Document.parent_document)). \
        filter(Document.uuid == target_uuid). \
        all()

    return splitted_documents


def enqueue_to_upload(parent_document, tenant_id=None, subtenant_id=None):
    global session
    splitted_documents = get_all_splitted_documents(parent_document)

    # Use provided tenant information or fallback to parent document
    if tenant_id is None:
        tenant_id = parent_document.tenant_id
    if subtenant_id is None:
        subtenant_id = parent_document.subtenant_id
    
    print(f"Enqueueing documents for upload with tenant: {tenant_id}, subtenant: {subtenant_id}")

    for splitted_document in splitted_documents:
        splitted_document.status = "to_upload"
        enqueue_splitted_document(
            str(splitted_document.uuid), 
            splitted_document.document_type,
            tenant_id,
            subtenant_id
        )
    session.commit()


def decrypt_token(encrypted_token, key):
    """
    Decrypt the encrypted token and extract parent_document_uuid and timestamp.
    """
    try:
        cipher = Fernet(key)
        decrypted_token = cipher.decrypt(encrypted_token.encode()).decode()
        parts = decrypted_token.split('-')
        if len(parts) >= 2:
            parent_document_uuid = '-'.join(parts[:-1])
            timestamp = parts[-1]
        else:
            print("Invalid token format.")
            return None, None
        return parent_document_uuid, timestamp
    except InvalidToken:
        print("Invalid token.")
        return None, None


def find_package_by_id(decoded_string, session):
    """
    Search for a matching package_id (UUID) among all IncomingPackage records.

    Args:
        decoded_string (str): The decoded string from the token containing package_id, timestamp, and UUID.
        session: The SQLAlchemy session for database queries.

    Returns:
        bool: True if a matching IncomingPackage is found, False otherwise.
    """
    # Extract package_id from the decoded string
    package_id = decoded_string.split('-')[0]

    # Query the database to find the IncomingPackage with the matching uuid
    package_exists = session.query(IncomingPackage).filter_by(uuid=package_id).first() is not None

    return package_exists


def send_to_dead_letter_queue(channel, method, body, error_message):
    """
    Send a failed message to the dead letter queue.
    
    Args:
        channel: RabbitMQ channel
        method: RabbitMQ method frame
        body: Message body
        error_message: Error description for logging
    """
    try:
        # Prepare message for dead letter queue with error information
        dlq_message = {
            'original_message': json.loads(body.decode('UTF-8')) if isinstance(body, bytes) else body,
            'error_message': error_message,
            'failed_at': datetime.now(timezone.utc).isoformat(),
            'tenant_name': TENANT_NAME,
            'queue_name': from_backend_qa_queue_name
        }
        
        # Send to dead letter queue
        channel.basic_publish(
            exchange='',
            routing_key=DEAD_LETTER_QUEUE_NAME,
            body=json.dumps(dlq_message),
            properties=pika.BasicProperties(
                delivery_mode=2,  # Persistent message
                content_type='application/json'
            )
        )
        
        print(f"Message sent to dead letter queue: {DEAD_LETTER_QUEUE_NAME}")
        print(f"Error: {error_message}")
        
    except Exception as e:
        print(f"Failed to send message to dead letter queue: {e}")
        # If we can't even send to DLQ, log the error and continue
        print(f"Original error: {error_message}")


def should_retry_message(properties, error_message=""):
    """
    Check if a message should be retried based on retry count and error type.
    
    Args:
        properties: RabbitMQ message properties
        error_message: Error message to determine retry strategy
        
    Returns:
        bool: True if message should be retried, False otherwise
    """
    if not properties or not properties.headers:
        return True  # No retry info, allow retry
    
    retry_count = properties.headers.get('x-retry-count', 0)
    
    # Use lower retry count for MinIO errors
    if 'NoSuchKey' in error_message or 'S3 operation failed' in error_message:
        return retry_count < MINIO_ERROR_MAX_RETRIES
    
    # Use standard retry count for other errors
    return retry_count < MAX_RETRY_ATTEMPTS


def is_retryable_error(error_message):
    """
    Check if an error is retryable or should go directly to DLQ.
    
    Args:
        error_message (str): The error message to check
        
    Returns:
        bool: True if error is retryable, False if it should go to DLQ immediately
    """
    # Non-retryable errors (will go directly to DLQ)
    non_retryable_patterns = [
        'NoSuchKey',  # MinIO/S3 file doesn't exist
        'Invalid token',  # Token issues
        'Invalid token format',  # Token format issues
        'Message missing required fields',  # Message format issues
        'Failed to parse message JSON',  # JSON parsing issues
        'No parent document found',  # Database issues
    ]
    
    for pattern in non_retryable_patterns:
        if pattern.lower() in error_message.lower():
            return False
    
    # Default to retryable for other errors
    return True


def increment_retry_count(properties):
    """
    Increment the retry count in message properties.
    
    Args:
        properties: RabbitMQ message properties
        
    Returns:
        dict: Updated headers with incremented retry count
    """
    headers = properties.headers or {}
    retry_count = headers.get('x-retry-count', 0)
    headers['x-retry-count'] = retry_count + 1
    return headers


def callback(msg_tuple):
    ch, method, properties, body = msg_tuple
    """
    Function to be called by RabbitMQ consumer loop when a message is received.
    Includes proper error handling and dead letter queue support.

    Args:
        ch (BlockingChannel): The channel object.
        method (Method): The method frame with delivery tag and other delivery parameters.
        properties (BasicProperties): The properties of the message.
        body (bytes): The message body, expected to be a JSON string with keys 'token', 'data', 
                     and optionally 'tenant_id' and 'subtenant_id'.

    Steps:
        1. Check if message should be retried based on retry count.
        2. Decode the JSON string from the message body to extract 'token', 'data', and tenant info.
        3. Decrypt the token to get parent document ID.
        4. Process the document with proper error handling.
        5. On success: acknowledge the message.
        6. On failure: either retry or send to dead letter queue.
    """

    global session
    
    try:
        # Check if we should retry this message (no error message available yet)
        if not should_retry_message(properties, ""):
            error_msg = f"Message exceeded maximum retry attempts ({MAX_RETRY_ATTEMPTS})"
            send_to_dead_letter_queue(ch, method, body, error_msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)
            return

        # Parse the message
        try:
            queue_item = json.loads(body.decode('UTF-8'))
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            error_msg = f"Failed to parse message JSON: {str(e)}"
            print(f"Non-retryable error detected: {error_msg}")
            send_to_dead_letter_queue(ch, method, body, error_msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)
            return

        # Extract and validate required fields
        if 'token' not in queue_item or 'data' not in queue_item:
            error_msg = "Message missing required fields: 'token' or 'data'"
            print(f"Non-retryable error detected: {error_msg}")
            send_to_dead_letter_queue(ch, method, body, error_msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)
            return

        token = queue_item['token']
        parent_document_data = queue_item['data']
        
        # Extract tenant information from message (optional, will fallback to database values)
        tenant_id = queue_item.get('tenant_id')
        subtenant_id = queue_item.get('subtenant_id')
        
        print(f"Received tenant_id: {tenant_id}, subtenant_id: {subtenant_id}")

        # Decrypt token
        parent_document_id, _ = decrypt_token(token, KEY)
        if not parent_document_id:
            error_msg = "Invalid or expired token"
            print(f"Non-retryable error detected: {error_msg}")
            send_to_dead_letter_queue(ch, method, body, error_msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)
            return

        print(f" [x] Received parent document id: {parent_document_id} (type: {type(parent_document_id)})")
        print("parent_document_data:", parent_document_data)

        # Validate required data fields
        if "predictions" not in parent_document_data or "metadata" not in parent_document_data:
            error_msg = "Message data missing required fields: 'predictions' or 'metadata'"
            print(f"Non-retryable error detected: {error_msg}")
            send_to_dead_letter_queue(ch, method, body, error_msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)
            return

        parent_document_predictions_human = parent_document_data["predictions"]
        parent_document_metadata_human = parent_document_data["metadata"]

        pprint(parent_document_metadata_human)

        # Process the document
        session = db_connector.get_session()
        try:
            parent_document = get_parent_document(parent_document_id)
            
            if not parent_document:
                error_msg = "No parent document found"
                print(f"Non-retryable error detected: {error_msg}")
                send_to_dead_letter_queue(ch, method, body, error_msg)
                ch.basic_ack(delivery_tag=method.delivery_tag)
                return

            # Delete existing splitted documents
            delete_splitted_documents(parent_document_id)
            
            # Update parent document
            parent_document.predictions_human = parent_document_predictions_human
            parent_document.metadata_human = parent_document_metadata_human
            parent_document.status = 'to_upload'
            
            # Update tenant information if provided in message
            if tenant_id is not None:
                parent_document.tenant_id = tenant_id
            if subtenant_id is not None:
                parent_document.subtenant_id = subtenant_id
            
            # Retrieve file from MinIO
            parent_document_id_str = str(parent_document_id)
            print(f"Using parent_document_id_str for MinIO: {parent_document_id_str} (type: {type(parent_document_id_str)})")
            print(f"MinIO bucket: {MINIO_FILES_BUCKET}")
            
            try:
                response = minio_client.get_object(MINIO_FILES_BUCKET, parent_document_id_str)
                parent_document_in_memory_file = BytesIO(response.read())
            except Exception as minio_err:
                error_msg = f"Error retrieving file from MinIO: {str(minio_err)}"
                raise Exception(error_msg)

            # Create new splitted documents
            create_new_splitted_documents(parent_document_in_memory_file, parent_document, parent_document_data)
            
            # Enqueue for upload with tenant information from message or fallback to parent document
            enqueue_to_upload(parent_document, tenant_id, subtenant_id)

            # Commit all changes
            session.commit()
            print("Document splitted, human metadata saved.")
            
            # Acknowledge successful processing
            ch.basic_ack(delivery_tag=method.delivery_tag)
            
        except Exception as processing_error:
            # Rollback database changes
            session.rollback()
            raise processing_error
        finally:
            session.close()

    except Exception as e:
        error_msg = f"Error processing message: {str(e)}"
        print(f"Processing failed: {error_msg}")
        
        # Check if this error is retryable
        if not is_retryable_error(error_msg):
            print(f"Non-retryable error detected: {error_msg}")
            send_to_dead_letter_queue(ch, method, body, error_msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)
            return
        
        # Check if we should retry based on retry count
        if should_retry_message(properties, error_msg):
            # Increment retry count and reject message for retry
            updated_headers = increment_retry_count(properties)
            retry_count = updated_headers.get('x-retry-count', 1)
            print(f"Rejecting message for retry. Retry count: {retry_count}")
            
            # For retryable errors, we still use requeue=True but with proper retry count tracking
            # The message will be redelivered by RabbitMQ after a delay
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
        else:
            # Send to dead letter queue after max retries
            print(f"Message exceeded maximum retry attempts ({MAX_RETRY_ATTEMPTS})")
            send_to_dead_letter_queue(ch, method, body, error_msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)


remote_rmq_service.read_messages(from_backend_qa_queue_name, callback)
# monitor = MonitorService(PROJECT_NAME, APP_NAME, 'qa_postprocessor', API_HOST, API_PORT)
# monitor.start_monitoring()
# monitor.update_status(Status.UP)

remote_rmq_service.run()
