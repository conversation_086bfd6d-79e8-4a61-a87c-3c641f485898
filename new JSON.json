{"packetId": "", "tenantId": "", "subTenantId": "", "transactionId": "Generated Key Id from Atom Team", "packetFileName": "", "pagesCount": "", "packetReceivedTime": "", "packetConfidence": "", "custom1": "", "custom2": "", "custom3": "", "statusCode": "", "documentsCount": "", "documents": [{"docName": "", "docLink": "", "docType": "RFA", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRefOrigin": [[]], "isDuplicated": "", "pagesRef": [[1, 1]], "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "expeditedFlag": "", "rushFlag": "", "jurisdiction": "", "senderName": "", "newSubmission": "", "resubmission": "", "writtenConfirmPriorOralRequest": "", "physician": {"type": "", "npi": "", "firstName": "", "lastName": "", "facilityName": "", "keyContactName": "", "specialty": "", "credentials": "", "emailAddress": "", "phoneNumber": "", "faxNumber": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "physicianName": ""}, "adjuster": {"adjusterId": "", "company": "", "keyContact": "", "firstName": "", "lastName": "", "middleName": "", "suffix": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "phoneExt": "", "faxNumber": "+19999999999", "emailAddress": "<EMAIL>"}, "attorney": {"type": "applicant or defense", "company": "", "firstName": "", "lastName": "", "middleName": "", "suffix": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "phoneExt": "", "faxNumber": "+19999999999", "emailAddress": "<EMAIL>"}, "employer": {"name": "", "phoneNumber": "", "faxNumber": "", "contactEmail": "", "taxId": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}}, "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}, "treatments": [{"diagnosisDescription": [], "diagnosisCode": [], "treatmentServiceRequested": [], "procedureCode": [], "otherInformation": [], "bodyPart": "", "medicationFlag": "", "physicalTherapyFlag": "", "quantity": "", "frequency": "", "duration": ""}], "isEnglishLanguage": true, "isHandwritten": true}}, {"docName": "", "docLink": "", "docType": "Determination - <PERSON><PERSON>", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRefOrigin": [[]], "pagesRef": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}, "summary": "", "isEnglishLanguage": true, "isHandwritten": true}}, {"docName": "", "docLink": "", "docType": "Case Managment Notes", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}, "summary": "", "isEnglishLanguage": true, "isHandwritten": true}}, {"docName": "", "docLink": "", "docType": "Medical Records", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}, "physician": {"npi": "", "firstName": "", "lastName": "", "facilityName": "", "keyContactName": "", "specialty": "", "credentials": "", "emailAddress": "", "phoneNumber": "", "faxNumber": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}}, "summary": "", "isEnglishLanguage": true, "isHandwritten": true}}, {"docName": "", "docLink": "", "docType": "Fax Coversheet", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "subjectLine": "", "body": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": "", "isEnglishLanguage": true, "isHandwritten": true}}}, {"docName": "", "docLink": "", "docType": "IMR IME QME", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}, "summary": "", "isEnglishLanguage": true, "isHandwritten": true}}, {"docName": "", "docLink": "", "docType": "Injury Illness FROI", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}, "summary": "", "isEnglishLanguage": true, "isHandwritten": true}}, {"docName": "", "docLink": "", "docType": "Supplemental Work Status", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}, "physician": {"npi": "", "firstName": "", "lastName": "", "facilityName": "", "keyContactName": "", "specialty": "", "credentials": "", "emailAddress": "", "phoneNumber": "", "faxNumber": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}}, "summary": "", "isEnglishLanguage": true, "isHandwritten": true}}, {"docName": "", "docLink": "", "docType": "Misc Correspondence", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}}, "summary": "", "isEnglishLanguage": true, "isHandwritten": true}, {"docName": "", "docLink": "", "docType": "Other", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "19660607", "gender": "f", "ssn": "*********", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "phoneNumber": "+19999999999", "emailAddress": "<EMAIL>", "employeeId": ""}}, "summary": "", "isEnglishLanguage": true, "isHandwritten": true}, {"docName": "", "docLink": "", "docType": "Physician <PERSON> (HCFA)", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docReceivedDate": "", "senderName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "dateSubmitted": "", "carrier": {"carrierName": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}}, "claimant": {"firstName": "Patient name", "lastName": "<PERSON><PERSON>", "middleName": "G", "dateOfBirth": "19660607", "gender": "f/m. This is a checkbox (Patient Sex)", "phoneNumber": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "relatedCondition": {"employmentFlag": "This is field - Is patient’s condition related to", "autoAccidentFlag": "", "otherAccidentFlag": "", "claimCodes": " 10d - Claim codes"}, "relationshipToInsured": "Patient relationship to <PERSON><PERSON><PERSON>", "signature": " Patient`s or Authorized person signature", "dateSigned": " Date of Patient`s or Authorized person signature", "reservedForNUCCUse": "", "additionalClaimInfo": "Additional Claim Information "}, "insurance": {"insuranceType": " MEDICARE / MEDICAID / TRICARE / CHAMPVA / GROUP HEALTH PLAN / FECA BLK LUNG / OTHER. This is a checkbox", "insuranceId": "Insured’s I.D. Number", "insurancePlanName": "Insured’s Insurance Plan Name or Program Name"}, "insured": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "dateOfBirth": "19660607", "gender": "f", "phoneNumber": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "policyGroup": "Insured’s Policy Group or FECA Number", "otherClaimId": " Insured’s Other Claim ID", "anotherHealthBenefitPlanFlag": " Is there another Health Benefit Plan?", "signature:": "Insured`s or Authorized person signature"}, "otherInsured": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "policyGroup": "Other Insured’s policy or group number", "reservedNuccUseb": "Other Insured’s Reserved for NUCC Use b", "reservedNuccUsec": "Other Insured’s Reserved for NUCC Use c", "insurancePlanName": "Other Insured’s Insurance Plan Name or Program Name"}, "physician": [{"type": "referring", "name": "", "npi": "NPI of Referring Provider or Other Source", "otherId": "OtherID of Referring Provider or Other Source", "credentials": ""}, {"type": "billing", "name": "", "npi": "Billing Provider Info and Phone: alphanumeric field 1: NPI", "otherId": "Billing Provider Info and Phone: alphanumeric field 2: OtherId number", "credentials": "", "phoneNumber": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}}], "facility": {"facilityName": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "npi": "Service Facility Location Informatione: alphanumeric field 1: NPI", "otherId": "Service Facility Location Information: alphanumeric field 2: OtherId number"}, "diagnosis": [{"icdInd": "", "diagnosisCode": "Diagnosis or Nature of Illness or Injury"}], "reference": {"code": "Resubmission Code", "originalRefNumber": "Resubmission Original Ref. No."}, "priorAuthNumber": "Prior Authorization Number", "federalTaxIdNo": "Federal Tax ID Number", "federalTaxIdType": "Federal Tax ID Type (Checkbox)", "currentIllnessDate": {"date": "Date of Current Illness, Injury or Pregnancy (LMP)", "qual": ""}, "otherDate": {"date": "Other Date ", "qual": ""}, "unableToWorkDates": {"from": "Dates Patient Unable to Work in Current Occupation", "to": ""}, "hospitalizationDates": {"from": "Hospitalization Dates Related to Current Services", "to": ""}, "billing": [{"datesOfService": {"from": "Date(s) of Service: From", "to": "Date(s) of Service: To"}, "placeOfService": "", "emergencyFlag": "EMG", "procedureCode": "CPT/HCPCS codes", "diagnosisPointer": "", "modifier": "", "charges": "", "daysOrUnits": "", "epsdtFamilyPlan": "", "idQual": "", "renderingProviderID": "", "renderingProviderNpi": ""}], "outsideLab": {"outsideLabFlag": "", "charges": ""}, "totalChange": "", "ammountPaid": "", "receivedForNUCCUse": "", "patientAccountNumber": "<PERSON><PERSON>’s Account No.", "acceptAssigmentFlag": "Accept Assignment (Checkbox)", "physicianSignature": {"firstName": "Signature of Physician or Supplier Including Degrees or Credential", "lastName": "<PERSON><PERSON>", "middleName": "G", "id": "", "date": ""}}}, {"docName": "", "docLink": "", "docType": "Hospital Bill (UB)", "docConfidence": "", "pageRefStart": "", "pageRefEnd": "", "pagesRef": [[]], "pagesRefOrigin": [[]], "isDuplicated": "", "claimNumber": "", "metaData": {"docDate": "", "docReceivedDate": "", "senderName": "", "patientName": "", "claim": {"claimNumber": "", "dateOfInjuryFrom": "********", "dateOfInjuryThrough": "********", "jurisState": ""}, "claimant": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "suffix": "", "dateOfBirth": "Patient Birthdate", "gender": "Patient Sex", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "relationshipToInsured": "<PERSON><PERSON>'s Relation to Insured (<PERSON><PERSON>)"}, "patientControlNumber": "Patient Control Number", "medicalRecordNumber": "Medical Record Number", "taxID": "Federal Tax ID Number", "billType": "Type of Bill", "adminstrativeNecessaryDays": "", "statementCoverPeriod": {"from": "", "through": ""}, "physician": {"firstName": "", "lastName": "", "middleName": "", "credentials": "", "phoneNumber": "", "npi": "", "otherProviderIdentifier": "Other Provider Identifier (Other PRV ID)", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "physicianName": ""}, "payToProvider": {"firstName": "", "lastName": "", "middleName": "", "credentials": "", "phoneNumber": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}, "payToProviderName": ""}, "providers": [{"type": "Attending", "firstName": "", "lastName": "", "npi": "", "qual": ""}, {"type": "Operating ", "firstName": "", "lastName": "", "npi": "", "qual": ""}, {"type": "Other", "firstName": "", "lastName": "", "npi": "", "qual": ""}], "admission": {"date": "", "hours": "", "type": "", "source": ""}, "discharge": {"hours": "", "status": ""}, "condition": {"conditionCodes": ""}, "accident": {"state": "", "date": ""}, "occurance": {"code": "", "date": ""}, "occuranceSpan": {"code": "", "from": "", "through": ""}, "responsibleParty": {"firstName": "", "lastName": "", "middleName": "", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}}, "valueCodes": {"code": "", "amount": ""}, "insured": {"insuredName": "", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "G", "address": {"address1": "", "address2": "", "city": "", "state": "", "zip": ""}}, "insurance": {"insuranceId": "Insured’s I.D. Number", "insuranceGroupName": "Group Name (Insured's other group health coverage)", "insuranceGroupNumber": "Insurance Group Number"}, "billing": {"revenueCode": "", "description": "", "hcpc": "", "serviceDate": "", "serviceDayUnits": "", "serviceUnitsNo": "", "totalCharges": "", "nonCoveredCharges": "", "sumTotalCharges": "", "sumNonCoveredCharges": ""}, "payer": [{"payerName": "", "payerCarrierCode": ""}], "creationDate": "", "healthPlanId": "", "releaseOfInformation": "", "assignmentOfBenefits": "", "priorPayments": "", "estimatedAmountDue": "", "treatmentAuthCodes": "", "docControlNumber": "", "diagnosis": {"diagnosisCode": "", "principalDiagnosisAdmission": "", "otherDiagnosisCodes": "", "admittingDiagnosisCode": "", "patientReasonDiagnosis": "", "ppsCode": "", "externalCauseOfInjuryCode": ""}, "principalProcedure": {"code": "", "date": ""}, "otherProcedure": [{"code": "", "date": ""}], "employerName": "", "remarks": "", "codeCodeField": ""}}]}