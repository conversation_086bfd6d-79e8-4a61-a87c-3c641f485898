module "transfer_sftp" {
  source = "aws-ia/transfer-family/aws//modules/transfer-server"

  identity_provider = "SERVICE_MANAGED"
  protocols             = ["SFTP"]
  domain                = "S3"
  endpoint_type         = "PUBLIC"

  # Enable logging
  enable_logging    = true
  log_retention_days = 14

  tags = {
    Environment = "${var.environment}"
    Project     = "Atom"
  }
}

module "transfer_users" {
  source = "aws-ia/transfer-family/aws//modules/transfer-users"
  
  server_id = module.transfer_sftp.server_id
  s3_bucket_name = module.s3_sftp_bucket.s3_bucket_id
  s3_bucket_arn  = module.s3_sftp_bucket.s3_bucket_arn
  create_test_user = true
  kms_key_id = aws_kms_key.sftp_key.arn
}
resource "aws_kms_key" "sftp_key" {
  description             = "KMS key for SFTP S3 bucket encryption"
  deletion_window_in_days = 7

  tags = {
    Environment = var.environment
    Project     = "Atom"
  }
}

module "s3_sftp_bucket" {
  source = "terraform-aws-modules/s3-bucket/aws"
  version = "4.11.0"

  bucket = "atom-advantage-sftp-${var.environment}"
  acl    = "private"

  attach_policy                            = true
  policy                                   = data.aws_iam_policy_document.sftp_bucket_policy.json

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  versioning = {
    enabled = false
  }
}

data "aws_iam_policy_document" "sftp_bucket_policy" {
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.account_id}:root"]
    }

    actions = [
      "s3:*",
    ]
    resources = [
      "arn:aws:s3:::${element(split(".", module.s3_sftp_bucket.s3_bucket_bucket_domain_name), 0)}",
      "arn:aws:s3:::${element(split(".", module.s3_sftp_bucket.s3_bucket_bucket_domain_name), 0)}/*"
    ]
  } 
}

# IAM role for SFTP users
resource "aws_iam_role" "transfer_user_role" {
  name = "transfer-user-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "transfer.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Environment = var.environment
    Project     = "Atom"
  }
}

# IAM policy for SFTP users
resource "aws_iam_role_policy" "transfer_user_policy" {
  name = "transfer-user-policy"
  role = aws_iam_role.transfer_user_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket",
          "s3:GetBucketLocation",
          "s3:ListAllMyBuckets"
        ]
        Resource = [
          module.s3_sftp_bucket.s3_bucket_arn,
          "arn:aws:s3:::*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:DeleteObjectVersion",
          "s3:GetObjectVersion",
          "s3:GetObjectACL",
          "s3:PutObjectACL",
          "s3:ListBucketMultipartUploads",
          "s3:ListMultipartUploadParts",
          "s3:AbortMultipartUpload"
        ]
        Resource = "${module.s3_sftp_bucket.s3_bucket_arn}/*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
          "kms:DescribeKey"
        ]
        Resource = aws_kms_key.sftp_key.arn
      }
    ]
  })
}