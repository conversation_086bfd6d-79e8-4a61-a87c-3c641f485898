module "classifier_role_training" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "classifier-training"
  namespace         = "training"
  s3_bucket_names = {
    "models"  = module.ml_s3_bucket.s3_bucket_id
  }
}

module "metadata_extractor_role_training" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "metadata-extractor-training"
  namespace         = "training"
  s3_bucket_names = {
    "models"  = module.ml_s3_bucket.s3_bucket_id
  }
}

module "downloader_role_training" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "downloader-training"
  namespace         = "training"
  s3_bucket_names = {
    "packets" = module.s3_packets_bucket.s3_bucket_id
  }
}

module "qa_backend_role_training" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "qa-backend-training"
  namespace         = "training"
  s3_bucket_names = {
    "packets" = module.s3_packets_bucket.s3_bucket_id
  }
}

module "select_and_populate_role_training" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "select-and-populate-training"
  namespace         = "training"
  s3_bucket_names = {
    "packets" = module.s3_packets_bucket.s3_bucket_id
  }
}
