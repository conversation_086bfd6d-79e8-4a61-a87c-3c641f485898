variable "tenant" {}
variable "environment" {}
variable "account_id" {}
variable "cidr_block" {}
variable "region" {}
variable "enable_cf" { default = false }
variable "sns_endpoint" {
  type    = list(any)
  default = []
}
variable "enable_nat_gateway" {

}

variable "tags" {

}
variable "security_groups" {

}
variable "ssm_backend" {}
variable "rds_name" {}
variable "rds_username" {}
variable "rds_engine" {}
variable "rds_engine_version" {}
variable "apply_immediately" {}
variable "rds_allocated_storage" {}
variable "rds_instance_class" {}
variable "rds_port" {}
variable "rds_multi_az" {}
variable "rds_family" {}
variable "sso_admin_role" {}
variable "dns_zone" { 
  description = "The DNS zone name for the hosted zone"
  type        = string
}
variable "dns_delegations" {
  description = "DNS delegations configuration"
  type        = map(any)
  default     = {}
}

variable "dns_caa_records" {
  description = "CAA records for the DNS zone"
  type        = list(string)
  default     = null
}

variable "powerbi_proxy_rds_targets" {
  description = "A map of RDS instances to expose through the PowerBI proxy. The key is a unique name for the target, and the value is an object with listener_port, rds_hostname, and rds_port."
  type = map(object({
    listener_port = number
    rds_hostname  = string
    rds_port      = number
  }))
  default = {}
}

variable "is_powerbi_proxy_consumer" {
  description = "Set to true if this environment is a consumer of the PowerBI proxy service."
  type        = bool
  default     = false
}

variable "powerbi_proxy_service_name" {
  description = "The service name of the PowerBI proxy VPC Endpoint Service to connect to."
  type        = string
  default     = ""
}

variable "powerbi_proxy_consumer_account_arn" {
  description = "The ARN of the AWS Account (the consumer) that is allowed to connect to the endpoint service. e.g. arn:aws:iam::************:root"
  type        = string
  default     = ""
}