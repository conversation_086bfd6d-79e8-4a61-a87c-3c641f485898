module "qa_backend_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "qa-backend"
  namespace         = "apps"
  s3_bucket_names = {
    "packets" = module.s3_packets_bucket.s3_bucket_id
  }
}
module "downloader_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "downloader"
  namespace         = "apps"
}

module "uploader_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "uploader"
  namespace         = "apps"
}

module "splitter_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "splitter"
  namespace         = "apps"
}

module "qa_post_processor_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "qa-post-processor"
  namespace         = "apps"
}


module "sender_processor_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "sender"
  namespace         = "apps"
}

module "pgbouncer_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "pgbouncer"
  namespace         = "apps"
}

module "classifier_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "classifier"
  namespace         = "apps"
  s3_bucket_names = {
    "models"  = module.ml_s3_bucket.s3_bucket_id
  }
}

module "metadata_extractor_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "metadata-extractor"
  namespace         = "apps"
  s3_bucket_names = {
    "models"  = module.ml_s3_bucket.s3_bucket_id
  }
}

module "metadata_post_processor_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "metadata-post-processor"
  namespace         = "apps"
  s3_bucket_names = {
    "models"  = module.ml_s3_bucket.s3_bucket_id
  }
}

module "loki_role" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "loki"
  namespace         = "monitoring"
  s3_bucket_names = {
    "loki"  = module.s3_loki_bucket.s3_bucket_id
  }
}