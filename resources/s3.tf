module "ml_s3_bucket" {
  source = "terraform-aws-modules/s3-bucket/aws"
  version = "4.11.0"
  bucket = "atom-advantage-ml-data-${var.environment}"
  acl    = "private"

  attach_policy                            = true
  policy                                   = data.aws_iam_policy_document.ml_bucket_policy.json

  control_object_ownership = true
  object_ownership         = "ObjectWriter"
  versioning = {
    enabled = false
  }
}

data "aws_iam_policy_document" "ml_bucket_policy" {
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.account_id}:root"]
    }

    actions = [
      "s3:ListBucket",
      "s3:PutObject",
      "s3:GetObject",
      "s3:DeleteObject"
    ]

    resources = [
      "arn:aws:s3:::${element(split(".", module.ml_s3_bucket.s3_bucket_bucket_domain_name), 0)}",
      "arn:aws:s3:::${element(split(".", module.ml_s3_bucket.s3_bucket_bucket_domain_name), 0)}/*"
    ]
  } 
}   


### Backend S3 Bucket ### 

module "s3_packets_bucket" {
  source = "terraform-aws-modules/s3-bucket/aws"
  version = "4.11.0"
  bucket = "atom-advantage-packets-${var.environment}"
  acl    = "private"

  attach_policy                            = true
  policy                                   = data.aws_iam_policy_document.packets_bucket_policy.json

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  versioning = {
    enabled = false
  }

  cors_rule = [
    {
      allowed_headers = ["Authorization"]
      allowed_methods = ["GET", "HEAD"]
      allowed_origins = ["*"]
      expose_headers  = ["Access-Control-Allow-Origin"]
      max_age_seconds = 3000
    }
  ]

}

data "aws_iam_policy_document" "packets_bucket_policy" {
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.account_id}:root"]
    }

    actions = [
      "s3:*",
    ]
    resources = [
      "arn:aws:s3:::${element(split(".", module.s3_packets_bucket.s3_bucket_bucket_domain_name), 0)}",
      "arn:aws:s3:::${element(split(".", module.s3_packets_bucket.s3_bucket_bucket_domain_name), 0)}/*"
    ]
  } 
}

### Training Control group S3 Bucket ### 

module "s3_training_control_group_bucket" {
  source = "terraform-aws-modules/s3-bucket/aws"
  version = "4.11.0"
  bucket = "atom-advantage-training-control-group-${var.environment}"
  acl    = "private"

  attach_policy                            = true
  policy                                   = data.aws_iam_policy_document.training_control_group_bucket_policy.json

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  versioning = {
    enabled = false
  }
}

data "aws_iam_policy_document" "training_control_group_bucket_policy" {
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.account_id}:root"]
    }

    actions = [
      "s3:*",
    ]
    resources = [
      "arn:aws:s3:::${element(split(".", module.s3_training_control_group_bucket.s3_bucket_bucket_domain_name), 0)}",
      "arn:aws:s3:::${element(split(".", module.s3_training_control_group_bucket.s3_bucket_bucket_domain_name), 0)}/*"
    ]
  } 
}

###  Loki S3 Bucket ### 
module "s3_loki_bucket" {
  source = "terraform-aws-modules/s3-bucket/aws"
  version = "4.11.0"
  bucket = "atom-advantage-loki-${var.environment}"
  acl    = "private"

  attach_policy                            = true
  policy                                   = data.aws_iam_policy_document.loki_bucket_policy.json

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  versioning = {
    enabled = false
  }
}

data "aws_iam_policy_document" "loki_bucket_policy" {
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.account_id}:root"]
    }

    actions = [
      "s3:*",
    ]
    resources = [
      "arn:aws:s3:::${element(split(".", module.s3_loki_bucket.s3_bucket_bucket_domain_name), 0)}",
      "arn:aws:s3:::${element(split(".", module.s3_loki_bucket.s3_bucket_bucket_domain_name), 0)}/*"
    ]
  } 
}