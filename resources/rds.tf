module "rds" {
  source               = "../local-modules/rds"
  identifier           = var.rds_name
  name                 = var.rds_name
  parameter_group_name = var.rds_name
  username             = var.rds_username
  engine               = var.rds_engine
  engine_version       = var.rds_engine_version
  major_engine_version = var.rds_engine_version
  family               = var.rds_family
  port                 = var.rds_port
  multi_az             = var.rds_multi_az
  apply_immediately    = var.apply_immediately
  allocated_storage    = var.rds_allocated_storage
  instance_class       = var.rds_instance_class
  security_group_ids   = [module.sg.id["rds"]]
  subnet_ids           = module.vpc.private_subnets

  parameters = [
    {
      apply_method = "pending-reboot"
      name         = "rds.logical_replication"
      value        = "1"
    },
    {
      apply_method = "immediate"
      name         = "rds.force_ssl"
      value        = "0"
    }
  ]
}


###############################################################################
# Store the PostgreSQL DSN in SSM Parameter Store
###############################################################################

resource "aws_ssm_parameter" "postgres_connection_string" {
  name        = "/rds/${var.rds_name}/postgres_dsn"
  description = "PostgreSQL connection string for ${var.rds_name}"
  type        = "SecureString"

  # Build the DSN in one expression:
  #
  #   postgresql://<user>:<url‑encoded‑password>@<host>:<port>/<dbname>
  #
  value = format(
    "postgresql://%s:%s@%s:%s/%s",
    module.rds.username,
    module.rds.password,
    module.rds.host,
    module.rds.port,
    module.rds.name
  )

  overwrite = true

  tags = merge(var.tags, {
    ManagedBy = "terraform"
    Service   = "rds"
  })

  depends_on = [module.rds]
}