resource "aws_instance" "bastion" {
  ami                    = "ami-04f167a56786e4b09"
  instance_type          = "t3.micro"
  key_name               = aws_key_pair.ec2.key_name
  monitoring             = true
  vpc_security_group_ids = [module.sg.id["management"]]
  subnet_id              = "${tostring(module.vpc.public_subnets_ids_az["${var.region}a"])}"

  root_block_device {
    delete_on_termination = false
    volume_size           = 20
    volume_type           = "gp3"
    tags = merge(
      var.tags,
        {
          Name = "bastion-${var.environment}"
          Snapshot = "false"
        }
      )
    }

  lifecycle {
    ignore_changes = [
      ami,
    ]
  }

  tags = merge(
    var.tags,
    {
        Name = "bastion-${var.environment}"
    }
  )
}

resource "aws_eip" "bastion" {
  instance = aws_instance.bastion.id
  domain   = "vpc"
}