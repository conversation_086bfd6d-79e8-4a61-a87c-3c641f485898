module "dns" {
  source = "../local-modules/dns"
  
  dns_zone    = var.dns_zone
  environment = var.environment
  
  # Optional variables with defaults
  delegations = var.dns_delegations != null ? var.dns_delegations : {}
  caa_records = var.dns_caa_records != null ? var.dns_caa_records : [
    "0 issue \"amazon.com\"",
    "0 issue \"amazonaws.com\"",
    "0 issue \"amazontrust.com\"",
    "0 issue \"awstrust.com\"",
    "0 issuewild \"amazon.com\"",
    "0 issuewild \"amazonaws.com\"",
    "0 issuewild \"amazontrust.com\"",
    "0 issuewild \"awstrust.com\"",
    "0 issuewild \"letsencrypt.org\"",
    "0 issue \"letsencrypt.org\"",
  ]
}

# Outputs from the DNS module
output "dns_zone_nameservers" {
  description = "Name servers for the DNS zone"
  value       = module.dns.nameservers
}

output "dns_zone_id" {
  description = "The hosted zone ID"
  value       = module.dns.zone_id
}

output "dns_domain_name" {
  description = "The domain name of the hosted zone"
  value       = module.dns.domain_name
}
