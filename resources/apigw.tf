module "api_message_queue" {
  source = "terraform-aws-modules/sqs/aws"
  version = "~> 4.0"

  name = "atom-advantage-api-messages-${var.environment}"

  # Queue configuration
  delay_seconds              = 0
  max_message_size          = 262144
  message_retention_seconds = 1209600  # 14 days
  receive_wait_time_seconds = 0
  visibility_timeout_seconds = 30

  # Dead letter queue configuration
  create_dlq = true
  dlq_name   = "atom-advantage-api-messages-dlq-${var.environment}"
  dlq_delay_seconds              = 0
  dlq_message_retention_seconds = 1209600
  dlq_receive_wait_time_seconds = 0
  dlq_visibility_timeout_seconds = 30

  # Redrive policy - let the module handle this automatically when create_dlq = true
  redrive_policy = {
    maxReceiveCount = 3
  }

  tags = var.tags
}

# IAM Role for API Gateway to access SQS
resource "aws_iam_role" "api_gateway_sqs_role" {
  name = "atom-advantage-api-gateway-sqs-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for API Gateway to send messages to SQS
resource "aws_iam_policy" "api_gateway_sqs_policy" {
  name        = "atom-advantage-api-gateway-sqs-policy-${var.environment}"
  description = "Policy for API Gateway to send messages to SQS"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sqs:SendMessage",
          "sqs:GetQueueAttributes"
        ]
        Resource = [
          module.api_message_queue.queue_arn,
          module.api_message_queue.dead_letter_queue_arn
        ]
      }
    ]
  })

  tags = var.tags
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "api_gateway_sqs_policy_attachment" {
  role       = aws_iam_role.api_gateway_sqs_role.name
  policy_arn = aws_iam_policy.api_gateway_sqs_policy.arn
}

resource "aws_acm_certificate" "api_cert" {
  domain_name       = "api.dev.atomadvantage.ai"
  validation_method = "DNS"

  subject_alternative_names = [
    "*.dev.atomadvantage.ai"
  ]

  lifecycle {
    create_before_destroy = true
  }

  tags = var.tags
}

# API Gateway REST API (v1) for proper SQS integration support
resource "aws_api_gateway_rest_api" "api_gateway" {
  name        = "atom-advantage-api-${var.environment}"
  description = "REST API Gateway for Atom Advantage that forwards messages to SQS"

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = var.tags
}

resource "aws_api_gateway_resource" "messages" {
  rest_api_id = aws_api_gateway_rest_api.api_gateway.id
  parent_id   = aws_api_gateway_rest_api.api_gateway.root_resource_id
  path_part   = "messages"
}

resource "aws_api_gateway_method" "post_messages" {
  rest_api_id   = aws_api_gateway_rest_api.api_gateway.id
  resource_id   = aws_api_gateway_resource.messages.id
  http_method   = "POST"
  authorization = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "sqs_integration" {
  rest_api_id = aws_api_gateway_rest_api.api_gateway.id
  resource_id = aws_api_gateway_resource.messages.id
  http_method = aws_api_gateway_method.post_messages.http_method

  type                    = "AWS"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:sqs:path/${var.account_id}/${module.api_message_queue.queue_name}"
  credentials             = aws_iam_role.api_gateway_sqs_role.arn

  request_parameters = {
    "integration.request.header.Content-Type" = "'application/x-www-form-urlencoded'"
  }

  request_templates = {
    "application/json" = "Action=SendMessage&MessageBody=$util.urlEncode($input.body)"
  }
}

resource "aws_api_gateway_method_response" "response_200" {
  rest_api_id = aws_api_gateway_rest_api.api_gateway.id
  resource_id = aws_api_gateway_resource.messages.id
  http_method = aws_api_gateway_method.post_messages.http_method
  status_code = "200"

  response_models = {
    "application/json" = "Empty"
  }
}

resource "aws_api_gateway_integration_response" "response_200" {
  rest_api_id = aws_api_gateway_rest_api.api_gateway.id
  resource_id = aws_api_gateway_resource.messages.id
  http_method = aws_api_gateway_method.post_messages.http_method
  status_code = aws_api_gateway_method_response.response_200.status_code

  response_templates = {
    "application/json" = jsonencode({
      message   = "Message sent to queue successfully"
      timestamp = "$context.requestTime"
      requestId = "$context.requestId"
    })
  }
}

resource "aws_api_gateway_deployment" "deployment" {
  depends_on = [
    aws_api_gateway_integration.sqs_integration,
    aws_api_gateway_integration_response.response_200
  ]

  rest_api_id = aws_api_gateway_rest_api.api_gateway.id
  stage_name  = var.environment

  # Force redeployment when method configuration changes
  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.messages.id,
      aws_api_gateway_method.post_messages.id,
      aws_api_gateway_integration.sqs_integration.id,
      aws_api_gateway_method_response.response_200.id,
      aws_api_gateway_integration_response.response_200.id,
      # Force redeployment with simplified SQS integration
      "sqs-simple-v1"
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Additional SQS queue policy to allow API Gateway access
resource "aws_sqs_queue_policy" "api_message_queue_policy" {
  queue_url = module.api_message_queue.queue_id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowAPIGatewayToSendMessages"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
        Action = [
          "sqs:SendMessage"
        ]
        Resource = module.api_message_queue.queue_arn
        Condition = {
          StringEquals = {
            "aws:SourceAccount" = var.account_id
          }
        }
      }
    ]
  })
}

# API Key for customer access
resource "aws_api_gateway_api_key" "customer_api_key" {
  name        = "atom-advantage-customer-key-${var.environment}"
  description = "API key for customer access to Atom Advantage API"
  enabled     = true

  tags = var.tags
}

# Usage Plan to control API access
resource "aws_api_gateway_usage_plan" "api_usage_plan" {
  name        = "atom-advantage-usage-plan-${var.environment}"
  description = "Usage plan for Atom Advantage API"

  api_stages {
    api_id = aws_api_gateway_rest_api.api_gateway.id
    stage  = aws_api_gateway_deployment.deployment.stage_name
  }

  # Rate limiting
  throttle_settings {
    rate_limit  = 1000  # requests per second
    burst_limit = 2000  # burst capacity
  }

  # Quota settings
  quota_settings {
    limit  = 100000  # requests per month
    period = "MONTH"
  }

  tags = var.tags
}

# Associate API key with usage plan
resource "aws_api_gateway_usage_plan_key" "api_key_association" {
  key_id        = aws_api_gateway_api_key.customer_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.api_usage_plan.id
}

# IAM Role for API Gateway CloudWatch Logs
resource "aws_iam_role" "api_gateway_cloudwatch_role" {
  name = "atom-advantage-api-gateway-cloudwatch-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for API Gateway CloudWatch Logs
resource "aws_iam_policy" "api_gateway_cloudwatch_policy" {
  name        = "atom-advantage-api-gateway-cloudwatch-policy-${var.environment}"
  description = "Policy for API Gateway to write logs to CloudWatch"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams",
          "logs:PutLogEvents",
          "logs:GetLogEvents",
          "logs:FilterLogEvents"
        ]
        Resource = "arn:aws:logs:${var.region}:${var.account_id}:*"
      }
    ]
  })

  tags = var.tags
}

# Attach CloudWatch policy to role
resource "aws_iam_role_policy_attachment" "api_gateway_cloudwatch_policy_attachment" {
  role       = aws_iam_role.api_gateway_cloudwatch_role.name
  policy_arn = aws_iam_policy.api_gateway_cloudwatch_policy.arn
}

# CloudWatch Log Group for API Gateway logs
resource "aws_cloudwatch_log_group" "api_gateway_logs" {
  name              = "/aws/apigateway/atom-advantage-api-${var.environment}"
  retention_in_days = 14

  tags = var.tags
}

# API Gateway Account settings (required for CloudWatch logging)
resource "aws_api_gateway_account" "main" {
  cloudwatch_role_arn = aws_iam_role.api_gateway_cloudwatch_role.arn
}

