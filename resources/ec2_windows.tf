resource "aws_instance" "windows_server" {
  ami                    = "ami-070f90bab72874c6f"
  instance_type          = "t3.medium" 
  monitoring             = true
  key_name               = aws_key_pair.ec2.key_name
  vpc_security_group_ids = [module.sg.id["management"]]
  subnet_id              = "${tostring(module.vpc.public_subnets_ids_az["${var.region}a"])}"
  
  get_password_data      = true

  root_block_device {
    delete_on_termination = false
    volume_size           = 50
    volume_type           = "gp3"
    tags = merge(
      var.tags,
      {
        Name = "windows-server-${var.environment}"
        Snapshot = "false"
      }
    )
  }

  lifecycle {
    ignore_changes = [
      ami,
    ]
  }

  tags = merge(
    var.tags,
    {
      Name = "windows-server-${var.environment}"
    }
  )
}

resource "aws_eip" "windows_server" {
  instance = aws_instance.windows_server.id
  domain   = "vpc"
}

resource "aws_ssm_parameter" "windows_password" {
  name        = "/ec2/windows-server-${var.environment}/password"
  description = "Windows administrator password for windows-server-${var.environment}"
  type        = "SecureString"
  value       = "placeholder-replace-after-creation"
  
  tags = merge(
    var.tags,
    {
      Name = "windows-server-password-${var.environment}"
    }
  )
  
  lifecycle {
    ignore_changes = [value]
  }
}