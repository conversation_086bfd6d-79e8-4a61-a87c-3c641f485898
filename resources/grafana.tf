module "managed_grafana" {
  source = "terraform-aws-modules/managed-service-grafana/aws"
  version = "2.1.1"

  name = "amg-${var.tenant}-${var.environment}"
  associate_license         = false
  description               = "AWS Managed Grafana service for ${var.tenant}-${var.environment}"
  account_access_type       = "CURRENT_ACCOUNT"
  authentication_providers  = ["AWS_SSO"]
  permission_type           = "SERVICE_MANAGED"
  data_sources              = ["CLOUDWATCH"]
  notification_destinations = ["SNS"]
  grafana_version           = "10.4"

  security_group_rules = {
    egress = {
      description = "Allow egress"
      from_port   = -1
      to_port     = -1
      protocol    = "All"
      cidr_blocks = ["0.0.0.0/0"]
    }
  }
  vpc_configuration = {
    subnet_ids = module.vpc.private_subnet_ids
  }

  # Workspace IAM role
  create_iam_role                = true
  iam_role_name                  = "amg-${var.tenant}-${var.environment}"
  use_iam_role_name_prefix       = true
  iam_role_path                  = "/grafana/"
  iam_role_force_detach_policies = true
  iam_role_max_session_duration  = 7200
  iam_role_tags                  = { role = true }

  role_associations = {
    "ADMIN" = {
      "group_ids" = ["c17bf500-6081-702f-d1a2-60290b09c14b"]
    },
    "EDITOR" = {
      "group_ids" = ["b15b3590-4011-70d3-ad52-62a8fdcd7617"]
    },
    "VIEWER" = {
      "group_ids" = ["714b2540-b051-7020-4a54-958949d06060"]
    }
  }
  tags = {
    Terraform   = "true"
    Environment = "${var.environment}"
  }
}