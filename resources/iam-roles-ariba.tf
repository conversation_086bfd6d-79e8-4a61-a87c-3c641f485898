module "classifier_role_ariba" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "classifier-ariba"
  namespace         = "ariba"
  s3_bucket_names = {
    "models"  = module.ml_s3_bucket.s3_bucket_id
  }
}

module "metadata_extractor_role_ariba" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "metadata-extractor-ariba"
  namespace         = "ariba"
  s3_bucket_names = {
    "models"  = module.ml_s3_bucket.s3_bucket_id
  }
}

module "downloader_role_ariba" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "downloader-ariba"
  namespace         = "ariba"
  s3_bucket_names = {
    "packets" = module.s3_packets_bucket.s3_bucket_id
  }
}

module "qa_backend_role_ariba" {
  source            = "../local-modules/iam/application-role"
  oidc_provider_arn = module.eks.oidc_provider_arn
  oidc_provider_url = module.eks.oidc_provider
  cluster_name      = module.eks.cluster_name
  serviceaccount    = "qa-backend-ariba"
  namespace         = "ariba"
  s3_bucket_names = {
    "packets" = module.s3_packets_bucket.s3_bucket_id
  }
}
