# /resources/privatelink.tf

# A single NLB to act as the proxy gateway.
# It is only created if there is at least one RDS target defined.
resource "aws_lb" "powerbi_proxy_nlb" {
  count = length(var.powerbi_proxy_rds_targets) > 0 ? 1 : 0

  name               = "pwrbi-px-${var.environment}-nlb"
  internal           = true
  load_balancer_type = "network"
  subnets            = module.vpc.private_subnets

  tags = merge(var.tags, {
    Name = "powerbi-proxy-${var.tenant}-${var.environment}-nlb"
  })
}

# Dynamically look up the RDS instance's private IP from its public hostname.
data "dns_a_record_set" "rds_ip" {
  for_each = var.powerbi_proxy_rds_targets
  host     = each.value.rds_hostname
}

# Create a dedicated Target Group for each RDS instance in the map.
resource "aws_lb_target_group" "powerbi_proxy_tg" {
  for_each = var.powerbi_proxy_rds_targets

  name        = "pwrbi-px-${replace(each.key, "_", "-")}-tg"
  port        = each.value.rds_port
  protocol    = "TCP"
  vpc_id      = module.vpc.vpc_id
  target_type = "ip"

  tags = merge(var.tags, {
    Name = "pwrbi-px-${var.tenant}-${each.key}-tg"
  })
}

# Attach the resolved private IP to its corresponding Target Group.
resource "aws_lb_target_group_attachment" "powerbi_proxy_tg_attachment" {
  for_each = var.powerbi_proxy_rds_targets

  target_group_arn = aws_lb_target_group.powerbi_proxy_tg[each.key].arn
  target_id        = data.dns_a_record_set.rds_ip[each.key].addrs[0]
  port             = each.value.rds_port
}

# Create a dedicated Listener on the NLB for each RDS instance in the map.
resource "aws_lb_listener" "powerbi_proxy_listener" {
  for_each = var.powerbi_proxy_rds_targets

  load_balancer_arn = aws_lb.powerbi_proxy_nlb[0].arn
  port              = each.value.listener_port
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.powerbi_proxy_tg[each.key].arn
  }
}

# Create the single VPC Endpoint Service that exposes the NLB.
# This is only created if there are targets defined.
resource "aws_vpc_endpoint_service" "powerbi_proxy_vpce_svc" {
  count = length(var.powerbi_proxy_rds_targets) > 0 ? 1 : 0

  acceptance_required        = false
  network_load_balancer_arns = [aws_lb.powerbi_proxy_nlb[0].arn]
  allowed_principals         = [var.powerbi_proxy_consumer_account_arn]

  tags = merge(var.tags, {
    Name = "powerbi-proxy-${var.tenant}-${var.environment}-vpce-svc"
  })
}

# For the consumer environment, create the VPC Endpoint to connect to the service.
resource "aws_vpc_endpoint" "powerbi_proxy_vpce" {
  count = var.is_powerbi_proxy_consumer ? 1 : 0

  vpc_id             = module.vpc.vpc_id
  service_name       = var.powerbi_proxy_service_name
  vpc_endpoint_type  = "Interface"
  subnet_ids         = module.vpc.private_subnets
  security_group_ids = [module.sg.id["management"]]

  tags = merge(var.tags, {
    Name = "powerbi-proxy-${var.tenant}-${var.environment}-vpce"
  })
} 