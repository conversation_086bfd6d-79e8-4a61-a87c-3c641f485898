# API Gateway outputs
output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = "https://${aws_api_gateway_rest_api.api_gateway.id}.execute-api.${var.region}.amazonaws.com/${var.environment}"
}

output "api_gateway_id" {
  description = "ID of the API Gateway"
  value       = aws_api_gateway_rest_api.api_gateway.id
}

# API Key outputs
output "customer_api_key_id" {
  description = "ID of the customer API key"
  value       = aws_api_gateway_api_key.customer_api_key.id
}

output "customer_api_key_value" {
  description = "Value of the customer API key (sensitive)"
  value       = aws_api_gateway_api_key.customer_api_key.value
  sensitive   = true
}

# Usage Plan outputs
output "usage_plan_id" {
  description = "ID of the API usage plan"
  value       = aws_api_gateway_usage_plan.api_usage_plan.id
}

# SQS Queue outputs
output "message_queue_url" {
  description = "URL of the SQS message queue"
  value       = module.api_message_queue.queue_id
}

output "message_queue_arn" {
  description = "ARN of the SQS message queue"
  value       = module.api_message_queue.queue_arn
}

output "powerbi_proxy_vpce_service_name" {
  description = "The service name of the VPC endpoint service for the PowerBI proxy"
  value       = try(aws_vpc_endpoint_service.powerbi_proxy_vpce_svc[0].service_name, "")
}

output "powerbi_proxy_vpce_dns_name" {
  description = "The DNS name of the VPC endpoint for the PowerBI proxy. Use this to connect from the consumer VPC."
  value       = try(aws_vpc_endpoint.powerbi_proxy_vpce[0].dns_entry[0].dns_name, "")
}
