module "eks" {
  source  = "terraform-aws-modules/eks/aws"
  version = "~> 20.31"

  cluster_name                             = "${var.tenant}-${var.region}"
  cluster_version                          = "1.32"
  cluster_endpoint_public_access           = true
  enable_cluster_creator_admin_permissions = true
  cluster_enabled_log_types                = ["authenticator"]
  kms_key_administrators                   = ["arn:aws:iam::${var.account_id}:root", "arn:aws:iam::${var.account_id}:role/OrganizationAccountAccessRole", "arn:aws:iam::${var.account_id}:role/aws-reserved/sso.amazonaws.com/${var.region}/AWSReservedSSO_AdministratorAccess_${var.sso_admin_role}"]
  cluster_addons = {
    coredns = {
      most_recent = true
    }
    kube-proxy = {
      most_recent = true
    }
    vpc-cni = {
      most_recent      = true
      before_compute   = true
    }
  }

  vpc_id                   = module.vpc.id
  subnet_ids               = module.vpc.public_subnet_ids
  control_plane_subnet_ids = module.vpc.private_subnet_ids

  # EKS Managed Node Group(s)
  eks_managed_node_group_defaults = {
    instance_types = ["t3.medium"]
  }

  eks_managed_node_groups = {
    qa-tool = {
      min_size     = 0
      max_size     = 6
      desired_size = 1
      iam_role_additional_policies = {
        EbsPolicy = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
      }
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      subnet_ids     = module.vpc.private_subnet_ids
      taints = {
        qa-tool = {
          key    = "qa-tool"
          value  = "true"
          effect = "NO_EXECUTE"
        }
      }
      labels = {
        assignment = "qa-tool"
      }
    }

    ariba = {
      min_size     = 0
      max_size     = 6
      desired_size = 1
      iam_role_additional_policies = {
        EbsPolicy = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
      }
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      subnet_ids     = module.vpc.private_subnet_ids
      taints = {
        qa-tool = {
          key    = "ariba"
          value  = "true"
          effect = "NO_EXECUTE"
        }
      }
      labels = {
        assignment = "ariba"
      }
    }

    training = {
      min_size     = 0
      max_size     = 6
      desired_size = 3
      iam_role_additional_policies = {
        EbsPolicy = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
      }
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      subnet_ids     = module.vpc.private_subnet_ids
      taints = {
        qa-tool = {
          key    = "training"
          value  = "true"
          effect = "NO_EXECUTE"
        }
      }
      labels = {
        assignment = "training"
      }
    }

    controller = {
      min_size     = 1
      max_size     = 3
      desired_size = 1
      iam_role_additional_policies = {
        EbsPolicy        = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
        AutoscalerPolicy = aws_iam_policy.iam_policy_cluster_autoscaler.arn
      }
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      subnet_ids     = module.vpc.private_subnet_ids
      labels = {
        assignment = "controller"
      }
      subnet_ids = [module.vpc.private_subnets_ids_az["${var.region}c"]]
    }

    ml-workloads = {
      min_size     = 3
      max_size     = 12
      desired_size = 4
      ami_type     = "AL2_x86_64_GPU"
      block_device_mappings = {
        xvda = {
          device_name = "/dev/xvda"
          ebs = {
            volume_size = 100
          }
        }
      }
      iam_role_additional_policies = {
        EbsPolicy = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
      }
      instance_types = ["g5.xlarge"]
      capacity_type  = "ON_DEMAND"
      subnet_ids     = module.vpc.private_subnet_ids
      taints = {
        ml-workloads = {
          key    = "ml-workloads"
          value  = "true"
          effect = "NO_EXECUTE"
        }
      }
      labels = {
        assignment = "ml-workloads"
      }
    }

    monitoring = {
      min_size     = 1
      max_size     = 3
      desired_size = 1
      iam_role_additional_policies = {
        EbsPolicy = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
      }
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      subnet_ids     = [module.vpc.private_subnets_ids_az["${var.region}c"]]
      taints = {
        monitoring = {
          key    = "monitoring"
          value  = "true"
          effect = "NO_EXECUTE"
        }
      }
      labels = {
        assignment = "monitoring"
      }
    }

  }

  tags = {
    Environment = "dev"
    Terraform   = "true"
  }
  access_entries = {
    sso_admin = {
      principal_arn = "arn:aws:iam::${var.account_id}:role/aws-reserved/sso.amazonaws.com/${var.region}/AWSReservedSSO_AdministratorAccess_${var.sso_admin_role}"
      policy_associations = {
        admin_policy = {
          policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSClusterAdminPolicy"
          access_scope = {
            type = "cluster"
          }
        }
      }
    },
  }
}
