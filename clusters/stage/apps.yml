apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: apps
  namespace: flux-system
spec:
  interval: 10m0s
  retryInterval: 1m
  sourceRef:
    kind: GitRepository
    name: flux-system
  path: ../../../apps/overlays/stage
  prune: true
  wait: true
  timeout: 5m0s
---
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: ariba
  namespace: flux-system
spec:
  interval: 10m0s
  retryInterval: 1m
  sourceRef:
    kind: GitRepository
    name: flux-system
  path: ../../../apps/overlays/stage-ariba
  prune: true
  wait: true
  timeout: 5m0s
---
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: training
  namespace: flux-system
spec:
  interval: 10m0s
  retryInterval: 1m
  sourceRef:
    kind: GitRepository
    name: flux-system
  path: ../../../apps/overlays/stage-training
  prune: true
  wait: true
  timeout: 5m0s