import { StringSchema } from "yup";
import { PageRanges } from "../../store/queries/chunks/types/Chunk.type.ts";

export interface Field {
  valid: boolean | string | null;
  value: string;
  required: boolean;
  confidence: number | null;
}

export type NestedField = Field | Field[] | { [key: string]: NestedField };

export interface NamingData {
  [key: string]: Field;
}

export interface MetaData {
  [key: string]: NestedField;
}

export interface MetadataToUpdate {
  docType: string;
  docSubType?: string;
  docName: string;
  docLink: string;
  docConfidence: number;
  pageRefStart: number;
  pageRefEnd: number;
  claimNumber: string;
  isDuplicated: boolean;
  pagesRefOrig: PageRanges;
  namingData: NamingData;
  metaData: MetaData;
  pagesRef: PageRanges;
}

export interface PackageToUpdate {
  metadata: MetadataToUpdate[];
  predictions: EntryListItem[];
  comment: string | null;
}

export interface FieldProps {
  index?: number;
  base: string;
  name: string;
  label: string;
  level?: number;
  treatment?: boolean;
  validation?: StringSchema;
}
