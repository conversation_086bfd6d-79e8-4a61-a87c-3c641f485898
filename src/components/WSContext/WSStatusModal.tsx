import { Box, Modal } from "@mui/material";
import { FC, useCallback, useEffect, useMemo, useRef } from "react";
import { useAuthContext } from "../../hooks/useAuth.ts";
import { useLogout } from "../../store/queries/users/users.query.ts";
import { WSClose, WSCloseStatus, WSWorkStatus } from "./types.ts";

export const WSStatusModal: FC<{
  WorkStatus: WSWorkStatus;
  CloseStatus: WSCloseStatus;
  close: WSClose;
}> = ({ CloseStatus, WorkStatus, close }) => {
  const { logout } = useAuthContext();

  const [sendLogout] = useLogout();

  const ReloadTimeout = useRef(null);
  const LogoutTimeout = useRef(null);

  const reloadPage = useCallback(() => {
    ReloadTimeout.current = window.setTimeout(() => {
      window.clearTimeout(ReloadTimeout.current);
      window.location.reload();
    }, 4000);
  }, []);

  const logoutUser = useCallback(
    (timeout: number) => {
      LogoutTimeout.current = window.setTimeout(() => {
        sendLogout(null)
          .unwrap()
          .then(() => {
            close({ logout: true, reason: "Logout" });
            logout();
          });
      }, timeout * 60_000);
    },
    [close, logout, sendLogout],
  );

  const getErrorMessage = useCallback(
    (status: WSCloseStatus) => {
      switch (status) {
        case WSCloseStatus.AuthError:
          return {
            shown: true,
            label: "Authentication Error",
            text: "There was an issue verifying your credentials. Please login again or contact support if the issue persists",
          };

        case WSCloseStatus.ReconnectError: {
          reloadPage();
          return {
            shown: true,
            label: "Reconnection Error",
            text: "We couldn't re-establish the connection. Please check your internet and try again, or contact your administrator if the issue continues",
          };
        }

        case WSCloseStatus.SameUserError:
          return {
            shown: true,
            label: "Session Conflict: Another Login Detected",
            text: "Your account has been accessed from another device or browser. Please check if this is expected, or log back in if needed",
          };

        case WSCloseStatus.SamePackageError: {
          reloadPage();
          return {
            shown: true,
            label:
              "Package unavailable: Package already opened by another user",
            text: null,
          };
        }

        case WSCloseStatus.TimeoutError: {
          logoutUser(5);
          return {
            shown: true,
            label: "Connection Timeout",
            text: "The connection has timed out due to inactivity. Please refresh or log back in to continue",
          };
        }

        case WSCloseStatus.OfflineTimeoutError: {
          return {
            shown: true,
            label: "Page Refresh Needed",
            text: "Unfortunately, we couldn't reconnect within 60 seconds. Refresh page as internet connection will be restored. Any unsaved changes may be lost.",
          };
        }

        case WSCloseStatus.InternalError: {
          reloadPage();
          return {
            shown: true,
            label: "Internal Server Error",
            text: "An unexpected error occurred on the server. Please try again later or contact support if the issue persists",
          };
        }

        default:
          return {
            shown: false,
            label: null,
            text: null,
          };
      }
    },
    [logoutUser, reloadPage],
  );

  const { shown, label, text } = useMemo<{
    shown: boolean;
    label: string | null;
    text: string | null;
  }>(() => {
    switch (WorkStatus) {
      case WSWorkStatus.AFK: {
        logoutUser(25);
        return {
          shown: true,
          label: "AFK",
          text: "It seems you've been inactive for a while. Once you return, you can resume your work",
        };
      }
      case WSWorkStatus.Offline:
        return {
          shown: true,
          label: "Internet Connection Lost",
          text: "We've lost connection to the internet. Once the connection is restored, you can continue reviewing your document without interruption",
        };
      case WSWorkStatus.RECONNECTING:
        return {
          shown: true,
          label: "Restoring Connection",
          text: "We're experiencing issues re-establishing the connection. Please contact your administrator if the issue persists beyond 60 seconds",
        };
      case WSWorkStatus.ERROR:
        return getErrorMessage(CloseStatus);
      default:
        return {
          shown: false,
          label: null,
          text: null,
        };
    }
  }, [CloseStatus, WorkStatus, getErrorMessage, logoutUser]);

  useEffect(() => {
    if (WorkStatus !== WSWorkStatus.AFK)
      window.clearTimeout(LogoutTimeout.current);
  }, [WorkStatus]);

  useEffect(() => {
    return () => {
      window.clearTimeout(ReloadTimeout.current);
      window.clearTimeout(LogoutTimeout.current);
    };
  }, []);

  if (!shown) return null;

  return (
    <Modal open={shown}>
      <Box
        style={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          backgroundColor: "white",
          padding: "24px 32px",
          boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
          zIndex: 1000,
          textAlign: "center",
          color: "black",
          borderRadius: "16px",
        }}
      >
        <Box
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "12px",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Box style={{ fontSize: "20px", fontWeight: 600 }}>{label}</Box>
          {text && (
            <Box style={{ fontSize: "16px", fontWeight: 400 }}>{text}</Box>
          )}
        </Box>
      </Box>
    </Modal>
  );
};
