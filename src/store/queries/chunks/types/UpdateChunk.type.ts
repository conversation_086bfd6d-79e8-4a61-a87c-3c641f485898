import { MetaData } from "../../../../components/SegmentEditor/segment.types.ts";
import { PageRanges } from "./Chunk.type.ts";

export interface ChunkToUpdate {
  predictions: {
    page_ranges: PageRanges;
    classification_confidence: number;
    overall_confidence: number;
    packet_type: string;
    packet_subtype?: string;
    incomplete: boolean;
    duplicate_of?: { packet_type: string; page_ranges: PageRanges };
  }[];
  metadata: {
    docName: string;
    docLink: string;
    docType: string;
    docSubType?: string;
    docConfidence: number;
    pageRefStart: number;
    pageRefEnd: number;
    pagesRef: PageRanges;
    claimNumber: string;
    isDuplicated: boolean;
    pagesRefOrig: PageRanges;
    namingData: NamingData;
    metaData: MetaData;
  }[];
  comment: string;
}

export interface ChunkUpdateRequest {
  package_id: string;
  update_type: "submit" | "delegate" | "part_update";
  payload: ChunkToUpdate;
}
