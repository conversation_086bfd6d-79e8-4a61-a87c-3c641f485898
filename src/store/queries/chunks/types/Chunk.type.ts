export type PageRanges = [number, number][];

export type ChunkFieldsToValidate = ("metaData" | "namingData")[];

export type MetadataFiled = {
  valid: boolean | string | null;
  value: string;
  required: boolean;
  confidence: number | null;
};

export type PaketMetaData =
  | { [key: string]: MetadataFiled }
  | { [key: string]: PaketMetaData };

export interface Client {
  client_id: string;
  name: string;
}

export interface Packet {
  packet_id: string;
  filename: string;
  received_time: string;
  sent_time: string;
  token: string;
  pipeline_packet_id: string;
  pages_count: number;
  fields_to_validate: ChunkFieldsToValidate;
  is_large: boolean;
  chunk_amount: number;
}

export interface PacketChunkPrediction {
  page_ranges: PageRanges;
  classification_confidence: number;
  overall_confidence: number;
  packet_type: string;
  packet_subtype?: string;
  incomplete: boolean;
  packet_chunk_prediction_id: string;
  chunk_id: string;
  prediction_type: string;
}

export interface PacketChunkMetadata {
  docName: string;
  docLink: string;
  docType: string;
  docSubType?: string;
  docConfidence: number;
  pageRefStart: number;
  pageRefEnd: number;
  pagesRef: PageRanges;
  claimNumber: string;
  isDuplicated: boolean;
  pagesRefOrig: PageRanges;

  namingData: { [key: string]: MetadataFiled };
  metaData: PaketMetaData;

  packet_chunk_metadata_id: string;
  chunk_id: string;
  metadata_type: string;
}

export interface PacketChunkComment {
  packet_chunk_comment_id: string;
  chunk_id: string;
  user_id: string;
  created_at: string;
  text: string;
}

export interface Chunk {
  chunk_id: string;
  pipeline_chunk_id: string;
  chunk_number: number;
  status: string;
  pages_count: number;
  priority: number;
  created_at: string;
  updated_at: string;
  merged_incomplete: boolean;
  pages_range: [number, number];
  presigned_url: string;
  delegated_by_username: string;
  delegated_by_user_id: string;
  delegated_time: string;
  client: Client;
  packet: Packet;
  predictions: PacketChunkPrediction[];
  metadata: PacketChunkMetadata[];
  comments: (PacketChunkComment | null)[];
  comment: string;
}
