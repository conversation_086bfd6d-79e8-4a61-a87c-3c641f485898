import { ChunkUpdateRequest } from "../../types/UpdateChunk.type.ts";

export const prepareMetadataToUpdate = ({
  payload,
}: Pick<ChunkUpdateRequest, "payload">) => {
  return payload.metadata.map((data, index) => {
    const slice = payload.predictions[index];
    return {
      ...data,
      docSubType: slice?.packet_subtype ?? null,
      incomplete: <PERSON><PERSON><PERSON>(slice.incomplete),
      isDuplicated: <PERSON><PERSON><PERSON>(slice.duplicate_of),
      pagesRefOrig: slice?.duplicate_of?.page_ranges ?? null,
    };
  });
};
