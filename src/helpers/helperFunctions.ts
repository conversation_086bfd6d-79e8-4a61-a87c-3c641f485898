import {
  checkIfBooleanField,
  preprocessSlices,
  processFaxAndPhone,
  processGender,
} from "../components/Editor/helpers";
import { docTypes } from "./documentTypes";
import { reqFields } from "./documentsRequiredFields";

const colors = {
  0: "#0000FF",
  1: "#000000",
  2: "#800080",
  3: "#1e7d2f",
};

const getColor = (level: number): string => {
  const index = level % 4;
  return colors[index];
};

const isMoreThanSevenDaysAgo = (startDateStr: string) => {
  const startDate = new Date(startDateStr);
  const today = new Date();
  const timeDiff = today.getTime() - startDate.getTime();
  const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
  return daysDiff > 7;
};

function checkKeyExists(str: string, docType: string) {
  const keys = str.split(".").slice(1);
  let currentObj = reqFields?.[docType] || reqFields.default;

  for (const key of keys) {
    if (currentObj?.[key] === undefined) {
      return false;
    }
    currentObj = currentObj[key];
  }

  return true;
}

const updateDocumentSlices = async (
  slices: EntryListItem[],
  metadata: any,
): Promise<Document[]> => {
  const processedSlices = preprocessSlices(slices);
  const docsArray: any[] = processedSlices.map((slice) => {
    const sliceDocType = slice.packet_type;
    const slicePageRanges = slice.page_ranges;

    const matchingMetaV1 = metadata.find((meta: PacketMetadata) => {
      const metaDocType = meta.docType;
      const metaPageRanges = meta.pagesRef;

      function arrayIncludesArray(
        mainArray: number[][],
        subArray: number[][],
      ): boolean {
        return subArray.every((sub) =>
          mainArray.some(
            (main) =>
              main.length === sub.length &&
              main.every((value, index) => {
                return Math.abs(value - sub[index]) <= 1;
              }),
          ),
        );
      }

      return (
        sliceDocType === metaDocType &&
        arrayIncludesArray(slicePageRanges, metaPageRanges)
      );
    });

    const matchingMetaV2 = metadata.find((meta: PacketMetadata) => {
      const isSameDoctype = meta.docType === sliceDocType;
      const isSamePageRanges =
        JSON.stringify(meta.pagesRef) === JSON.stringify(slicePageRanges);

      return isSameDoctype && isSamePageRanges;
    });

    const matchingMeta = matchingMetaV2;

    if (matchingMeta && !hasMetaDataOnOuterLayer(matchingMeta)) {
      const { metaData, namingData } = unpackData(matchingMeta);
      const doc = {
        namingData,
        metaData: metaData,
        pagesRef: slice.page_ranges,
        docType: slice.packet_type,
      };
      const filledData = ensureFieldsRequired(fillMissingFields(doc));
      // const filledData = fillMissingFields(doc);

      return {
        ...doc,
        metaData: filledData,
        docType: slice.packet_type,
        docSubType: slice.packet_subtype ?? null,
      };
    }

    if (!matchingMeta && sliceDocType === "Undefined") {
      return null;
    }

    if (
      !matchingMeta ||
      (typeof matchingMeta && Object.keys(matchingMeta.metaData).length === 0)
    ) {
      const docType = docTypes[slice.packet_type] || slice.packet_type;
      const requiredDoc = reqFields[docType] || reqFields.default;
      const expandedReqDoc = expandMetadata(requiredDoc);

      return {
        ...expandedReqDoc,
        pagesRef: slice.page_ranges,
        docType: slice.packet_type,
        docSubType: slice.packet_subtype ?? null,
      };
    } else {
      const metaData = ensureFieldsRequired(fillMissingFields(matchingMeta));
      // const metaData = fillMissingFields(matchingMeta);
      const { namingData, doc_type, docType } = matchingMeta;
      return {
        namingData,
        metaData,
        pagesRef: slice.page_ranges,
        docType: doc_type || docType,
        docSubType: slice.packet_subtype ?? null,
      };
    }
  });

  console.log("updated array from slices", docsArray);

  return docsArray.filter(Boolean);
};

export function isMetadataEntry(data: any): data is MetadataEntry {
  return (
    typeof data.value === "string" ||
    (typeof data.value === "boolean" &&
      (typeof data.confidence === "number" || data.confidence === null) &&
      (typeof data.valid === "boolean" || data.valid === null) &&
      typeof data.required === "boolean")
  );
}

export function hasRequiredFields(field: AnyRecord): boolean {
  return Object.values(field).some((field) => field?.required);
}

export function expandMetadata(jsonObj: JsonObject | JsonValue[]) {
  function processValue(key: string, value: JsonValue) {
    if (Array.isArray(value)) {
      return value.map((item) =>
        typeof item === "object" && item !== null
          ? expandMetadata(item)
          : {
              value: item,
              confidence: 1,
              valid: true,
              required: true,
            },
      );
    } else if (typeof value === "object" && value !== null) {
      return expandMetadata(value);
    } else {
      return {
        value: value,
        confidence: 1,
        valid: true,
        required: true,
      };
    }
  }

  const result: { [key: string]: Metadata } = {};
  for (const key in jsonObj) {
    if (Object.prototype.hasOwnProperty.call(jsonObj, key)) {
      result[key] = processValue(key, jsonObj[key]) as Metadata;
    }
  }

  return result;
}

export function hasMetaDataOnOuterLayer<T extends Record<string, any>>(
  data: T,
): boolean {
  return "metaData" in data;
}

export function unpackData<T extends Record<string, any>>(data: T): T {
  const result: Record<string, any> = {
    metaData: {},
    namingData: {},
  };

  function recurse(obj: any) {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const value = obj[key];
        if (key === "metaData" || key === "namingData") {
          result[key] = value;
        } else if (
          typeof value === "object" &&
          value !== null &&
          !Array.isArray(value)
        ) {
          recurse(value);
        } else {
          result[key] = value;
        }
      }
    }
  }

  recurse(data);

  return result as T;
}

function fillMissingFields(inputObject: InputObject): AnyRecord {
  const docType =
    inputObject.packet_type || inputObject.doc_type || inputObject.docType;
  const requiredFieldsForDocType =
    reqFields[docType]?.metaData || reqFields.default.metaData;

  if (!requiredFieldsForDocType) {
    return inputObject.metaData;
  }

  function fillObject(obj: AnyRecord, schema: RequiredFieldsSchema): AnyRecord {
    const filledObject: AnyRecord = deepCopy(obj);

    for (const field in schema) {
      if (field === "treatments") {
        if (Array.isArray(filledObject[field])) {
          filledObject[field] = filledObject[field].map((item: AnyRecord) => {
            for (const insideField in item) {
              const isFieldRequired =
                requiredFieldsForDocType[field][0][insideField] === "";

              if (Object.prototype.hasOwnProperty.call(item, insideField)) {
                item[insideField] =
                  typeof item[insideField] === "object" &&
                  !Array.isArray(item[insideField])
                    ? item[insideField]
                    : {
                        value: "",
                        confidence: 1,
                        valid: true,
                        required: true,
                      };
                item[insideField].required = isFieldRequired;
              }
            }
            return item;
          });
        } else {
          filledObject["treatments"] = [];
        }
      }
      if (Array.isArray(schema[field])) {
        filledObject[field] = filledObject[field] || [];
        if (
          Array.isArray(filledObject[field]) &&
          filledObject[field].length > 0
        ) {
          for (let i = 0; i < filledObject[field].length; i++) {
            filledObject[field][i] = fillObject(
              filledObject[field][i] || {},
              schema[field][0] as RequiredFieldsSchema,
            );
          }
        } else if (Array.isArray(filledObject[field])) {
          for (let i = 0; i < schema[field].length; i++) {
            filledObject[field][i] = fillObject(
              filledObject[field][i] || {},
              schema[field][i] as RequiredFieldsSchema,
            );
          }
        }
      } else if (
        typeof schema[field] === "object" &&
        !("value" in schema[field])
      ) {
        filledObject[field] = fillObject(
          filledObject[field] || {},
          schema[field] as unknown as RequiredFieldsSchema,
        );
      } else {
        if (!(field in filledObject)) {
          filledObject[field] = {
            value: "",
            confidence: 1,
            valid: true,
            required: true,
          };
        } else if (filledObject[field].required === false) {
          filledObject[field].required = true;
        }
      }
    }

    function sortObjectBySchema(objectToSort, schemaObject) {
      const result = {};

      Object.keys(schemaObject).forEach((key) => {
        if (Object.prototype.hasOwnProperty.call(objectToSort, key)) {
          if (
            typeof schemaObject[key] === "object" &&
            schemaObject[key] !== null &&
            !Array.isArray(schemaObject[key])
          ) {
            result[key] = sortObjectBySchema(
              objectToSort[key],
              schemaObject[key],
            );
          } else if (Array.isArray(schemaObject[key])) {
            result[key] = objectToSort[key].map((item, index) =>
              sortObjectBySchema(item, schemaObject[key][0]),
            );
          } else {
            result[key] = objectToSort[key];
          }
        } else {
          result[key] = undefined;
        }
      });

      return result;
    }

    return sortObjectBySchema(filledObject, schema);
  }

  return fillObject(inputObject.metaData, requiredFieldsForDocType);
}

const sortKeys = (obj: { [x: string]: any }) => {
  const sortedKeys = Object.keys(obj).sort();

  const sortedObj = {};
  sortedKeys.forEach((key) => {
    sortedObj[key] = obj[key];
  });
  return sortedObj;
};

function ensureFieldsRequired(data: any): any {
  function traverseAndModify(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map((item) => traverseAndModify(item));
    } else if (typeof obj === "object" && obj !== null) {
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const value = obj[key];
          if (checkIfBooleanField(key.toLowerCase())) {
            if (!obj[key]) {
              obj[key] = {
                value: null,
                confidence: 1,
                valid: true,
                required: true,
              };
            } else if (
              obj[key].value === undefined ||
              obj[key].value === null
            ) {
              obj[key].value = null;
            } else if (typeof obj[key].value === "boolean") {
              if (obj[key].value === true) {
                obj[key].value = "Y";
              } else if (obj[key].value === false) {
                obj[key].value = "N";
              }
            } else if (
              obj[key].value.toLowerCase() === "y" ||
              obj[key].value.toLowerCase() === "n"
            ) {
              if (obj[key].value.toLowerCase() === "y") {
                obj[key].value = "Y";
              } else if (obj[key].value.toLowerCase() === "n") {
                obj[key].value = "N";
              }
            } else if (
              obj[key].value.toLowerCase() === "true" ||
              obj[key].value.toLowerCase() === "false"
            ) {
              if (obj[key].value.toLowerCase() === "true") {
                obj[key].value = "Y";
              } else if (obj[key].value.toLowerCase() === "false") {
                obj[key].value = "N";
              }
            } else if (
              obj[key].value.toLowerCase() === "t" ||
              obj[key].value.toLowerCase() === "f"
            ) {
              if (obj[key].value.toLowerCase() === "t") {
                obj[key].value = "Y";
              } else if (obj[key].value.toLowerCase() === "f") {
                obj[key].value = "N";
              }
            } else if (
              obj[key].value !== "true" &&
              obj[key].value !== "false"
            ) {
              obj[key].value = null;
            }
          }
          if (key.toLowerCase().includes("gender")) {
            obj[key].value = processGender(obj[key].value);
          }
          if (
            key.toLowerCase().includes("fax") ||
            key.toLowerCase().includes("phone")
          ) {
            obj[key].value = processFaxAndPhone(obj[key].value);
          }
          if (typeof value === "object" && value !== null) {
            obj[key] = traverseAndModify(value);
          } else if (typeof obj.value === "number") {
            obj.value = `${obj.value}`;
          } else if (key === "value" && !obj.value) {
            obj.value = "";
          }
        }
      }
    }

    return obj;
  }

  return traverseAndModify(data);
}

function deepCopy<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}

export {
  checkKeyExists,
  deepCopy,
  getColor,
  isMoreThanSevenDaysAgo,
  sortKeys,
  updateDocumentSlices,
};
