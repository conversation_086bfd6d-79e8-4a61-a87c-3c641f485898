interface LoginData {
  username: string;
  password: string;
}

interface ResetPasswordData {
  password: string;
  password_repeat: string;
}

interface SubmitType {
  predictions: EntryListItem[];
  metadata: MetaData[];
  comment: string;
  predictions_human_time?: Date;
}

interface DocumentStats {
  total_packets: number;
  validated_packets: number;
  validated_types: {
    [key: string]: number;
  };
}

type AnyRecord = Record<string, any>;

interface MetadataEntry {
  value: string | any;
  confidence: number | null;
  valid: boolean | null;
  required: boolean;
}

interface DataExtracted {
  metaData: AnyRecord;
  namingData: AnyRecord;
}

type JsonValue = string | number | boolean | JsonObject | JsonValue[];
interface JsonObject {
  [key: string]: JsonValue;
}

interface Metadata {
  value: JsonValue;
  confidence: number | null;
  valid: boolean | null;
  required: boolean;
}

interface RequiredField {
  length: number;
  value: string;
  confidence: number;
  valid: boolean;
  required: boolean;
}

interface RequiredFieldsSchema {
  [key: string]: RequiredField;
}

interface InputObject {
  docType: string;
  metaData: AnyRecord;
  [key: string]: any;
}

interface RequiredFieldsSchema {
  [key: string]: RequiredField | RequiredFieldsSchema | RequiredFieldsSchema[];
}

interface UserType {
  user_uuid: string;
  username: string;
  password: string;
  user_type: string;
}

type RequiredFieldsSchema = Record<string, any>;

interface DataState {
  slices: EntryListItem[];
  metadata: PacketMetadata[];
  comment: string;
  status: string;
  marking: ("is_duplicate" | "is_incomplete")[];
  range: [number, number];
  is_chunk: boolean;
}

interface ResponseData {
  filename: string;
  status: string;
  received_time: Date;
  packet_id: string;
  presigned_url: string;
  predictions: Prediction[];
  packet_metadata: PacketMetadata[];
  comment: string | null;
  client: {
    client_id: string;
    name: string;
  };
}

interface EntryListItem {
  page_ranges: [number, number][];
  packet_type: string;
  packet_subtype?: string;
  classification_confidence: number;
  overall_confidence: number;
  duplicate_of?: { packet_type: string; page_ranges: [number, number][] } | null;
  packet_prediction_id: string;
  packet_id: string | null;
  prediction_type: string | null;
  incomplete: boolean;
  other_patient: boolean;
  index?: number;
}

interface PacketMetadata {
  docName: string;
  docLink: string;
  docType: string;
  docSubType?: string;
  docConfidence: null;
  pageRefStart: null;
  pageRefEnd: null;
  pagesRef: [number, number][];
  claimNumber: string;
  namingData: NamingData;
  metaData: MetaData;
  packet_metadata_id: string;
  packet_id: string;
  metadata_type: string;
}

interface MetaData extends Partial<Record<string, any>> {
  claim?: Claim;
  docDate?: MetadataEntry;
  adjuster?: Adjuster;
  attorney?: Adjuster;
  claimant?: Claimant;
  employer?: Employer;
  rushFlag?: MetadataEntry;
  physician?: Physician;
  senderName?: MetadataEntry;
  treatments?: any[];
  jurisdiction?: MetadataEntry;
  resubmission?: MetadataEntry;
  expeditedFlag?: MetadataEntry;
  isHandwritten?: MetadataEntry;
  newSubmission?: MetadataEntry;
  docReceivedDate?: MetadataEntry;
  isEnglishLanguage?: MetadataEntry;
  writtenConfirmPriorOralRequest?: MetadataEntry;
}

interface NamingData extends Partial<Record<string, any>> {
  MetadataEntry?: MetadataEntry;
  docState?: MetadataEntry;
  senderName?: MetadataEntry;
  claimNumber?: MetadataEntry;
  patientName?: MetadataEntry;
  docReceivedDate?: MetadataEntry;
  patientLastName?: PatientName;
  patientFirstName?: PatientName;
  patientMiddleName?: PatientName;
  providerOrFacilityName?: MetadataEntry;
}

interface Adjuster extends Partial<Record<string, any>> {
  suffix?: MetadataEntry;
  address?: Address;
  company?: MetadataEntry;
  lastName?: MetadataEntry;
  phoneExt?: MetadataEntry;
  faxNumber?: MetadataEntry;
  firstName?: MetadataEntry;
  adjusterId?: MetadataEntry;
  keyContact?: MetadataEntry;
  middleName?: MetadataEntry;
  phoneNumber?: MetadataEntry;
  emailAddress?: MetadataEntry;
  type?: MetadataEntry;
}

interface Address extends Partial<Record<string, any>> {
  zip?: MetadataEntry;
  city?: MetadataEntry;
  state?: MetadataEntry;
  address1?: MetadataEntry;
  address2?: MetadataEntry;
}

interface Claim extends Partial<Record<string, any>> {
  jurisState?: MetadataEntry;
  claimNumber?: MetadataEntry;
  dateOfInjury?: MetadataEntry;
}

interface Claimant extends Partial<Record<string, any>> {
  ssn?: MetadataEntry;
  gender?: MetadataEntry;
  suffix?: MetadataEntry;
  address?: Address;
  lastName?: MetadataEntry;
  firstName?: MetadataEntry;
  employeeId?: MetadataEntry;
  middleName?: MetadataEntry;
  dateOfBirth?: MetadataEntry;
  phoneNumber?: MetadataEntry;
  claimantName?: MetadataEntry;
  emailAddress?: MetadataEntry;
}

interface PatientName extends Partial<Record<string, any>> {
  value?: MetadataEntry;
}

interface Employer extends Partial<Record<string, any>> {
  name?: MetadataEntry;
  taxId?: MetadataEntry;
  address?: Address;
  faxNumber?: MetadataEntry;
  phoneNumber?: MetadataEntry;
  contactEmail?: MetadataEntry;
}

interface Physician extends Partial<Record<string, any>> {
  npi?: MetadataEntry;
  type?: MetadataEntry;
  address?: Address;
  lastName?: MetadataEntry;
  faxNumber?: MetadataEntry;
  firstName?: MetadataEntry;
  specialty?: MetadataEntry;
  credentials?: MetadataEntry;
  phoneNumber?: MetadataEntry;
  emailAddress?: MetadataEntry;
  facilityName?: MetadataEntry;
  physicianName?: MetadataEntry;
  keyContactName?: MetadataEntry;
}
