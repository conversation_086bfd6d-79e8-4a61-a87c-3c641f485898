FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    libreoffice \
    poppler-utils \
    libmagic1 \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt* ./
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt || \
    pip install --no-cache-dir \
        sqlalchemy \
        psycopg2-binary \
        pika \
        boto3 \
        requests \
        python-magic \
        pymupdf \
        img2pdf \
        pillow \
        paramiko \
        azure-servicebus \
        extract-msg \
        uuid6 \
        minio

# Copy the entire ML pipeline
COPY . .

# Create logs directory
RUN mkdir -p /app/logs

# Set Python path
ENV PYTHONPATH="/app"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# Default command (will be overridden per service)
CMD ["python", "--version"] 