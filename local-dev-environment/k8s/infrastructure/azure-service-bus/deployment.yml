apiVersion: apps/v1
kind: Deployment
metadata:
  name: azure-service-bus-emulator
  namespace: infrastructure
  labels:
    app: azure-service-bus-emulator
    component: message-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: azure-service-bus-emulator
  template:
    metadata:
      labels:
        app: azure-service-bus-emulator
        component: message-service
    spec:
      containers:
        - name: azure-service-bus-emulator
          # Using Apache Qpid Broker-J as Azure Service Bus emulator
          image: scholzj/qpid-broker-j:latest
          ports:
            - containerPort: 5672
              name: amqp
            - containerPort: 8080
              name: management
          env:
            - name: QPID_WORK_DIR
              value: "/var/lib/qpid"
            - name: QPID_CONFIG_FILE
              value: "/etc/qpid/config.json"
          volumeMounts:
            - name: qpid-storage
              mountPath: /var/lib/qpid
            - name: qpid-config
              mountPath: /etc/qpid
              readOnly: true
          resources:
            limits:
              memory: "512Mi"
              cpu: "300m"
            requests:
              memory: "128Mi"
              cpu: "100m"
          livenessProbe:
            tcpSocket:
              port: 5672
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            tcpSocket:
              port: 5672
            initialDelaySeconds: 10
            periodSeconds: 5
      volumes:
        - name: qpid-storage
          emptyDir: {}
        - name: qpid-config
          configMap:
            name: azure-service-bus-config
---
apiVersion: v1
kind: Service
metadata:
  name: azure-service-bus-emulator
  namespace: infrastructure
  labels:
    app: azure-service-bus-emulator
    component: message-service
spec:
  type: ClusterIP
  ports:
    - port: 5672
      targetPort: 5672
      protocol: TCP
      name: amqp
    - port: 8080
      targetPort: 8080
      protocol: TCP
      name: management
  selector:
    app: azure-service-bus-emulator
---
apiVersion: v1
kind: Service
metadata:
  name: azure-service-bus-external
  namespace: infrastructure
  labels:
    app: azure-service-bus-emulator
    component: message-service
spec:
  type: NodePort
  ports:
    - port: 5672
      targetPort: 5672
      nodePort: 30671
      protocol: TCP
      name: amqp
    - port: 8080
      targetPort: 8080
      nodePort: 30680
      protocol: TCP
      name: management
  selector:
    app: azure-service-bus-emulator
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: azure-service-bus-config
  namespace: infrastructure
data:
  config.json: |
    {
      "name": "azure-service-bus-emulator",
      "modelVersion": "8.0",
      "authenticationproviders": [
        {
          "name": "anonymous",
          "type": "Anonymous"
        }
      ],
      "ports": [
        {
          "name": "AMQP",
          "port": 5672,
          "authenticationProvider": "anonymous",
          "protocols": ["AMQP_0_10", "AMQP_1_0"]
        },
        {
          "name": "HTTP",
          "port": 8080,
          "authenticationProvider": "anonymous",
          "protocols": ["HTTP"]
        }
      ],
      "virtualhosts": [
        {
          "name": "default",
          "type": "BDB",
          "storePath": "/var/lib/qpid/default",
          "queues": [
            {
              "name": "splitter-local",
              "type": "standard"
            },
            {
              "name": "sbq-localdev-service-bus-queue",
              "type": "standard"
            }
          ],
          "exchanges": [
            {
              "name": "record-ranger-exchange",
              "type": "direct"
            }
          ]
        }
      ]
    }
