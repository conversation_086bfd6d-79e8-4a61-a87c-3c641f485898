apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-config
  namespace: infrastructure
data:
  rabbitmq.conf: |
    # RabbitMQ Configuration for Local Development
    
    # Logging
    log.console = true
    log.console.level = info
    log.file = false
    
    # Memory and disk thresholds
    vm_memory_high_watermark.relative = 0.6
    disk_free_limit.relative = 2.0
    
    # Clustering (disabled for single node)
    cluster_formation.peer_discovery_backend = classic_config
    
    # Management plugin
    management.tcp.port = 15672
    management.tcp.ip = 0.0.0.0
    
    # AMQP 0-9-1 and AMQP 1.0 listeners
    listeners.tcp.default = 5672
    
    # Default user and vhost
    default_vhost = /
    default_user = user
    default_pass = localdev123
    default_user_tags.administrator = true
    default_permissions.configure = .*
    default_permissions.read = .*
    default_permissions.write = .*
    
    # Queue and message settings
    queue_master_locator = min-masters
    
    # Heartbeat settings
    heartbeat = 60
    
    # Connection settings
    channel_max = 2047
    connection_max = 1000
    
    # TLS disabled for local development
    ssl_options.verify = verify_none
    ssl_options.fail_if_no_peer_cert = false
  
  enabled_plugins: |
    [rabbitmq_management,rabbitmq_prometheus,rabbitmq_shovel,rabbitmq_shovel_management].
  
  definitions.json: |
    {
      "users": [
        {
          "name": "user",
          "password_hash": "localdev123",
          "hashing_algorithm": "rabbit_password_hashing_sha256",
          "tags": "administrator"
        }
      ],
      "vhosts": [
        {
          "name": "/"
        }
      ],
      "permissions": [
        {
          "user": "user",
          "vhost": "/",
          "configure": ".*",
          "write": ".*",
          "read": ".*"
        }
      ],
      "queues": [
        {
          "name": "to_classify",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "to_split",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "to_extract_metadata",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "to_postprocess_metadata",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "to_validate",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "to_upload",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "to_backend_qa",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "to_backend_over_qa",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "packet_status",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "from_backend_qa",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        },
        {
          "name": "queue_from_backend_qa",
          "vhost": "/",
          "durable": true,
          "auto_delete": false,
          "arguments": {}
        }
      ],
      "exchanges": [
        {
          "name": "record_ranger_exchange",
          "vhost": "/",
          "type": "direct",
          "durable": true,
          "auto_delete": false,
          "internal": false,
          "arguments": {}
        }
      ],
      "bindings": []
    }
