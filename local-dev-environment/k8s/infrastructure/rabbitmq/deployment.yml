apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  namespace: infrastructure
  labels:
    app: rabbitmq
    component: message-broker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
        component: message-broker
    spec:
      containers:
        - name: rabbitmq
          image: rabbitmq:3-management
          ports:
            - containerPort: 5672
              name: amqp
            - containerPort: 15672
              name: management
          env:
            - name: RABBITMQ_DEFAULT_USER
              value: "user"
            - name: RABBITMQ_DEFAULT_PASS
              value: "localdev123"
            - name: RABBITMQ_DEFAULT_VHOST
              value: "/"
            - name: RABBITMQ_ERLANG_COOKIE
              value: "record-ranger-local-cookie"
          volumeMounts:
            - name: rabbitmq-storage
              mountPath: /var/lib/rabbitmq
            - name: rabbitmq-config
              mountPath: /etc/rabbitmq/conf.d
              readOnly: true
          resources:
            limits:
              memory: "1Gi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "100m"
          livenessProbe:
            exec:
              command:
                - rabbitmq-diagnostics
                - -q
                - ping
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            exec:
              command:
                - rabbitmq-diagnostics
                - -q
                - check_port_connectivity
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
      volumes:
        - name: rabbitmq-storage
          persistentVolumeClaim:
            claimName: rabbitmq-pvc
        - name: rabbitmq-config
          configMap:
            name: rabbitmq-config
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: rabbitmq-pvc
  namespace: infrastructure
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq
  namespace: infrastructure
  labels:
    app: rabbitmq
    component: message-broker
spec:
  type: ClusterIP
  ports:
    - port: 5672
      targetPort: 5672
      protocol: TCP
      name: amqp
    - port: 15672
      targetPort: 15672
      protocol: TCP
      name: management
  selector:
    app: rabbitmq
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-external
  namespace: infrastructure
  labels:
    app: rabbitmq
    component: message-broker
spec:
  type: NodePort
  ports:
    - port: 5672
      targetPort: 5672
      nodePort: 30672
      protocol: TCP
      name: amqp
    - port: 15672
      targetPort: 15672
      nodePort: 31672
      protocol: TCP
      name: management
  selector:
    app: rabbitmq
