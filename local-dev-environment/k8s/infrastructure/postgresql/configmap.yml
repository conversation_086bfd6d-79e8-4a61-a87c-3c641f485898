apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-init-scripts
  namespace: infrastructure
data:
  01-create-databases.sql: |
    -- Create the three required databases
    CREATE DATABASE rr OWNER backend;
    CREATE DATABASE backend OWNER backend;
    CREATE DATABASE queue OWNER backend;
    
    -- Grant all privileges
    GRANT ALL PRIVILEGES ON DATABASE rr TO backend;
    GRANT ALL PRIVILEGES ON DATABASE backend TO backend;
    GRANT ALL PRIVILEGES ON DATABASE queue TO backend;
  
  02-init-rr-schema.sql: |
    -- Connect to rr database and initialize schema
    \c rr;
    
    -- Create schema
    CREATE SCHEMA IF NOT EXISTS public AUTHORIZATION backend;
    COMMENT ON SCHEMA public IS 'standard public schema';
    
    -- Create tenants table
    CREATE TABLE IF NOT EXISTS public.tenants (
      tenant_id varchar(128) NOT NULL,
      tenant_name varchar(256) NOT NULL,
      description varchar(512) NULL,
      contact_email varchar(256) NULL,
      created_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
      updated_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
      active bool DEFAULT true NULL,
      tenant_config json NULL,
      CONSTRAINT tenants_pkey PRIMARY KEY (tenant_id)
    );
    
    -- Create user_activity_logs table
    CREATE TABLE IF NOT EXISTS public.user_activity_logs (
      activity_id uuid NOT NULL,
      user_uuid uuid NULL,
      "action" varchar NULL,
      description varchar NULL,
      action_timestamp timestamptz DEFAULT now() NOT NULL,
      tenant_id varchar(128) NULL,
      subtenant_id varchar(128) NULL,
      CONSTRAINT user_activity_logs_pkey PRIMARY KEY (activity_id)
    );
    CREATE INDEX IF NOT EXISTS idx_activity_log_tenant ON public.user_activity_logs USING btree (tenant_id, subtenant_id);
    
    -- Set ownership
    ALTER TABLE public.tenants OWNER TO backend;
    ALTER TABLE public.user_activity_logs OWNER TO backend;
    GRANT ALL ON TABLE public.tenants TO backend;
    GRANT ALL ON TABLE public.user_activity_logs TO backend;
    
    -- Insert sample tenant data
    INSERT INTO public.tenants (tenant_id, tenant_name, description, contact_email, active) 
    VALUES ('local-dev', 'Local Development', 'Local development tenant', 'dev@localhost', true)
    ON CONFLICT (tenant_id) DO NOTHING;
  
  03-init-backend-schema.sql: |
    -- Connect to backend database and initialize schema
    \c backend;
    
    -- Create schema
    CREATE SCHEMA IF NOT EXISTS public AUTHORIZATION backend;
    COMMENT ON SCHEMA public IS 'standard public schema';
    
    -- Create notification enums
    CREATE TYPE IF NOT EXISTS public."notificationentitynameenum" AS ENUM ('CHUNK', 'PACKET');
    CREATE TYPE IF NOT EXISTS public."notificationtypeenum" AS ENUM (
      'CHUNK_DELEGATED_STUCK',
      'CHUNK_REVIEW_STUCK', 
      'PACKET_LARGE_STUCK',
      'PACKET_STUCK_BEFORE_QA',
      'PACKET_STUCK_AFTER_QA'
    );
    
    -- Create alembic_version table
    CREATE TABLE IF NOT EXISTS public.alembic_version (
      version_num varchar(32) NOT NULL,
      CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
    );
    
    -- Create clients table
    CREATE TABLE IF NOT EXISTS public.clients (
      client_id uuid NOT NULL,
      "name" varchar(256) NOT NULL,
      priority int2 NOT NULL,
      CONSTRAINT clients_pkey PRIMARY KEY (client_id)
    );
    
    -- Set ownership
    ALTER TABLE public.alembic_version OWNER TO backend;
    ALTER TABLE public.clients OWNER TO backend;
    GRANT ALL ON TABLE public.alembic_version TO backend;
    GRANT ALL ON TABLE public.clients TO backend;
  
  04-init-queue-schema.sql: |
    -- Connect to queue database and initialize schema
    \c queue;
    
    -- Create schema
    CREATE SCHEMA IF NOT EXISTS public AUTHORIZATION backend;
    COMMENT ON SCHEMA public IS 'standard public schema';
    
    -- Create raw_incoming_packages table
    CREATE TABLE IF NOT EXISTS public.raw_incoming_packages (
      id uuid DEFAULT gen_random_uuid() NOT NULL,
      name_of_file varchar NULL,
      "object" bytea NULL,
      channel varchar NULL,
      message jsonb NULL,
      received_date timestamptz NULL,
      start_processing_date timestamptz NULL,
      end_processing_date timestamptz NULL,
      status varchar NULL,
      tenant_id varchar(128) NULL,
      subtenant_id varchar(128) NULL,
      CONSTRAINT raw_incoming_packages_pkey PRIMARY KEY (id)
    );
    CREATE INDEX IF NOT EXISTS idx_raw_incoming_status_tenant ON public.raw_incoming_packages USING btree (status, tenant_id, subtenant_id);
    CREATE INDEX IF NOT EXISTS idx_raw_incoming_tenant ON public.raw_incoming_packages USING btree (tenant_id, subtenant_id);
    
    -- Create tenant_configurations table
    CREATE TABLE IF NOT EXISTS public.tenant_configurations (
      id uuid DEFAULT gen_random_uuid() NOT NULL,
      tenant_id varchar(255) NOT NULL,
      subtenant_id varchar(255) NULL,
      configuration_key varchar(255) NOT NULL,
      configuration_value jsonb NOT NULL,
      configuration_type varchar(100) DEFAULT 'user'::character varying NOT NULL,
      "version" int4 DEFAULT 1 NOT NULL,
      is_active bool DEFAULT true NOT NULL,
      created_date timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
      updated_date timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
      CONSTRAINT tenant_configurations_pkey PRIMARY KEY (id)
    );
    
    -- Set ownership
    ALTER TABLE public.raw_incoming_packages OWNER TO backend;
    ALTER TABLE public.tenant_configurations OWNER TO backend;
    GRANT ALL ON TABLE public.raw_incoming_packages TO backend;
    GRANT ALL ON TABLE public.tenant_configurations TO backend;
