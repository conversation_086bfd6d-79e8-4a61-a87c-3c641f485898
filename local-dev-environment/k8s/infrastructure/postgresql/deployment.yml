apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql
  namespace: infrastructure
  labels:
    app: postgresql
    component: database
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
        component: database
    spec:
      containers:
        - name: postgresql
          image: postgres:16
          ports:
            - containerPort: 5432
              name: postgres
          env:
            - name: POSTGRES_USER
              value: "backend"
            - name: POSTGRES_PASSWORD
              value: "localdev123"
            - name: POSTGRES_DB
              value: "postgres"
            - name: PGDATA
              value: "/var/lib/postgresql/data/pgdata"
          volumeMounts:
            - name: postgresql-storage
              mountPath: /var/lib/postgresql/data
            - name: init-scripts
              mountPath: /docker-entrypoint-initdb.d
              readOnly: true
          resources:
            limits:
              memory: "2Gi"
              cpu: "1000m"
            requests:
              memory: "512Mi"
              cpu: "250m"
          livenessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - backend
                - -d
                - postgres
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
          readinessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - backend
                - -d
                - postgres
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
      volumes:
        - name: postgresql-storage
          persistentVolumeClaim:
            claimName: postgresql-pvc
        - name: init-scripts
          configMap:
            name: postgresql-init-scripts
            defaultMode: 0755
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-pvc
  namespace: infrastructure
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
apiVersion: v1
kind: Service
metadata:
  name: postgresql
  namespace: infrastructure
  labels:
    app: postgresql
    component: database
spec:
  type: ClusterIP
  ports:
    - port: 5432
      targetPort: 5432
      protocol: TCP
      name: postgres
  selector:
    app: postgresql
---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-external
  namespace: infrastructure
  labels:
    app: postgresql
    component: database
spec:
  type: NodePort
  ports:
    - port: 5432
      targetPort: 5432
      nodePort: 30432
      protocol: TCP
      name: postgres
  selector:
    app: postgresql
