apiVersion: apps/v1
kind: Deployment
metadata:
  name: sftp-server
  namespace: infrastructure
  labels:
    app: sftp-server
    component: file-transfer
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sftp-server
  template:
    metadata:
      labels:
        app: sftp-server
        component: file-transfer
    spec:
      containers:
        - name: sftp-server
          image: atmoz/sftp:latest
          ports:
            - containerPort: 22
              name: sftp
          env:
            - name: SFTP_USERS
              value: "sftp:test:1001:1001:folder"
          volumeMounts:
            - name: sftp-storage
              mountPath: /home/<USER>/folder
            - name: sftp-config
              mountPath: /etc/sftp
              readOnly: true
          resources:
            limits:
              memory: "256Mi"
              cpu: "200m"
            requests:
              memory: "64Mi"
              cpu: "50m"
          livenessProbe:
            tcpSocket:
              port: 22
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            tcpSocket:
              port: 22
            initialDelaySeconds: 5
            periodSeconds: 5
      volumes:
        - name: sftp-storage
          persistentVolumeClaim:
            claimName: sftp-pvc
        - name: sftp-config
          configMap:
            name: sftp-config
            defaultMode: 0644
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: sftp-pvc
  namespace: infrastructure
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: v1
kind: Service
metadata:
  name: sftp-server
  namespace: infrastructure
  labels:
    app: sftp-server
    component: file-transfer
spec:
  type: ClusterIP
  ports:
    - port: 22
      targetPort: 22
      protocol: TCP
      name: sftp
  selector:
    app: sftp-server
---
apiVersion: v1
kind: Service
metadata:
  name: sftp-server-external
  namespace: infrastructure
  labels:
    app: sftp-server
    component: file-transfer
spec:
  type: NodePort
  ports:
    - port: 22
      targetPort: 22
      nodePort: 30222
      protocol: TCP
      name: sftp
  selector:
    app: sftp-server
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: sftp-config
  namespace: infrastructure
data:
  sshd_config: |
    # SFTP Server Configuration for Local Development
    Port 22
    Protocol 2
    HostKey /etc/ssh/ssh_host_rsa_key
    HostKey /etc/ssh/ssh_host_dsa_key
    HostKey /etc/ssh/ssh_host_ecdsa_key
    HostKey /etc/ssh/ssh_host_ed25519_key
    
    # Logging
    SyslogFacility AUTH
    LogLevel INFO
    
    # Authentication
    LoginGraceTime 120
    PermitRootLogin no
    StrictModes yes
    
    # Password authentication
    PasswordAuthentication yes
    PermitEmptyPasswords no
    
    # SFTP subsystem
    Subsystem sftp internal-sftp
    
    # Chroot configuration
    Match User sftp
        ChrootDirectory /home/<USER>
        ForceCommand internal-sftp
        AllowTcpForwarding no
        X11Forwarding no
