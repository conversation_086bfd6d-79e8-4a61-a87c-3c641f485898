apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
  namespace: infrastructure
  labels:
    app: minio
    component: object-storage
spec:
  replicas: 1
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
        component: object-storage
    spec:
      containers:
        - name: minio
          image: minio/minio:latest
          command:
            - /bin/bash
            - -c
          args:
            - minio server /data --console-address :9001
          ports:
            - containerPort: 9000
              name: api
            - containerPort: 9001
              name: console
          env:
            - name: MINIO_ROOT_USER
              value: "minioadmin"
            - name: MINIO_ROOT_PASSWORD
              value: "minioadmin123"
            - name: MINIO_DEFAULT_BUCKETS
              value: "atom-advantage-packets-local,downloader-intake,splitter-local"
          volumeMounts:
            - name: minio-storage
              mountPath: /data
          resources:
            limits:
              memory: "1Gi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "100m"
          livenessProbe:
            httpGet:
              path: /minio/health/live
              port: 9000
            initialDelaySeconds: 30
            periodSeconds: 20
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /minio/health/ready
              port: 9000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
      volumes:
        - name: minio-storage
          persistentVolumeClaim:
            claimName: minio-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: minio-pvc
  namespace: infrastructure
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: standard
---
apiVersion: v1
kind: Service
metadata:
  name: minio
  namespace: infrastructure
  labels:
    app: minio
    component: object-storage
spec:
  type: ClusterIP
  ports:
    - port: 9000
      targetPort: 9000
      protocol: TCP
      name: api
    - port: 9001
      targetPort: 9001
      protocol: TCP
      name: console
  selector:
    app: minio
---
apiVersion: v1
kind: Service
metadata:
  name: minio-external
  namespace: infrastructure
  labels:
    app: minio
    component: object-storage
spec:
  type: NodePort
  ports:
    - port: 9000
      targetPort: 9000
      nodePort: 30900
      protocol: TCP
      name: api
    - port: 9001
      targetPort: 9001
      nodePort: 30901
      protocol: TCP
      name: console
  selector:
    app: minio
