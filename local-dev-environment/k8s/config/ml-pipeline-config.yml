apiVersion: v1
kind: ConfigMap
metadata:
  name: ml-pipeline-config
  namespace: ml-pipeline
data:
  # Common ML Pipeline Configuration
  PROJECT_NAME: "record_ranger"
  ENVIRONMENT: "local-dev"
  TENANT_NAME: "local-dev"
  LOGGING_LEVEL: "INFO"
  
  # Model Paths
  CLASSIFIER_GENERAL_MODEL_PATH: "/models/general_model.pkl"
  CLASSIFIER_PACKET_MODEL_PATH: "/models/packet_model.pkl"
  CLASSIFIER_HEADER_CONTENT_MODEL_PATH: "/models/header_content_model.pkl"
  
  # Metadata Extractor Model Paths
  MEDICAL_RECORDS_LAYOUT_MODEL_PATH: "/models/medical_records_title_and_text_layout_model.pt"
  RFA_CHECKBOX_MODEL_PATH: "/models/rfa_expedited_checkbox_model.pt"
  RUSH_STAMP_MODEL_PATH: "/models/stamp_is_rush_model.pt"
  WORK_STATUS_CHECKBOX_MODEL_PATH: "/models/work_status_summary_checkbox_model.pt"
  WORK_STATUS_LAYOUT_MODEL_PATH: "/models/work_status_title_and_text_layout_model.pt"
  KEYWORDS_CONFIG_PATH: "/app/keywords_config.yaml"
  
  # Device Configuration
  CLASSIFIER_DEVICE: "cpu"
  
  # Monitoring Configuration
  MONITOR_HOST: "http://localhost"
  MONITOR_PORT: "8080"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: qa-services-config
  namespace: qa-services
data:
  # QA Backend Configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  ACCESS_TOKEN_EXPIRE_MINUTES: "60"
  FRONTEND_URL: "http://localhost:3000"
  
  # QA Frontend Configuration
  REACT_APP_API_BASE_URL: "http://localhost:8000"
  REACT_APP_ENVIRONMENT: "local-dev"
  NODE_ENV: "development"
  PORT: "3000"
  
  # QA Post-Processor Configuration
  CHANNEL: "servicebus_queue"
  ADD_VALIDATION_REPORT_DATA: "no"
  S3_REGION: "us-east-1"
  S3_ENDPOINT_URL: "http://minio.infrastructure.svc.cluster.local:9000"
  AZURE_STORAGE_CONTAINER_NAME: "splitter-local"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: sample-keywords-config
  namespace: ml-pipeline
data:
  keywords_config.yaml: |
    # Sample Keywords Configuration for Local Development
    document_types:
      medical_records:
        keywords:
          - "medical"
          - "patient"
          - "diagnosis"
          - "treatment"
          - "prescription"
        confidence_threshold: 0.8
      
      rfa_forms:
        keywords:
          - "rfa"
          - "request for authorization"
          - "prior authorization"
          - "expedited"
        confidence_threshold: 0.9
      
      work_status:
        keywords:
          - "work status"
          - "employment"
          - "disability"
          - "return to work"
        confidence_threshold: 0.85
    
    extraction_rules:
      date_patterns:
        - "\\d{1,2}/\\d{1,2}/\\d{4}"
        - "\\d{4}-\\d{2}-\\d{2}"
      
      phone_patterns:
        - "\\(\\d{3}\\)\\s*\\d{3}-\\d{4}"
        - "\\d{3}-\\d{3}-\\d{4}"
      
      id_patterns:
        - "ID:\\s*\\w+"
        - "Patient ID:\\s*\\w+"
