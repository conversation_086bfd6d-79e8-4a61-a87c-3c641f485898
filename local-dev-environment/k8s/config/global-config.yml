apiVersion: v1
kind: ConfigMap
metadata:
  name: global-config
  namespace: infrastructure
data:
  # Database Configuration
  PGSQL_HOST: "postgresql.infrastructure.svc.cluster.local"
  PGSQL_PORT: "5432"
  PGSQL_USERNAME: "backend"
  PGSQL_DB_RR: "rr"
  PGSQL_DB_BACKEND: "backend"
  PGSQL_DB_QUEUE: "queue"
  
  # MinIO Configuration
  MINIO_URI: "minio.infrastructure.svc.cluster.local:9000"
  MINIO_ACCESS_KEY: "minioadmin"
  MINIO_FILES_BUCKET: "atom-advantage-packets-local"
  MINIO_SECURE: "false"
  
  # RabbitMQ Configuration
  RABBITMQ_HOST: "rabbitmq.infrastructure.svc.cluster.local"
  RABBITMQ_PORT: "5672"
  RABBITMQ_USERNAME: "user"
  RABBITMQ_DEFAULT_VHOST: "/"
  
  # SFTP Configuration
  SFTP_REMOTE_HOST: "sftp-server.infrastructure.svc.cluster.local"
  SFTP_REMOTE_PORT: "22"
  SFTP_REMOTE_USER: "sftp"
  SFTP_REMOTE_PATH: "folder"
  
  # LLM Server Configuration
  LLM_SERVER_HOST: "llm-server.ml-pipeline.svc.cluster.local"
  LLM_SERVER_PORT: "11434"
  
  # Environment Configuration
  ENVIRONMENT: "local-dev"
  TENANT_NAME: "local-dev"
  LOGGING_LEVEL: "INFO"
  
  # Azure Service Bus Emulator
  AZURE_SERVICE_BUS_HOST: "azure-service-bus-emulator.infrastructure.svc.cluster.local"
  AZURE_SERVICE_BUS_PORT: "5672"
---
apiVersion: v1
kind: Secret
metadata:
  name: global-secrets
  namespace: infrastructure
type: Opaque
stringData:
  # Database Passwords
  PGSQL_PASSWORD: "localdev123"
  
  # MinIO Secrets
  MINIO_SECRET_KEY: "minioadmin123"
  
  # RabbitMQ Secrets
  RABBITMQ_PASSWORD: "localdev123"
  
  # SFTP Secrets
  SFTP_REMOTE_PASSWORD: "test"
  
  # Encryption Keys
  CRYPTOGRAPHY_KEY: "local-dev-encryption-key-32-chars"
  
  # QA Backend Secrets
  QA_BACKEND_SECRET_KEY: "local-dev-secret-key-for-qa-backend"
  
  # Azure Storage Connection String
  AZURE_STORAGE_CONNECTION_STRING: "DefaultEndpointsProtocol=http;AccountName=localdev;AccountKey=localdevkey;BlobEndpoint=http://minio.infrastructure.svc.cluster.local:9000/splitter-local;"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-queues
  namespace: infrastructure
data:
  # ML Pipeline Queues
  TO_CLASSIFY_QUEUE: "to_classify"
  TO_SPLIT_QUEUE: "to_split"
  TO_EXTRACT_METADATA_QUEUE: "to_extract_metadata"
  TO_POSTPROCESS_METADATA_QUEUE: "to_postprocess_metadata"
  TO_VALIDATE_QUEUE: "to_validate"
  TO_UPLOAD_QUEUE: "to_upload"
  
  # QA Queues
  TO_BACKEND_QA_QUEUE: "to_backend_qa"
  TO_BACKEND_OVER_QA_QUEUE: "to_backend_over_qa"
  FROM_BACKEND_QA_QUEUE: "from_backend_qa"
  QUEUE_FROM_BACKEND_QA: "queue_from_backend_qa"
  
  # Status and Monitoring Queues
  PACKET_STATUS_QUEUE: "packet_status"
  
  # Service Bus Queues
  SERVICE_BUS_QUEUE_NAME: "sbq-localdev-service-bus-queue"
  TOPIC_NAME: "splitter-local"
  SUBSCRIPTION_NAME: "local-dev-subscription"
