apiVersion: v1
kind: Namespace
metadata:
  name: infrastructure
  labels:
    name: infrastructure
    app: record-ranger-infrastructure
    environment: local-dev
---
apiVersion: v1
kind: Namespace
metadata:
  name: ml-pipeline
  labels:
    name: ml-pipeline
    app: record-ranger-ml-pipeline
    environment: local-dev
---
apiVersion: v1
kind: Namespace
metadata:
  name: qa-services
  labels:
    name: qa-services
    app: record-ranger-qa-services
    environment: local-dev
