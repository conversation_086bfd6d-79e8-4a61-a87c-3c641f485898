apiVersion: apps/v1
kind: Deployment
metadata:
  name: qa-post-processor
  namespace: qa-services
  labels:
    app: qa-post-processor
    component: qa-processing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: qa-post-processor
  template:
    metadata:
      labels:
        app: qa-post-processor
        component: qa-processing
    spec:
      containers:
        - name: qa-post-processor
          image: localhost:5000/qa-post-processor:latest
          imagePullPolicy: Always
          env:
            - name: PROJECT_NAME
              value: "qa-post-processor"
            - name: APP_NAME
              value: "qa-post-processor-service"
            - name: LOGGING_LEVEL
              value: "INFO"
            - name: RABBITMQ_HOST
              value: "rabbitmq.infrastructure.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "localdev123"
            - name: RABBITMQ_QUEUE_SUB_PACKET_INCOMING
              value: "queue_from_backend_qa"
            - name: TOPIC_NAME
              value: "splitter-local"
            - name: SUBSCRIPTION_NAME
              value: "local-dev-subscription"
            - name: SERVICE_BUS_QUEUE_NAME
              value: "sbq-localdev-service-bus-queue"
            - name: S3_BUCKET_NAME
              value: "atom-advantage-packets-local"
            - name: S3_REGION
              value: "us-east-1"
            - name: S3_ENDPOINT_URL
              value: "http://minio.infrastructure.svc.cluster.local:9000"
            - name: PGSQL_HOST_REMOTE
              value: "postgresql.infrastructure.svc.cluster.local"
            - name: PGSQL_PORT_REMOTE
              value: "5432"
            - name: PGSQL_USERNAME_REMOTE
              value: "backend"
            - name: PGSQL_PASSWORD_REMOTE
              value: "localdev123"
            - name: PGSQL_DB_NAME_REMOTE
              value: "queue"
            - name: PGSQL_SSL_MODE_REMOTE
              value: "disable"
            - name: PGSQL_HOST
              value: "postgresql.infrastructure.svc.cluster.local"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "localdev123"
            - name: PGSQL_DB_NAME
              value: "rr"
            - name: MONITOR_HOST
              value: "http://localhost"
            - name: MONITOR_PORT
              value: "8080"
            - name: CHANNEL
              value: "servicebus_queue"
            - name: ADD_VALIDATION_REPORT_DATA
              value: "no"
            - name: AZURE_STORAGE_CONTAINER_NAME
              value: "splitter-local"
            - name: AZURE_STORAGE_CONNECTION_STRING
              value: "DefaultEndpointsProtocol=http;AccountName=localdev;AccountKey=localdevkey;BlobEndpoint=http://minio.infrastructure.svc.cluster.local:9000/splitter-local;"
            - name: TENANT_NAME
              value: "local-dev"
            - name: CRYPTOGRAPHY_KEY
              value: "local-dev-encryption-key-32-chars"
          resources:
            limits:
              memory: "1Gi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "200m"
          livenessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 30
            periodSeconds: 30
          readinessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 10
            periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: qa-post-processor
  namespace: qa-services
  labels:
    app: qa-post-processor
    component: qa-processing
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: qa-post-processor
