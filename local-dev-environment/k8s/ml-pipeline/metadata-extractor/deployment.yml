apiVersion: apps/v1
kind: Deployment
metadata:
  name: metadata-extractor
  namespace: ml-pipeline
  labels:
    app: metadata-extractor
    component: metadata-extraction
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metadata-extractor
  template:
    metadata:
      labels:
        app: metadata-extractor
        component: metadata-extraction
    spec:
      containers:
        - name: metadata-extractor
          image: localhost:5000/metadata-extractor:latest
          imagePullPolicy: Always
          env:
            - name: PROJECT_NAME
              value: "metadata-extractor"
            - name: APP_NAME
              value: "metadata-extractor-service"
            - name: LOGGING_LEVEL
              value: "INFO"
            - name: MEDICAL_RECORDS_LAYOUT_MODEL_PATH
              value: "/models/medical_records_title_and_text_layout_model.pt"
            - name: RFA_CHECKBOX_MODEL_PATH
              value: "/models/rfa_expedited_checkbox_model.pt"
            - name: RUSH_STAMP_MODEL_PATH
              value: "/models/stamp_is_rush_model.pt"
            - name: WORK_STATUS_CHECKBOX_MODEL_PATH
              value: "/models/work_status_summary_checkbox_model.pt"
            - name: WORK_STATUS_LAYOUT_MODEL_PATH
              value: "/models/work_status_title_and_text_layout_model.pt"
            - name: KEYWORDS_CONFIG_PATH
              value: "/app/keywords_config.yaml"
            - name: RABBITMQ_HOST
              value: "rabbitmq.infrastructure.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME
              value: "to_extract_metadata"
            - name: RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME
              value: "to_postprocess_metadata"
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "localdev123"
            - name: DB_PORT
              value: "5432"
            - name: MINIO_URI
              value: "minio.infrastructure.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "minioadmin"
            - name: MINIO_SECRET_KEY
              value: "minioadmin123"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-local"
            - name: MINIO_SECURE
              value: "false"
            - name: SERVER_HOST
              value: "llm-server.ml-pipeline.svc.cluster.local"
            - name: SERVER_PORT
              value: "11434"
            - name: PGSQL_HOST
              value: "postgresql.infrastructure.svc.cluster.local"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "localdev123"
            - name: PGSQL_DB_NAME
              value: "rr"
          volumeMounts:
            - name: models
              mountPath: /models
          resources:
            limits:
              memory: "2Gi"
              cpu: "1000m"
            requests:
              memory: "512Mi"
              cpu: "250m"
          livenessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 60
            periodSeconds: 30
          readinessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 30
            periodSeconds: 10
      volumes:
        - name: models
          persistentVolumeClaim:
            claimName: metadata-extractor-models-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: metadata-extractor-models-pvc
  namespace: ml-pipeline
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
apiVersion: v1
kind: Service
metadata:
  name: metadata-extractor
  namespace: ml-pipeline
  labels:
    app: metadata-extractor
    component: metadata-extraction
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: metadata-extractor
