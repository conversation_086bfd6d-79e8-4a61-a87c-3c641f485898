apiVersion: apps/v1
kind: Deployment
metadata:
  name: downloader
  namespace: ml-pipeline
  labels:
    app: downloader
    component: document-ingestion
spec:
  replicas: 1
  selector:
    matchLabels:
      app: downloader
  template:
    metadata:
      labels:
        app: downloader
        component: document-ingestion
    spec:
      containers:
        - name: downloader
          image: localhost:5000/downloader:latest
          imagePullPolicy: Always
          env:
            - name: PROJECT_NAME
              value: "record_ranger"
            - name: APP_NAME
              value: "downloader"
            - name: TENANT_NAME
              value: "local-dev"
            - name: CRYPTOGRAPHY_KEY
              value: "local-dev-encryption-key-32-chars"
            - name: MINIO_URI
              value: "minio.infrastructure.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "minioadmin"
            - name: MINIO_SECRET_KEY
              value: "minioadmin123"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-local"
            - name: MINIO_SECURE
              value: "false"
            - name: SFTP_REMOTE_HOST
              value: "sftp-server.infrastructure.svc.cluster.local"
            - name: SFTP_REMOTE_PORT
              value: "22"
            - name: SFTP_REMOTE_USER
              value: "sftp"
            - name: SFTP_REMOTE_PASSWORD
              value: "test"
            - name: SFTP_REMOTE_PATH
              value: "folder"
            - name: RABBITMQ_HOST
              value: "rabbitmq.infrastructure.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "localdev123"
            - name: RABBITMQ_TO_CLASSIFY_QUEUE_NAME
              value: "to_classify"
            - name: RABBITMQ_PACKET_STATUS_QUEUE_NAME
              value: "packet_status"
            - name: PGSQL_HOST
              value: "postgresql.infrastructure.svc.cluster.local"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "localdev123"
            - name: PGSQL_DB_NAME
              value: "rr"
            - name: PGSQL_HOST_REMOTE
              value: "postgresql.infrastructure.svc.cluster.local"
            - name: PGSQL_PORT_REMOTE
              value: "5432"
            - name: PGSQL_USERNAME_REMOTE
              value: "backend"
            - name: PGSQL_PASSWORD_REMOTE
              value: "localdev123"
            - name: PGSQL_DB_NAME_REMOTE
              value: "queue"
            - name: MONITOR_HOST
              value: "http://localhost"
            - name: MONITOR_PORT
              value: "8080"
          resources:
            limits:
              memory: "1000Mi"
              cpu: "500m"
            requests:
              memory: "200Mi"
              cpu: "200m"
          livenessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 30
            periodSeconds: 30
          readinessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 10
            periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: downloader
  namespace: ml-pipeline
  labels:
    app: downloader
    component: document-ingestion
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: downloader
