apiVersion: apps/v1
kind: Deployment
metadata:
  name: llm-server
  namespace: ml-pipeline
  labels:
    app: llm-server
    component: ml-inference
spec:
  replicas: 1
  selector:
    matchLabels:
      app: llm-server
  template:
    metadata:
      labels:
        app: llm-server
        component: ml-inference
    spec:
      containers:
        - name: llm-server
          image: localhost:5000/llm-server:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 11434
              name: ollama-http
          env:
            - name: OLLAMA_HOST
              value: "0.0.0.0"
            - name: OLLAMA_PORT
              value: "11434"
            - name: OLLAMA_MODELS
              value: "/models"
          volumeMounts:
            - name: models-storage
              mountPath: /models
          resources:
            limits:
              memory: "8Gi"
              cpu: "2000m"
            requests:
              memory: "2Gi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /api/tags
              port: 11434
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /api/tags
              port: 11434
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
      volumes:
        - name: models-storage
          persistentVolumeClaim:
            claimName: llm-models-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: llm-models-pvc
  namespace: ml-pipeline
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: standard
---
apiVersion: v1
kind: Service
metadata:
  name: llm-server
  namespace: ml-pipeline
  labels:
    app: llm-server
    component: ml-inference
spec:
  type: ClusterIP
  ports:
    - port: 11434
      targetPort: 11434
      protocol: TCP
      name: ollama-http
  selector:
    app: llm-server
---
apiVersion: v1
kind: Service
metadata:
  name: llm-server-external
  namespace: ml-pipeline
  labels:
    app: llm-server
    component: ml-inference
spec:
  type: NodePort
  ports:
    - port: 11434
      targetPort: 11434
      nodePort: 31434
      protocol: TCP
      name: ollama-http
  selector:
    app: llm-server
