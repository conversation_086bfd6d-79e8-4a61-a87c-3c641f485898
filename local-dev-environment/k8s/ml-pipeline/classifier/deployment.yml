apiVersion: apps/v1
kind: Deployment
metadata:
  name: classifier
  namespace: ml-pipeline
  labels:
    app: classifier
    component: document-classification
spec:
  replicas: 1
  selector:
    matchLabels:
      app: classifier
  template:
    metadata:
      labels:
        app: classifier
        component: document-classification
    spec:
      containers:
        - name: classifier
          image: localhost:5000/classifier:latest
          imagePullPolicy: Always
          env:
            - name: PROJECT_NAME
              value: "classifier"
            - name: APP_NAME
              value: "classifier-service"
            - name: DOWNLOADER_TENANT_NAME
              value: "local-dev"
            - name: LOGGING_LEVEL
              value: "INFO"
            - name: CLASSIFIER_DEVICE
              value: "cpu"
            - name: CLASSIFIER_GENERAL_MODEL_PATH
              value: "/models/general_model.pkl"
            - name: CLASSIFIER_PACKET_MODEL_PATH
              value: "/models/packet_model.pkl"
            - name: CLASSIFIER_HEADER_CONTENT_MODEL_PATH
              value: "/models/header_content_model.pkl"
            - name: RABBITMQ_HOST
              value: "rabbitmq.infrastructure.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_DEFAULT_USER
              value: "user"
            - name: RABBITMQ_DEFAULT_PASS
              value: "localdev123"
            - name: RABBITMQ_TO_CLASSIFY_QUEUE_NAME
              value: "to_classify"
            - name: RABBITMQ_TO_SPLIT_QUEUE_NAME
              value: "to_split"
            - name: DB_HOST
              value: "postgresql.infrastructure.svc.cluster.local"
            - name: DB_PORT
              value: "5432"
            - name: DB_USER
              value: "backend"
            - name: DB_PASSWORD
              value: "localdev123"
            - name: DB_NAME
              value: "rr"
            - name: MINIO_URI
              value: "minio.infrastructure.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "minioadmin"
            - name: MINIO_SECRET_KEY
              value: "minioadmin123"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-local"
            - name: MINIO_SECURE
              value: "false"
          volumeMounts:
            - name: models
              mountPath: /models
          resources:
            limits:
              memory: "1Gi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "200m"
          livenessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 30
            periodSeconds: 30
          readinessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 10
            periodSeconds: 10
      volumes:
        - name: models
          persistentVolumeClaim:
            claimName: classifier-models-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: classifier-models-pvc
  namespace: ml-pipeline
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: v1
kind: Service
metadata:
  name: classifier
  namespace: ml-pipeline
  labels:
    app: classifier
    component: document-classification
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: classifier
