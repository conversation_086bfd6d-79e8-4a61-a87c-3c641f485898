apiVersion: apps/v1
kind: Deployment
metadata:
  name: metadata-post-processor
  namespace: ml-pipeline
  labels:
    app: metadata-post-processor
    component: metadata-processing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metadata-post-processor
  template:
    metadata:
      labels:
        app: metadata-post-processor
        component: metadata-processing
    spec:
      containers:
        - name: metadata-post-processor
          image: localhost:5000/metadata-post-processor:latest
          imagePullPolicy: Always
          env:
            - name: PROJECT_NAME
              value: "metadata-post-processor"
            - name: APP_NAME
              value: "metadata-post-processor-service"
            - name: LOGGING_LEVEL
              value: "INFO"
            - name: RABBITMQ_HOST
              value: "rabbitmq.infrastructure.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "localdev123"
            - name: RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME
              value: "to_postprocess_metadata"
            - name: RABBITMQ_TO_VALIDATE_QUEUE_NAME
              value: "to_validate"
            - name: MINIO_URI
              value: "minio.infrastructure.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "minioadmin"
            - name: MINIO_SECRET_KEY
              value: "minioadmin123"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-local"
            - name: MINIO_SECURE
              value: "false"
            - name: PGSQL_HOST
              value: "postgresql.infrastructure.svc.cluster.local"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "localdev123"
            - name: PGSQL_DB_NAME
              value: "rr"
            - name: TENANT_NAME
              value: "local-dev"
            - name: CRYPTOGRAPHY_KEY
              value: "local-dev-encryption-key-32-chars"
          resources:
            limits:
              memory: "1Gi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "200m"
          livenessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 30
            periodSeconds: 30
          readinessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; sys.exit(0)"
            initialDelaySeconds: 10
            periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: metadata-post-processor
  namespace: ml-pipeline
  labels:
    app: metadata-post-processor
    component: metadata-processing
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: metadata-post-processor
