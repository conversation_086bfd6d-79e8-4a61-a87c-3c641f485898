# Troubleshooting Guide

Quick solutions for common issues in the Record Ranger ML Pipeline local development environment.

## Quick Diagnostics

```bash
# Run automated diagnostics
./scripts/test-environment.sh

# Check overall status
kubectl get pods --all-namespaces
kubectl top nodes
```

## Setup Issues

### Minikube Won't Start
```bash
# Check Docker is running, then recreate cluster
minikube delete --profile=record-ranger-local
./scripts/setup-minikube.sh
```
**Causes**: Insufficient resources, Docker not running, hypervisor conflicts

### Out of Disk Space
```bash
# Clean up and increase disk size
minikube ssh --profile=record-ranger-local "docker system prune -f"
# Edit DISK_SIZE in setup-minikube.sh, then recreate cluster
```

## Pod Issues

### Pods Stuck Pending
```bash
kubectl describe pod <pod-name> -n <namespace>
kubectl top nodes  # Check resources
```
**Solutions**: Increase Minikube memory/CPU, check persistent volumes

### Pods Crashing (CrashLoopBackOff)
```bash
kubectl logs <pod-name> -n <namespace> --previous
kubectl describe pod <pod-name> -n <namespace>
```
**Common causes**: Database connection failures, insufficient resources, missing environment variables

### Image Pull Errors
```bash
# Rebuild images
./scripts/build-images.sh

# Verify images exist
eval $(minikube docker-env --profile=record-ranger-local)
docker images | grep <service-name>
```

## Service Issues

### Database Connection Failures
```bash
# Check PostgreSQL status
kubectl get pods -n infrastructure -l app=postgresql
kubectl logs -n infrastructure deployment/postgresql

# Test connectivity
kubectl exec -n infrastructure deployment/postgresql -- pg_isready -U backend
```
**Solutions**: Restart PostgreSQL pod, verify credentials, check initialization

### Port Forwarding Not Working
```bash
# Kill existing forwards and restart
pkill -f "kubectl port-forward"
./scripts/start-environment.sh
```

### Services Can't Communicate
```bash
# Check service discovery
kubectl get svc --all-namespaces
kubectl exec -n ml-pipeline deployment/downloader -- nslookup postgresql.infrastructure.svc.cluster.local
```

### RabbitMQ Issues
```bash
# Check RabbitMQ status and logs
kubectl logs -n infrastructure deployment/rabbitmq

# Access management interface: http://localhost:15672 (user/localdev123)

# Reset if corrupted
kubectl delete pod -n infrastructure -l app=rabbitmq
```

### QA Frontend Not Loading
```bash
# Check frontend and backend
kubectl logs -n qa-services deployment/qa-frontend
kubectl logs -n qa-services deployment/qa-backend

# Test backend API
curl http://localhost:8000/health
```

### LLM Server Not Responding
```bash
# Check logs and resources (LLM server needs more resources)
kubectl logs -n ml-pipeline deployment/llm-server
kubectl describe pod -n ml-pipeline -l app=llm-server

# Test endpoint
curl http://localhost:11434/api/tags
```

## Recovery Procedures

### Complete Environment Reset
```bash
./scripts/stop-environment.sh
./scripts/cleanup-environment.sh
./scripts/setup-minikube.sh
./scripts/start-environment.sh
```

### Restart Specific Service
```bash
kubectl rollout restart deployment/<service-name> -n <namespace>
# or
kubectl scale deployment <service-name> --replicas=0 -n <namespace>
kubectl scale deployment <service-name> --replicas=1 -n <namespace>
```

### Registry Connection Issues
```bash
# For "connection refused" errors
./scripts/registry-port-forward.sh start

# For "image not found" in Kubernetes
eval $(minikube docker-env --profile=record-ranger-local)
./scripts/build-images.sh
```

## Diagnostic Commands

### Essential Diagnostics
```bash
# Automated health check
./scripts/test-environment.sh

# Check all pods and resources
kubectl get pods --all-namespaces
kubectl top nodes
kubectl top pods --all-namespaces
```

### Service-Specific Debugging
```bash
# Check specific service
kubectl describe deployment <service-name> -n <namespace>
kubectl logs -f deployment/<service-name> -n <namespace>

# Test internal connectivity
kubectl run -it --rm debug --image=busybox --restart=Never -- nslookup postgresql.infrastructure.svc.cluster.local
```

### Log Collection for Support
```bash
# Collect key logs
mkdir -p logs
kubectl logs deployment/postgresql -n infrastructure > logs/postgresql.log
kubectl logs deployment/rabbitmq -n infrastructure > logs/rabbitmq.log
kubectl logs deployment/qa-backend -n qa-services > logs/qa-backend.log

# System information
kubectl get pods --all-namespaces -o wide > logs/pods-status.txt
kubectl get svc --all-namespaces > logs/services-status.txt
```

## Quick Reference

| Issue | Quick Fix |
|-------|-----------|
| Environment not starting | `./scripts/test-environment.sh` |
| Pods stuck pending | `kubectl top nodes` (check resources) |
| Port conflicts | `pkill -f "kubectl port-forward"` |
| Database issues | `kubectl logs -n infrastructure deployment/postgresql` |
| Registry issues | `./scripts/registry-port-forward.sh start` |
| Complete reset needed | `./scripts/cleanup-environment.sh && ./scripts/setup-minikube.sh` |

---
**Still having issues?** Run `./scripts/test-environment.sh` for automated diagnostics or check [REFERENCE.md](REFERENCE.md) for detailed commands.
