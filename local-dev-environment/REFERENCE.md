# Record Ranger ML Pipeline - Quick Reference

Essential commands, ports, and configuration reference for local development.

## Scripts Reference

| Script | Purpose | Usage |
|--------|---------|-------|
| `setup-minikube.sh` | One-time environment setup | `./scripts/setup-minikube.sh` |
| `start-environment.sh` | Start all services | `./scripts/start-environment.sh` |
| `stop-environment.sh` | Stop services (preserves data) | `./scripts/stop-environment.sh` |
| `cleanup-environment.sh` | Complete cleanup | `./scripts/cleanup-environment.sh` |
| `test-environment.sh` | Verify all services | `./scripts/test-environment.sh` |
| `build-images.sh` | Build Docker images | `./scripts/build-images.sh` |
| `registry-port-forward.sh` | Manage registry access | `./scripts/registry-port-forward.sh start\|stop\|status` |

## Service URLs and Ports

### Web Interfaces
| Service | URL | Purpose |
|---------|-----|---------|
| **QA Frontend** | http://localhost:3000 | Document review interface |
| **QA Backend API** | http://localhost:8000 | QA REST API |
| **RabbitMQ Management** | http://localhost:15672 | Queue monitoring |
| **MinIO Console** | http://localhost:9001 | Object storage management |
| **Minikube Dashboard** | `minikube dashboard --profile=record-ranger-local` | Kubernetes dashboard |

### API Endpoints
| Service | URL | Purpose |
|---------|-----|---------|
| **LLM Server** | http://localhost:11434 | Ollama-compatible API |
| **MinIO API** | http://localhost:9000 | S3-compatible storage API |
| **PostgreSQL** | localhost:5432 | Database connection |
| **SFTP Server** | localhost:2222 | Document upload |

## Default Credentials

| Service | Username | Password | Notes |
|---------|----------|----------|-------|
| **PostgreSQL** | backend | localdev123 | Databases: rr, backend, queue |
| **RabbitMQ** | user | localdev123 | Management interface |
| **MinIO** | minioadmin | minioadmin123 | Console and API |
| **SFTP** | sftp | test | Document upload |

## Kubernetes Commands

### Pod Management
```bash
# Check all pods
kubectl get pods --all-namespaces

# Check specific namespace
kubectl get pods -n ml-pipeline
kubectl get pods -n infrastructure
kubectl get pods -n qa-services

# Pod details and logs
kubectl describe pod <pod-name> -n <namespace>
kubectl logs -f deployment/<service-name> -n <namespace>
kubectl logs <pod-name> -n <namespace> --previous
```

### Service Management
```bash
# Check services
kubectl get svc --all-namespaces

# Port forwarding (manual)
kubectl port-forward -n qa-services svc/qa-frontend-external 3000:3000
kubectl port-forward -n qa-services svc/qa-backend-external 8000:8000
kubectl port-forward -n infrastructure svc/rabbitmq-management 15672:15672

# Restart deployment
kubectl rollout restart deployment/<service-name> -n <namespace>

# Scale deployment
kubectl scale deployment <service-name> --replicas=0 -n <namespace>
kubectl scale deployment <service-name> --replicas=1 -n <namespace>
```

### Resource Monitoring
```bash
# Resource usage
kubectl top nodes
kubectl top pods --all-namespaces

# Cluster info
kubectl cluster-info
kubectl get nodes -o wide

# Events
kubectl get events --all-namespaces --sort-by='.lastTimestamp'
```

## Docker Commands

### Image Management
```bash
# Set Minikube Docker environment
eval $(minikube docker-env --profile=record-ranger-local)

# List images
docker images

# Build image
docker build -t <service-name>:latest .

# Tag for registry
docker tag <service-name>:latest localhost:5000/<service-name>:latest

# Push to registry (requires port forwarding)
docker push localhost:5000/<service-name>:latest
```

### Registry Operations
```bash
# Start registry port forwarding
./scripts/registry-port-forward.sh start

# Check registry contents
curl http://localhost:5000/v2/_catalog

# Check specific image tags
curl http://localhost:5000/v2/<service-name>/tags/list

# Stop registry port forwarding
./scripts/registry-port-forward.sh stop
```

## Database Commands

### PostgreSQL Access
```bash
# Connect to PostgreSQL
kubectl exec -it -n infrastructure deployment/postgresql -- psql -U backend -d rr

# Check database status
kubectl exec -n infrastructure deployment/postgresql -- pg_isready -U backend

# Run SQL command
kubectl exec -n infrastructure deployment/postgresql -- psql -U backend -d rr -c "SELECT * FROM documents LIMIT 5;"

# Backup database
kubectl exec -n infrastructure deployment/postgresql -- pg_dump -U backend rr > backup.sql

# Restore database
kubectl cp backup.sql infrastructure/postgresql-pod:/tmp/
kubectl exec -n infrastructure deployment/postgresql -- psql -U backend -d rr -f /tmp/backup.sql
```

## Configuration Files

### Key Configuration Locations
```
local-dev-environment/
├── config/
│   ├── database/init-scripts/          # Database initialization
│   ├── local-registry/registry-config.env  # Registry configuration
│   └── sample-data/                    # Test data
├── k8s/
│   ├── infrastructure/                 # Infrastructure manifests
│   ├── ml-pipeline/                    # ML service manifests
│   ├── qa-services/                    # QA service manifests
│   └── config/                         # ConfigMaps and Secrets
```

### Environment Variables
```bash
# Registry configuration
REGISTRY_HOST=localhost
REGISTRY_PORT=5000
REGISTRY_URL=localhost:5000

# Minikube profile
CLUSTER_NAME=record-ranger-local
```

## Minikube Commands

### Cluster Management
```bash
# Check status
minikube status --profile=record-ranger-local

# Start/stop cluster
minikube start --profile=record-ranger-local
minikube stop --profile=record-ranger-local

# Delete cluster
minikube delete --profile=record-ranger-local

# SSH into cluster
minikube ssh --profile=record-ranger-local

# Get cluster IP
minikube ip --profile=record-ranger-local
```

### Addons
```bash
# List addons
minikube addons list --profile=record-ranger-local

# Enable/disable addon
minikube addons enable registry --profile=record-ranger-local
minikube addons disable registry --profile=record-ranger-local
```

## Troubleshooting Commands

### Quick Diagnostics
```bash
# Automated health check
./scripts/test-environment.sh

# Check system resources
kubectl top nodes
kubectl top pods --all-namespaces

# Check disk space in Minikube
minikube ssh --profile=record-ranger-local "df -h"

# Clean up Docker images
minikube ssh --profile=record-ranger-local "docker system prune -f"
```

### Network Debugging
```bash
# Test internal DNS
kubectl run -it --rm debug --image=busybox --restart=Never -- nslookup postgresql.infrastructure.svc.cluster.local

# Test service connectivity
kubectl run -it --rm debug --image=busybox --restart=Never -- wget -qO- http://qa-backend.qa-services.svc.cluster.local:8000/health

# Kill port forwards
pkill -f "kubectl port-forward"
```

### Log Collection
```bash
# Collect logs for all key services
mkdir -p logs
kubectl logs deployment/postgresql -n infrastructure > logs/postgresql.log
kubectl logs deployment/minio -n infrastructure > logs/minio.log
kubectl logs deployment/rabbitmq -n infrastructure > logs/rabbitmq.log
kubectl logs deployment/qa-backend -n qa-services > logs/qa-backend.log
kubectl logs deployment/qa-frontend -n qa-services > logs/qa-frontend.log
kubectl logs deployment/downloader -n ml-pipeline > logs/downloader.log
kubectl logs deployment/llm-server -n ml-pipeline > logs/llm-server.log
```

## System Architecture

### Namespaces
- **infrastructure**: PostgreSQL, MinIO, RabbitMQ, SFTP Server
- **ml-pipeline**: Document processing services, LLM Server
- **qa-services**: QA Backend, Frontend, Post-Processor

### Processing Flow
```
SFTP → Downloader → Classifier → Splitter → Metadata Extractor → 
Metadata Post-Processor → Validate-Route → Uploader → QA Services
```

### RabbitMQ Queues
```
to_classify → to_split → to_extract_metadata → to_postprocess_metadata → 
to_validate → to_upload → to_backend_qa → from_backend_qa
```

## Quick Recovery

### Complete Reset
```bash
./scripts/stop-environment.sh
./scripts/cleanup-environment.sh
./scripts/setup-minikube.sh
./scripts/start-environment.sh
```

### Service Reset
```bash
kubectl rollout restart deployment/<service-name> -n <namespace>
```

### Registry Reset
```bash
./scripts/registry-port-forward.sh restart
eval $(minikube docker-env --profile=record-ranger-local)
./scripts/build-images.sh
```

---
**For detailed setup instructions**: [SETUP.md](SETUP.md)  
**For troubleshooting**: [TROUBLESHOOTING.md](docs/troubleshooting.md)  
**For daily workflow**: [README.md](README.md)
