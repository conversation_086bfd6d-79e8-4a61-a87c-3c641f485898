version: '3.8'

services:
  # Infrastructure Services
  postgresql:
    image: postgres:16
    container_name: rr-postgresql
    environment:
      POSTGRES_USER: backend
      POSTGRES_PASSWORD: localdev123
      POSTGRES_DB: postgres
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ../config/database/init-scripts:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U backend"]
      interval: 10s
      timeout: 5s
      retries: 5

  minio:
    image: minio/minio:latest
    container_name: rr-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rr-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: user
      RABBITMQ_DEFAULT_PASS: localdev123
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 30s
      timeout: 30s
      retries: 3

  sftp-server:
    image: atmoz/sftp:latest
    container_name: rr-sftp
    command: sftp:test:1001:1001:folder
    ports:
      - "2222:22"
    volumes:
      - sftp_data:/home/<USER>/folder

  # ML Pipeline Services
  llm-server:
    image: localhost:5000/llm-server:latest
    container_name: rr-llm-server
    environment:
      OLLAMA_HOST: 0.0.0.0
      OLLAMA_PORT: 11434
    ports:
      - "11434:11434"
    volumes:
      - llm_models:/models
    depends_on:
      - postgresql
      - minio
      - rabbitmq

  downloader:
    image: localhost:5000/downloader:latest
    container_name: rr-downloader
    environment:
      TENANT_NAME: local-dev
      MINIO_URI: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      MINIO_FILES_BUCKET: atom-advantage-packets-local
      MINIO_SECURE: "false"
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USERNAME: user
      RABBITMQ_PASSWORD: localdev123
      PGSQL_HOST: postgresql
      PGSQL_PORT: 5432
      PGSQL_USERNAME: backend
      PGSQL_PASSWORD: localdev123
      PGSQL_DB_NAME: rr
    depends_on:
      - postgresql
      - minio
      - rabbitmq
      - sftp-server

  classifier:
    image: localhost:5000/classifier:latest
    container_name: rr-classifier
    environment:
      DOWNLOADER_TENANT_NAME: local-dev
      CLASSIFIER_DEVICE: cpu
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_DEFAULT_USER: user
      RABBITMQ_DEFAULT_PASS: localdev123
      MINIO_URI: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      MINIO_FILES_BUCKET: atom-advantage-packets-local
      MINIO_SECURE: "false"
    volumes:
      - classifier_models:/models
    depends_on:
      - postgresql
      - minio
      - rabbitmq

  # QA Services
  qa-backend:
    image: localhost:5000/qa-backend:latest
    container_name: rr-qa-backend
    environment:
      API_HOST: 0.0.0.0
      API_PORT: 8000
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_DEFAULT_USER: user
      RABBITMQ_DEFAULT_PASS: localdev123
      PGSQL_HOST: postgresql
      PGSQL_PORT: 5432
      PGSQL_USERNAME: backend
      PGSQL_PASSWORD: localdev123
      PGSQL_DB_NAME: backend
      FRONTEND_URL: http://localhost:3000
    ports:
      - "8000:8000"
    depends_on:
      - postgresql
      - rabbitmq

  qa-frontend:
    image: localhost:5000/qa-frontend:latest
    container_name: rr-qa-frontend
    environment:
      REACT_APP_API_BASE_URL: http://localhost:8000
      REACT_APP_ENVIRONMENT: local-dev
      NODE_ENV: development
    ports:
      - "3000:3000"
    depends_on:
      - qa-backend

volumes:
  postgresql_data:
  minio_data:
  rabbitmq_data:
  sftp_data:
  llm_models:
  classifier_models:

networks:
  default:
    name: record-ranger-local
