#!/bin/bash

# Record Ranger ML Pipeline - Stop Environment Script
# This script stops all services in the local Minikube cluster

set -e

echo "🛑 Stopping Record Ranger ML Pipeline Local Development Environment"
echo "=================================================================="

# Configuration
CLUSTER_NAME="record-ranger-local"

# Check if Minikube is running
echo "🔍 Checking Minikube status..."
if ! minikube status --profile=$CLUSTER_NAME >/dev/null 2>&1; then
    echo "❌ Minikube cluster '$CLUSTER_NAME' is not running."
    exit 1
fi

echo "✅ Minikube cluster is running"

# Set kubectl context
kubectl config use-context $CLUSTER_NAME

# Stop port forwarding
echo "🌐 Stopping port forwarding..."
pkill -f "kubectl port-forward" || true

# Scale down all deployments to 0 replicas
echo "📉 Scaling down all deployments..."

# QA Services
echo "   - QA Services..."
kubectl scale deployment --all --replicas=0 -n qa-services || true

# ML Pipeline Services
echo "   - ML Pipeline Services..."
kubectl scale deployment --all --replicas=0 -n ml-pipeline || true

# Infrastructure Services
echo "   - Infrastructure Services..."
kubectl scale deployment --all --replicas=0 -n infrastructure || true

# Wait for pods to terminate
echo "⏳ Waiting for pods to terminate..."
kubectl wait --for=delete pod --all -n qa-services --timeout=120s || true
kubectl wait --for=delete pod --all -n ml-pipeline --timeout=120s || true
kubectl wait --for=delete pod --all -n infrastructure --timeout=120s || true

echo "✅ Environment stopped successfully!"
echo ""
echo "📋 Environment Status:"
echo "   - All deployments scaled to 0 replicas"
echo "   - Port forwarding stopped"
echo "   - Persistent data preserved"
echo ""
echo "🔄 To restart: ./start-environment.sh"
echo "🧹 To clean up completely: ./cleanup-environment.sh"
echo ""
