#!/bin/bash

# Record Ranger ML Pipeline - Minikube Setup Script
# This script sets up a local Minikube cluster with all required addons and configurations

set -e

echo "🚀 Setting up Record Ranger ML Pipeline Local Development Environment"
echo "=================================================================="

# Check prerequisites
echo "📋 Checking prerequisites..."

command -v docker >/dev/null 2>&1 || { echo "❌ Docker is required but not installed. Aborting." >&2; exit 1; }
command -v minikube >/dev/null 2>&1 || { echo "❌ Minikube is required but not installed. Aborting." >&2; exit 1; }
command -v kubectl >/dev/null 2>&1 || { echo "❌ kubectl is required but not installed. Aborting." >&2; exit 1; }

echo "✅ All prerequisites found"

# Configuration
CLUSTER_NAME="record-ranger-local"
MEMORY="8192"
CPUS="4"
DISK_SIZE="50g"
KUBERNETES_VERSION="v1.28.3"

echo "🔧 Minikube Configuration:"
echo "   Cluster Name: $CLUSTER_NAME"
echo "   Memory: ${MEMORY}MB"
echo "   CPUs: $CPUS"
echo "   Disk Size: $DISK_SIZE"
echo "   Kubernetes Version: $KUBERNETES_VERSION"

# Start Minikube
echo "🏗️  Starting Minikube cluster..."
minikube start \
  --profile=$CLUSTER_NAME \
  --memory=$MEMORY \
  --cpus=$CPUS \
  --disk-size=$DISK_SIZE \
  --kubernetes-version=$KUBERNETES_VERSION \
  --driver=docker \
  --addons=ingress,dashboard,metrics-server,registry

# Set kubectl context
echo "🔗 Setting kubectl context..."
kubectl config use-context $CLUSTER_NAME

# Enable required addons
echo "🔌 Enabling additional addons..."
minikube addons enable ingress --profile=$CLUSTER_NAME
minikube addons enable dashboard --profile=$CLUSTER_NAME
minikube addons enable metrics-server --profile=$CLUSTER_NAME
minikube addons enable registry --profile=$CLUSTER_NAME
minikube addons enable storage-provisioner --profile=$CLUSTER_NAME

# Create namespaces
echo "📁 Creating namespaces..."
kubectl create namespace infrastructure --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace ml-pipeline --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace qa-services --dry-run=client -o yaml | kubectl apply -f -

# Label namespaces
kubectl label namespace infrastructure app=record-ranger-infrastructure --overwrite
kubectl label namespace ml-pipeline app=record-ranger-ml-pipeline --overwrite
kubectl label namespace qa-services app=record-ranger-qa-services --overwrite

# Setup local Docker registry access
echo "🐳 Setting up local Docker registry access..."

# Get Minikube IP
MINIKUBE_IP=$(minikube ip --profile=$CLUSTER_NAME)

# Try to get registry port from service, fallback to default port 5000
REGISTRY_PORT=$(kubectl get service registry -n kube-system -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "5000")

# If registry port is empty or invalid, use default
if [ -z "$REGISTRY_PORT" ] || [ "$REGISTRY_PORT" = "null" ]; then
    echo "⚠️  Registry service port not found, using default port 5000"
    REGISTRY_PORT="5000"
fi

echo "📝 Local Docker Registry Information:"
echo "   Registry URL: $MINIKUBE_IP:$REGISTRY_PORT"
echo "   To push images: docker tag <image> $MINIKUBE_IP:$REGISTRY_PORT/<image>"
echo "   Then: docker push $MINIKUBE_IP:$REGISTRY_PORT/<image>"

# Create registry configuration
mkdir -p ../config/local-registry
cat > ../config/local-registry/registry-config.env << EOF
REGISTRY_HOST=$MINIKUBE_IP
REGISTRY_PORT=$REGISTRY_PORT
REGISTRY_URL=$MINIKUBE_IP:$REGISTRY_PORT
EOF

echo "✅ Minikube setup complete!"
echo ""
echo "🎯 Next Steps:"
echo "   1. Run './start-environment.sh' to deploy all services"
echo "   2. Access Minikube dashboard: minikube dashboard --profile=$CLUSTER_NAME"
echo "   3. Check cluster status: kubectl get nodes"
echo ""
echo "📊 Cluster Information:"
echo "   Profile: $CLUSTER_NAME"
echo "   Dashboard: minikube dashboard --profile=$CLUSTER_NAME"
echo "   Registry: $MINIKUBE_IP:$REGISTRY_PORT"
echo "   Status: minikube status --profile=$CLUSTER_NAME"
