#!/bin/bash

# Record Ranger ML Pipeline - Registry Port Forward Script
# This script manages port forwarding to the Minikube registry

set -e

CLUSTER_NAME="record-ranger-local"
REGISTRY_PORT="5000"
PID_FILE="/tmp/registry-port-forward.pid"

function start_port_forward() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "✅ Registry port forwarding is already running (PID: $pid)"
            return 0
        else
            rm -f "$PID_FILE"
        fi
    fi
    
    echo "🔗 Starting registry port forwarding..."
    kubectl port-forward --namespace kube-system service/registry $REGISTRY_PORT:80 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    # Wait for port forwarding to be ready
    echo "⏳ Waiting for registry to be accessible..."
    for i in {1..30}; do
        if curl -s "http://localhost:$REGISTRY_PORT/v2/" >/dev/null 2>&1; then
            echo "✅ Registry is accessible at localhost:$REGISTRY_PORT"
            return 0
        fi
        sleep 1
    done
    
    echo "❌ Failed to access registry after 30 seconds"
    stop_port_forward
    return 1
}

function stop_port_forward() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "🛑 Stopping registry port forwarding (PID: $pid)..."
            kill "$pid"
            rm -f "$PID_FILE"
            echo "✅ Registry port forwarding stopped"
        else
            echo "⚠️  Port forwarding process not found, cleaning up PID file"
            rm -f "$PID_FILE"
        fi
    else
        echo "ℹ️  Registry port forwarding is not running"
    fi
}

function status_port_forward() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "✅ Registry port forwarding is running (PID: $pid)"
            if curl -s "http://localhost:$REGISTRY_PORT/v2/" >/dev/null 2>&1; then
                echo "✅ Registry is accessible at localhost:$REGISTRY_PORT"
            else
                echo "⚠️  Registry port forwarding is running but registry is not accessible"
            fi
        else
            echo "❌ Registry port forwarding PID file exists but process is not running"
            rm -f "$PID_FILE"
        fi
    else
        echo "❌ Registry port forwarding is not running"
    fi
}

case "${1:-}" in
    start)
        start_port_forward
        ;;
    stop)
        stop_port_forward
        ;;
    status)
        status_port_forward
        ;;
    restart)
        stop_port_forward
        start_port_forward
        ;;
    *)
        echo "Usage: $0 {start|stop|status|restart}"
        echo ""
        echo "Commands:"
        echo "  start   - Start registry port forwarding"
        echo "  stop    - Stop registry port forwarding"
        echo "  status  - Check registry port forwarding status"
        echo "  restart - Restart registry port forwarding"
        echo ""
        echo "Registry will be accessible at localhost:$REGISTRY_PORT when running"
        exit 1
        ;;
esac
