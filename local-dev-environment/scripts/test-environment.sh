#!/bin/bash

# Record Ranger ML Pipeline - Test Environment Script
# This script runs comprehensive tests on the local development environment

set -e

echo "🧪 Testing Record Ranger ML Pipeline Local Development Environment"
echo "================================================================="

# Configuration
CLUSTER_NAME="record-ranger-local"

# Check if Minikube is running
echo "🔍 Checking Minikube status..."
if ! minikube status --profile=$CLUSTER_NAME >/dev/null 2>&1; then
    echo "❌ Minikube cluster '$CLUSTER_NAME' is not running."
    echo "   Please run './start-environment.sh' first."
    exit 1
fi

echo "✅ Minikube cluster is running"

# Set kubectl context
kubectl config use-context $CLUSTER_NAME

# Test infrastructure services
echo "🏗️  Testing infrastructure services..."

echo "   📊 Testing PostgreSQL..."
if kubectl exec -n infrastructure deployment/postgresql -- pg_isready -U backend >/dev/null 2>&1; then
    echo "   ✅ PostgreSQL is healthy"
else
    echo "   ❌ PostgreSQL is not responding"
fi

echo "   📦 Testing MinIO..."
if kubectl exec -n infrastructure deployment/minio -- curl -f http://localhost:9000/minio/health/live >/dev/null 2>&1; then
    echo "   ✅ MinIO is healthy"
else
    echo "   ❌ MinIO is not responding"
fi

echo "   🐰 Testing RabbitMQ..."
if kubectl exec -n infrastructure deployment/rabbitmq -- rabbitmq-diagnostics -q ping >/dev/null 2>&1; then
    echo "   ✅ RabbitMQ is healthy"
else
    echo "   ❌ RabbitMQ is not responding"
fi

# Test ML Pipeline services
echo "🤖 Testing ML Pipeline services..."

ML_SERVICES=("llm-server" "downloader" "classifier" "splitter" "metadata-extractor" "metadata-post-processor" "validate-route" "uploader")

for service in "${ML_SERVICES[@]}"; do
    echo "   Testing $service..."
    if kubectl get deployment $service -n ml-pipeline >/dev/null 2>&1; then
        READY=$(kubectl get deployment $service -n ml-pipeline -o jsonpath='{.status.readyReplicas}')
        DESIRED=$(kubectl get deployment $service -n ml-pipeline -o jsonpath='{.spec.replicas}')
        if [ "$READY" = "$DESIRED" ] && [ "$READY" != "0" ]; then
            echo "   ✅ $service is ready ($READY/$DESIRED)"
        else
            echo "   ⚠️  $service is not ready ($READY/$DESIRED)"
        fi
    else
        echo "   ❌ $service deployment not found"
    fi
done

# Test QA services
echo "🎯 Testing QA services..."

QA_SERVICES=("qa-backend" "qa-frontend" "qa-post-processor")

for service in "${QA_SERVICES[@]}"; do
    echo "   Testing $service..."
    if kubectl get deployment $service -n qa-services >/dev/null 2>&1; then
        READY=$(kubectl get deployment $service -n qa-services -o jsonpath='{.status.readyReplicas}')
        DESIRED=$(kubectl get deployment $service -n qa-services -o jsonpath='{.spec.replicas}')
        if [ "$READY" = "$DESIRED" ] && [ "$READY" != "0" ]; then
            echo "   ✅ $service is ready ($READY/$DESIRED)"
        else
            echo "   ⚠️  $service is not ready ($READY/$DESIRED)"
        fi
    else
        echo "   ❌ $service deployment not found"
    fi
done

# Test external connectivity
echo "🌐 Testing external connectivity..."

echo "   Testing QA Frontend (port 3000)..."
if curl -f http://localhost:3000 >/dev/null 2>&1; then
    echo "   ✅ QA Frontend is accessible"
else
    echo "   ❌ QA Frontend is not accessible"
fi

echo "   Testing QA Backend (port 8000)..."
if curl -f http://localhost:8000/health >/dev/null 2>&1; then
    echo "   ✅ QA Backend is accessible"
else
    echo "   ❌ QA Backend is not accessible"
fi

echo "   Testing RabbitMQ Management (port 15672)..."
if curl -f http://localhost:15672 >/dev/null 2>&1; then
    echo "   ✅ RabbitMQ Management is accessible"
else
    echo "   ❌ RabbitMQ Management is not accessible"
fi

echo "   Testing MinIO Console (port 9001)..."
if curl -f http://localhost:9001 >/dev/null 2>&1; then
    echo "   ✅ MinIO Console is accessible"
else
    echo "   ❌ MinIO Console is not accessible"
fi

# Test database connectivity
echo "🗄️  Testing database connectivity..."

echo "   Testing database schemas..."
DB_SCHEMAS=("rr" "backend" "queue")

for schema in "${DB_SCHEMAS[@]}"; do
    if kubectl exec -n infrastructure deployment/postgresql -- psql -U backend -d $schema -c "SELECT 1;" >/dev/null 2>&1; then
        echo "   ✅ Database '$schema' is accessible"
    else
        echo "   ❌ Database '$schema' is not accessible"
    fi
done

# Generate test report
echo ""
echo "📋 Test Summary"
echo "==============="

# Count services
TOTAL_PODS=$(kubectl get pods --all-namespaces --no-headers | wc -l)
RUNNING_PODS=$(kubectl get pods --all-namespaces --no-headers | grep Running | wc -l)
READY_PODS=$(kubectl get pods --all-namespaces --no-headers | grep "1/1\|2/2\|3/3" | wc -l)

echo "📊 Pod Status: $RUNNING_PODS/$TOTAL_PODS running, $READY_PODS ready"

# Check persistent volumes
PV_COUNT=$(kubectl get pv --no-headers | wc -l)
PV_BOUND=$(kubectl get pv --no-headers | grep Bound | wc -l)
echo "💾 Persistent Volumes: $PV_BOUND/$PV_COUNT bound"

# Check services
SVC_COUNT=$(kubectl get svc --all-namespaces --no-headers | wc -l)
echo "🌐 Services: $SVC_COUNT total"

echo ""
echo "🎯 Access URLs:"
echo "   📊 QA Frontend:        http://localhost:3000"
echo "   🔧 QA Backend API:     http://localhost:8000"
echo "   🗄️  PostgreSQL:        localhost:5432"
echo "   📦 MinIO Console:      http://localhost:9001"
echo "   🐰 RabbitMQ Mgmt:      http://localhost:15672"
echo "   🤖 LLM Server:         http://localhost:11434"
echo ""

if [ "$RUNNING_PODS" -eq "$TOTAL_PODS" ] && [ "$READY_PODS" -gt 0 ]; then
    echo "✅ Environment test completed successfully!"
    exit 0
else
    echo "⚠️  Environment test completed with issues. Check the logs above."
    exit 1
fi
