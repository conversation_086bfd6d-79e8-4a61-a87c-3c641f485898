#!/bin/bash

# Record Ranger ML Pipeline - Build Images Script
# This script builds all Docker images for local development

set -e

echo "🐳 Building Record Ranger ML Pipeline Docker Images"
echo "=================================================="

# Configuration
CLUSTER_NAME="record-ranger-local"
REGISTRY_CONFIG="../config/local-registry/registry-config.env"

# Load registry configuration
if [ -f "$REGISTRY_CONFIG" ]; then
    source "$REGISTRY_CONFIG"
    echo "📝 Using registry: $REGISTRY_URL"
else
    echo "❌ Registry configuration not found. Please run setup-minikube.sh first."
    exit 1
fi

# Check if Minikube is running
echo "🔍 Checking Minikube status..."
if ! minikube status --profile=$CLUSTER_NAME >/dev/null 2>&1; then
    echo "❌ Minikube cluster '$CLUSTER_NAME' is not running."
    echo "   Please run './setup-minikube.sh' first."
    exit 1
fi

# Set Docker environment to use <PERSON>ku<PERSON>'s Docker daemon
echo "🔧 Setting Docker environment..."
eval $(minikube docker-env --profile=$CLUSTER_NAME)

# Define services to build
declare -a SERVICES=(
    "downloader"
    "classifier" 
    "splitter"
    "metadata-extractor"
    "metadata-post-processor"
    "validate-route"
    "uploader"
    "llm-server"
    "qa-backend"
    "qa-frontend"
    "qa-post-processor"
)

# Build each service
echo "🏗️  Building Docker images..."

for service in "${SERVICES[@]}"; do
    echo "   Building $service..."
    
    # Create a simple Dockerfile for each service if it doesn't exist
    if [ ! -f "../docker-images/$service/Dockerfile" ]; then
        mkdir -p "../docker-images/$service"
        cat > "../docker-images/$service/Dockerfile" << EOF
# Placeholder Dockerfile for $service
FROM python:3.9-slim

WORKDIR /app

# Install basic dependencies
RUN pip install --no-cache-dir fastapi uvicorn pika psycopg2-binary minio

# Create a simple health check endpoint
RUN echo 'from fastapi import FastAPI; app = FastAPI(); @app.get("/health"); def health(): return {"status": "healthy"}' > main.py

EXPOSE 8080

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
EOF
    fi
    
    # Build the image
    docker build -t "$service:latest" "../docker-images/$service/" || {
        echo "⚠️  Failed to build $service, creating placeholder image..."
        docker build -t "$service:latest" - << EOF
FROM alpine:latest
RUN echo "Placeholder for $service" > /placeholder.txt
CMD ["tail", "-f", "/dev/null"]
EOF
    }
    
    # Tag for local registry (using correct localhost URL)
    docker tag "$service:latest" "localhost:5000/$service:latest"
    
    echo "   ✅ $service built successfully"
done

# Push images to local registry (optional)
echo ""
echo "ℹ️  Images are already available in Minikube's Docker daemon."
echo "   For local development, Kubernetes can use these images directly."
echo ""
read -p "📤 Push images to local registry anyway? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📤 Setting up registry access and pushing images..."

    # Check if port forwarding is already active
    if ! curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1; then
        echo "🔗 Setting up port forwarding to registry..."
        kubectl port-forward --namespace kube-system service/registry $REGISTRY_PORT:80 &
        PORT_FORWARD_PID=$!

        # Wait for port forwarding to be ready
        echo "⏳ Waiting for registry to be accessible..."
        for i in {1..30}; do
            if curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1; then
                echo "✅ Registry is accessible"
                break
            fi
            sleep 1
        done

        if ! curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1; then
            echo "❌ Failed to access registry after port forwarding"
            kill $PORT_FORWARD_PID 2>/dev/null
            exit 1
        fi
    fi

    # Push images
    for service in "${SERVICES[@]}"; do
        echo "   Pushing $service..."
        docker push "localhost:5000/$service:latest" || echo "   ⚠️  Failed to push $service"
    done

    # Clean up port forwarding if we started it
    if [ ! -z "$PORT_FORWARD_PID" ]; then
        echo "🧹 Cleaning up port forwarding..."
        kill $PORT_FORWARD_PID 2>/dev/null
    fi
else
    echo "⏭️  Skipping registry push. Images are available in Minikube's Docker daemon."
fi

echo "✅ Image building completed!"
echo ""
echo "📋 Built Images:"
for service in "${SERVICES[@]}"; do
    echo "   - $service:latest"
done
echo ""
echo "🎯 Next Steps:"
echo "   1. Start the environment: ./start-environment.sh"
echo "   2. Check image status: docker images"
echo ""
