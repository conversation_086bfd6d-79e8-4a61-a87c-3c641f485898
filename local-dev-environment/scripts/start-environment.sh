#!/bin/bash

# Record Ranger ML Pipeline - Start Environment Script
# This script deploys all services to the local Minikube cluster

set -e

echo "🚀 Starting Record Ranger ML Pipeline Local Development Environment"
echo "=================================================================="

# Configuration
CLUSTER_NAME="record-ranger-local"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
K8S_DIR="$(dirname "$SCRIPT_DIR")/k8s"

# Check if Minikube is running
echo "🔍 Checking Minikube status..."
if ! minikube status --profile=$CLUSTER_NAME >/dev/null 2>&1; then
    echo "❌ Minikube cluster '$CLUSTER_NAME' is not running."
    echo "   Please run './setup-minikube.sh' first."
    exit 1
fi

echo "✅ Minikube cluster is running"

# Set kubectl context
kubectl config use-context $CLUSTER_NAME

# Apply namespaces first
echo "📁 Creating namespaces..."
kubectl apply -f "$K8S_DIR/namespaces/namespaces.yml"

# Apply global configuration
echo "⚙️  Applying global configuration..."
kubectl apply -f "$K8S_DIR/config/global-config.yml"
kubectl apply -f "$K8S_DIR/config/ml-pipeline-config.yml"

# Deploy infrastructure services
echo "🏗️  Deploying infrastructure services..."
echo "   - PostgreSQL..."
kubectl apply -f "$K8S_DIR/infrastructure/postgresql/"
echo "   - MinIO..."
kubectl apply -f "$K8S_DIR/infrastructure/minio/"
echo "   - RabbitMQ..."
kubectl apply -f "$K8S_DIR/infrastructure/rabbitmq/"
echo "   - SFTP Server..."
kubectl apply -f "$K8S_DIR/infrastructure/sftp-server/"
echo "   - Azure Service Bus Emulator..."
kubectl apply -f "$K8S_DIR/infrastructure/azure-service-bus/"

# Wait for infrastructure to be ready
echo "⏳ Waiting for infrastructure services to be ready..."
kubectl wait --for=condition=ready pod -l app=postgresql -n infrastructure --timeout=300s
kubectl wait --for=condition=ready pod -l app=minio -n infrastructure --timeout=300s
kubectl wait --for=condition=ready pod -l app=rabbitmq -n infrastructure --timeout=300s

# Deploy ML Pipeline services
echo "🤖 Deploying ML Pipeline services..."
echo "   - LLM Server..."
kubectl apply -f "$K8S_DIR/ml-pipeline/llm-server/"
echo "   - Downloader..."
kubectl apply -f "$K8S_DIR/ml-pipeline/downloader/"
echo "   - Classifier..."
kubectl apply -f "$K8S_DIR/ml-pipeline/classifier/"
echo "   - Splitter..."
kubectl apply -f "$K8S_DIR/ml-pipeline/splitter/"
echo "   - Metadata Extractor..."
kubectl apply -f "$K8S_DIR/ml-pipeline/metadata-extractor/"
echo "   - Metadata Post-Processor..."
kubectl apply -f "$K8S_DIR/ml-pipeline/metadata-post-processor/"
echo "   - Validate-Route..."
kubectl apply -f "$K8S_DIR/ml-pipeline/validate-route/"
echo "   - Uploader..."
kubectl apply -f "$K8S_DIR/ml-pipeline/uploader/"

# Deploy QA services
echo "🎯 Deploying QA services..."
echo "   - QA Backend..."
kubectl apply -f "$K8S_DIR/qa-services/qa-backend/"
echo "   - QA Frontend..."
kubectl apply -f "$K8S_DIR/qa-services/qa-frontend/"
echo "   - QA Post-Processor..."
kubectl apply -f "$K8S_DIR/qa-services/qa-post-processor/"

# Setup port forwarding for external access
echo "🌐 Setting up port forwarding..."

# Kill any existing port forwards
pkill -f "kubectl port-forward" || true

# Start port forwarding in background
kubectl port-forward -n infrastructure svc/postgresql-external 5432:5432 &
kubectl port-forward -n infrastructure svc/minio-external 9000:9000 &
kubectl port-forward -n infrastructure svc/minio-external 9001:9001 &
kubectl port-forward -n infrastructure svc/rabbitmq-external 5672:5672 &
kubectl port-forward -n infrastructure svc/rabbitmq-external 15672:15672 &
kubectl port-forward -n infrastructure svc/sftp-server-external 2222:22 &
kubectl port-forward -n ml-pipeline svc/llm-server-external 11434:11434 &
kubectl port-forward -n qa-services svc/qa-backend-external 8000:8000 &
kubectl port-forward -n qa-services svc/qa-frontend-external 3000:3000 &

echo "✅ Environment started successfully!"
echo ""
echo "🎯 Service Access URLs:"
echo "   📊 QA Frontend:        http://localhost:3000"
echo "   🔧 QA Backend API:     http://localhost:8000"
echo "   🗄️  PostgreSQL:        localhost:5432"
echo "   📦 MinIO Console:      http://localhost:9001"
echo "   📦 MinIO API:          http://localhost:9000"
echo "   🐰 RabbitMQ Mgmt:      http://localhost:15672"
echo "   🤖 LLM Server:         http://localhost:11434"
echo "   📁 SFTP Server:        localhost:2222"
echo ""
echo "🔑 Default Credentials:"
echo "   PostgreSQL: backend / localdev123"
echo "   MinIO: minioadmin / minioadmin123"
echo "   RabbitMQ: user / localdev123"
echo "   SFTP: sftp / test"
echo ""
echo "📋 Useful Commands:"
echo "   Check status: kubectl get pods --all-namespaces"
echo "   View logs: kubectl logs -f <pod-name> -n <namespace>"
echo "   Stop environment: ./stop-environment.sh"
echo ""
