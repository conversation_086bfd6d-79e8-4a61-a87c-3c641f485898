#!/bin/bash

# Record Ranger ML Pipeline - Cleanup Environment Script
# This script completely removes all resources from the local Minikube cluster

set -e

echo "🧹 Cleaning up Record Ranger ML Pipeline Local Development Environment"
echo "====================================================================="

# Configuration
CLUSTER_NAME="record-ranger-local"

# Confirmation prompt
read -p "⚠️  This will delete ALL data and resources. Are you sure? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Cleanup cancelled."
    exit 1
fi

# Check if Minikube is running
echo "🔍 Checking Minikube status..."
if ! minikube status --profile=$CLUSTER_NAME >/dev/null 2>&1; then
    echo "❌ Minikube cluster '$CLUSTER_NAME' is not running."
    exit 1
fi

echo "✅ Minikube cluster is running"

# Set kubectl context
kubectl config use-context $CLUSTER_NAME

# Stop port forwarding
echo "🌐 Stopping port forwarding..."
pkill -f "kubectl port-forward" || true

# Delete all resources in namespaces
echo "🗑️  Deleting all resources..."

echo "   - QA Services namespace..."
kubectl delete namespace qa-services --ignore-not-found=true

echo "   - ML Pipeline namespace..."
kubectl delete namespace ml-pipeline --ignore-not-found=true

echo "   - Infrastructure namespace..."
kubectl delete namespace infrastructure --ignore-not-found=true

# Delete persistent volumes
echo "💾 Deleting persistent volumes..."
kubectl delete pv --all --ignore-not-found=true

# Wait for cleanup to complete
echo "⏳ Waiting for cleanup to complete..."
sleep 10

# Option to delete the entire Minikube cluster
read -p "🔥 Do you want to delete the entire Minikube cluster? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔥 Deleting Minikube cluster..."
    minikube delete --profile=$CLUSTER_NAME
    echo "✅ Minikube cluster deleted completely!"
else
    echo "✅ Resources cleaned up, Minikube cluster preserved!"
fi

echo ""
echo "🎯 Cleanup Summary:"
echo "   - All namespaces deleted"
echo "   - All persistent volumes deleted"
echo "   - Port forwarding stopped"
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "   - Minikube cluster deleted"
    echo ""
    echo "🚀 To start fresh: ./setup-minikube.sh"
else
    echo "   - Minikube cluster preserved"
    echo ""
    echo "🚀 To redeploy: ./start-environment.sh"
fi
echo ""
