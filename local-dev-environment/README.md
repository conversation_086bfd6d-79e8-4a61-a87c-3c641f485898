# Record Ranger ML Pipeline - Local Development

Complete local development environment for the Record Ranger ML Pipeline using Minikube and Kubernetes.

## Quick Start

```bash
# 1. Setup environment (one-time)
./scripts/setup-minikube.sh

# 2. Start all services
./scripts/start-environment.sh

# 3. Verify setup
./scripts/test-environment.sh
```

**Ready in ~10 minutes** | **Access QA Frontend**: http://localhost:3000

## Key Services

| Service | URL | Purpose |
|---------|-----|---------|
| **QA Frontend** | http://localhost:3000 | Document review interface |
| **QA Backend** | http://localhost:8000 | QA API |
| **RabbitMQ Management** | http://localhost:15672 | Monitor queues |
| **MinIO Console** | http://localhost:9001 | Object storage |
| **PostgreSQL** | localhost:5432 | Database |

**Default Credentials**: `user/localdev123` (RabbitMQ), `minioadmin/minioadmin123` (MinIO), `backend/localdev123` (PostgreSQL)

## Prerequisites

- **Docker Desktop** or Docker Engine
- **Minikube** (v1.30+)
- **kubectl** (v1.28+)

## Development Workflow

```bash
# Daily startup
./scripts/start-environment.sh

# Upload test documents (optional)
./config/sample-data/upload-sample-data.sh

# Monitor processing
# - RabbitMQ: http://localhost:15672
# - QA Frontend: http://localhost:3000
# - MinIO: http://localhost:9001

# Daily shutdown
./scripts/stop-environment.sh
```

## Common Issues

| Problem | Solution |
|---------|----------|
| Pods not starting | `kubectl top nodes` (check resources) |
| Port conflicts | `pkill -f "kubectl port-forward"` |
| Database errors | Verify PostgreSQL pod: `kubectl get pods -n infrastructure` |
| Image pull errors | `./scripts/build-images.sh` |

## Scripts Reference

| Script | Purpose |
|--------|---------|
| `setup-minikube.sh` | One-time environment setup |
| `start-environment.sh` | Start all services |
| `stop-environment.sh` | Stop services (preserves data) |
| `cleanup-environment.sh` | Complete cleanup |
| `test-environment.sh` | Verify all services |
| `build-images.sh` | Build Docker images |

## Documentation

- **[SETUP.md](SETUP.md)** - Detailed setup instructions
- **[TROUBLESHOOTING.md](TROUBLESHOOTING.md)** - Common issues and solutions
- **[REFERENCE.md](REFERENCE.md)** - Commands, ports, and configuration reference

---
**Need help?** Check [TROUBLESHOOTING.md](TROUBLESHOOTING.md) or run `./scripts/test-environment.sh` for diagnostics.
