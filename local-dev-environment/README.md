# Record Ranger ML Pipeline - Local Development Environment

This directory contains a comprehensive local development environment for the Record Ranger ML pipeline system using Kubernetes and Docker.

## Overview

The local development environment provides:
- **Minikube** for local Kubernetes cluster management
- **Local Docker Registry** for building and storing images from main branch
- **Complete Infrastructure Services**: PostgreSQL, MinIO, RabbitMQ, SFTP Server, Azure Service Bus
- **ML Pipeline Services**: All containerized processes with proper inter-service communication
- **QA Services**: Backend, Frontend, and Post-Processor components
- **HIPAA Compliance**: Maintained throughout the local setup
- **Easy Start/Stop**: Scripts for managing the entire environment

## Quick Start

```bash
# Setup Minikube and local registry
./scripts/setup-minikube.sh

# Start all services
./scripts/start-environment.sh

# Stop all services
./scripts/stop-environment.sh

# Clean up everything
./scripts/cleanup-environment.sh
```

## Directory Structure

```
local-dev-environment/
├── README.md                          # This file
├── scripts/                           # Setup and management scripts
│   ├── setup-minikube.sh             # Initial Minikube setup
│   ├── start-environment.sh          # Start all services
│   ├── stop-environment.sh           # Stop all services
│   ├── cleanup-environment.sh        # Clean up environment
│   └── build-images.sh               # Build local images
├── k8s/                              # Kubernetes manifests
│   ├── infrastructure/               # Infrastructure services
│   │   ├── postgresql/               # PostgreSQL with all schemas
│   │   ├── minio/                    # MinIO object storage
│   │   ├── rabbitmq/                 # RabbitMQ message broker
│   │   ├── sftp-server/              # SFTP server
│   │   └── azure-service-bus/        # Azure Service Bus emulator
│   ├── ml-pipeline/                  # ML Pipeline services
│   │   ├── downloader/               # Document ingestion
│   │   ├── classifier/               # Document classification
│   │   ├── splitter/                 # Document splitting
│   │   ├── metadata-extractor/       # Metadata extraction
│   │   ├── metadata-post-processor/  # Metadata processing
│   │   ├── validate-route/           # Validation and routing
│   │   ├── uploader/                 # Document upload
│   │   └── llm-server/               # LLM inference
│   ├── qa-services/                  # QA Services
│   │   ├── qa-backend/               # QA Backend API
│   │   ├── qa-frontend/              # QA Web Interface
│   │   └── qa-post-processor/        # QA Post-processing
│   ├── config/                       # ConfigMaps and Secrets
│   └── namespaces/                   # Namespace definitions
├── docker-compose/                   # Alternative Docker Compose setup
│   └── docker-compose.yml           # Complete stack in Docker Compose
├── config/                           # Configuration files
│   ├── database/                     # Database initialization
│   │   ├── init-scripts/             # SQL initialization scripts
│   │   └── migrations/               # Database migrations
│   ├── sample-data/                  # Sample datasets for testing
│   └── local-registry/               # Local Docker registry config
└── docs/                             # Documentation
    ├── setup-guide.md                # Detailed setup instructions
    ├── troubleshooting.md            # Common issues and solutions
    └── architecture.md               # Local environment architecture
```

## Services and Ports

### Infrastructure Services
- **PostgreSQL**: `localhost:5432` (rr, backend, queue schemas)
- **MinIO**: `localhost:9000` (Console: `localhost:9001`)
- **RabbitMQ**: `localhost:5672` (Management: `localhost:15672`)
- **SFTP Server**: `localhost:2222`
- **Azure Service Bus Emulator**: `localhost:5671`

### ML Pipeline Services
- **LLM Server**: `localhost:11434` (Ollama-compatible API)
- **All other ML services**: Internal communication via RabbitMQ

### QA Services
- **QA Backend**: `localhost:8000`
- **QA Frontend**: `localhost:3000`

## Prerequisites

- Docker Desktop or Docker Engine
- Minikube
- kubectl
- Helm (optional, for alternative deployments)

## Next Steps

1. Run the setup script: `./scripts/setup-minikube.sh`
2. Start the environment: `./scripts/start-environment.sh`
3. Access the QA Frontend at `http://localhost:3000`
4. Check service health and logs as needed

For detailed instructions, see [docs/setup-guide.md](docs/setup-guide.md).
