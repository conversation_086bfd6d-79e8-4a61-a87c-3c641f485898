# Record Ranger ML Pipeline - Local Development Environment

This directory contains a comprehensive local development environment for the Record Ranger ML pipeline system using Kubernetes and Docker.

## Overview

The local development environment provides:
- **Minikube** for local Kubernetes cluster management
- **Local Docker Registry** for building and storing images from main branch
- **Complete Infrastructure Services**: PostgreSQL, MinIO, RabbitMQ, SFTP Server, Azure Service Bus
- **ML Pipeline Services**: All containerized processes with proper inter-service communication
- **QA Services**: Backend, Frontend, and Post-Processor components
- **HIPAA Compliance**: Maintained throughout the local setup
- **Easy Start/Stop**: Scripts for managing the entire environment

## Quick Start

```bash
# Setup Minikube and local registry
./scripts/setup-minikube.sh

# Start all services
./scripts/start-environment.sh

# Stop all services
./scripts/stop-environment.sh

# Clean up everything
./scripts/cleanup-environment.sh
```

## Services and Ports

### Infrastructure Services
- **PostgreSQL**: `localhost:5432` (rr, backend, queue schemas)
- **MinIO**: `localhost:9000` (Console: `localhost:9001`)
- **RabbitMQ**: `localhost:5672` (Management: `localhost:15672`)
- **SFTP Server**: `localhost:2222`
- **Azure Service Bus Emulator**: `localhost:5671`

### ML Pipeline Services
- **LLM Server**: `localhost:11434` (Ollama-compatible API)
- **All other ML services**: Internal communication via RabbitMQ

### QA Services
- **QA Backend**: `localhost:8000`
- **QA Frontend**: `localhost:3000`

## Prerequisites

- Docker Desktop or Docker Engine
- Minikube
- kubectl
- Helm (optional, for alternative deployments)

## Quick Validation

After setup, verify your environment:

```bash
# Test all services
./scripts/test-environment.sh

# Upload sample data
./config/sample-data/upload-sample-data.sh

# Monitor processing
kubectl logs -f deployment/downloader -n ml-pipeline
```

## Development Workflow

1. **Start Development Session**
   ```bash
   ./scripts/start-environment.sh
   ./scripts/test-environment.sh
   ```

2. **Upload Test Documents**
   ```bash
   ./config/sample-data/upload-sample-data.sh
   ```

3. **Monitor Processing**
   - RabbitMQ Management: http://localhost:15672
   - QA Frontend: http://localhost:3000
   - MinIO Console: http://localhost:9001

4. **Debug Issues**
   ```bash
   kubectl get pods --all-namespaces
   kubectl logs -f deployment/<service> -n <namespace>
   ```

5. **End Development Session**
   ```bash
   ./scripts/stop-environment.sh
   ```

## Architecture Overview

The local environment consists of three main namespaces:

- **Infrastructure** (`infrastructure`): PostgreSQL, MinIO, RabbitMQ, SFTP Server
- **ML Pipeline** (`ml-pipeline`): Document processing services and LLM Server  
- **QA Services** (`qa-services`): QA Backend, Frontend, and Post-Processor

All services communicate via Kubernetes DNS and RabbitMQ message queues, replicating the production architecture at a smaller scale.

## Troubleshooting

Common issues and solutions:

- **Pods not starting**: Check resource allocation with `kubectl top nodes`
- **Port forwarding issues**: Kill existing forwards with `pkill -f "kubectl port-forward"`
- **Database connection errors**: Verify PostgreSQL pod is running and initialized
- **Image pull errors**: Run `./scripts/build-images.sh` to create placeholder images

For comprehensive troubleshooting, see [docs/troubleshooting.md](docs/troubleshooting.md).

## Documentation

- [Setup Guide](docs/setup-guide.md) - Detailed setup instructions
- [Architecture](docs/architecture.md) - System architecture and design
- [Troubleshooting](docs/troubleshooting.md) - Common issues and solutions

## Default Credentials

- **PostgreSQL**: backend / localdev123
- **MinIO**: minioadmin / minioadmin123
- **RabbitMQ**: user / localdev123
- **SFTP**: sftp / test
