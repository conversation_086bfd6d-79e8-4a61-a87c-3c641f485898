# Record Ranger ML Pipeline - Troubleshooting Guide

This guide helps resolve common issues when running the Record Ranger ML Pipeline in the local development environment.

## Common Issues and Solutions

### 1. Minikube Issues

#### Minikube Won't Start
```bash
# Check system resources
minikube status --profile=record-ranger-local

# Delete and recreate cluster
minikube delete --profile=record-ranger-local
./scripts/setup-minikube.sh
```

**Common Causes:**
- Insufficient system resources (CPU/Memory)
- Docker daemon not running
- VirtualBox/Hypervisor conflicts

#### Minikube Out of Disk Space
```bash
# Check disk usage
minikube ssh --profile=record-ranger-local "df -h"

# Clean up unused images
minikube ssh --profile=record-ranger-local "docker system prune -f"

# Increase disk size (requires cluster recreation)
minikube delete --profile=record-ranger-local
# Edit setup-minikube.sh to increase DISK_SIZE
./scripts/setup-minikube.sh
```

### 2. Pod Issues

#### Pods Stuck in Pending State
```bash
# Check pod status and events
kubectl describe pod <pod-name> -n <namespace>

# Check node resources
kubectl top nodes
kubectl describe nodes
```

**Common Solutions:**
- Increase Minikube memory/CPU allocation
- Check persistent volume availability
- Verify image pull policies

#### Pods Crashing (CrashLoopBackOff)
```bash
# Check pod logs
kubectl logs <pod-name> -n <namespace> --previous

# Check resource limits
kubectl describe pod <pod-name> -n <namespace>
```

**Common Causes:**
- Missing environment variables
- Database connection failures
- Insufficient memory/CPU limits
- Missing dependencies in Docker images

#### Image Pull Errors
```bash
# Check if using local registry
kubectl describe pod <pod-name> -n <namespace>

# Verify image exists
docker images | grep <service-name>

# Rebuild images if needed
./scripts/build-images.sh
```

### 3. Database Issues

#### PostgreSQL Connection Failures
```bash
# Check PostgreSQL pod status
kubectl get pods -n infrastructure -l app=postgresql

# Test database connectivity
kubectl exec -n infrastructure deployment/postgresql -- pg_isready -U backend

# Check database logs
kubectl logs -n infrastructure deployment/postgresql
```

**Solutions:**
- Verify database credentials in ConfigMaps/Secrets
- Check if databases were initialized properly
- Restart PostgreSQL pod if needed

#### Database Schema Missing
```bash
# Manually run database initialization
kubectl exec -n infrastructure deployment/postgresql -- psql -U backend -d postgres -c "CREATE DATABASE rr;"

# Copy and run init scripts
kubectl cp config/database/init-scripts/rr.sql infrastructure/postgresql-pod:/tmp/
kubectl exec -n infrastructure deployment/postgresql -- psql -U backend -d rr -f /tmp/rr.sql
```

### 4. Network Issues

#### Port Forwarding Not Working
```bash
# Kill existing port forwards
pkill -f "kubectl port-forward"

# Restart port forwarding
kubectl port-forward -n qa-services svc/qa-frontend-external 3000:3000 &

# Check if ports are in use
netstat -tulpn | grep :3000
```

#### Services Can't Communicate
```bash
# Check service discovery
kubectl get svc --all-namespaces

# Test internal connectivity
kubectl exec -n ml-pipeline deployment/downloader -- nslookup postgresql.infrastructure.svc.cluster.local

# Check network policies
kubectl get networkpolicies --all-namespaces
```

### 5. Storage Issues

#### Persistent Volume Claims Pending
```bash
# Check PVC status
kubectl get pvc --all-namespaces

# Check persistent volumes
kubectl get pv

# Manually create PV if needed
kubectl apply -f k8s/infrastructure/postgresql/deployment.yml
```

#### MinIO Storage Issues
```bash
# Check MinIO pod logs
kubectl logs -n infrastructure deployment/minio

# Access MinIO console
# http://localhost:9001 (minioadmin/minioadmin123)

# Create buckets manually if needed
kubectl exec -n infrastructure deployment/minio -- mc mb /data/atom-advantage-packets-local
```

### 6. RabbitMQ Issues

#### RabbitMQ Not Starting
```bash
# Check RabbitMQ logs
kubectl logs -n infrastructure deployment/rabbitmq

# Check disk space (RabbitMQ is sensitive to disk space)
kubectl exec -n infrastructure deployment/rabbitmq -- df -h

# Reset RabbitMQ data if corrupted
kubectl delete pod -n infrastructure -l app=rabbitmq
```

#### Queues Not Created
```bash
# Access RabbitMQ management interface
# http://localhost:15672 (user/localdev123)

# Manually create queues
kubectl exec -n infrastructure deployment/rabbitmq -- rabbitmqctl add_queue to_classify
```

### 7. ML Pipeline Issues

#### Services Not Processing Messages
```bash
# Check RabbitMQ queue status
# http://localhost:15672 -> Queues tab

# Check service logs
kubectl logs -f deployment/downloader -n ml-pipeline

# Verify environment variables
kubectl describe deployment downloader -n ml-pipeline
```

#### LLM Server Not Responding
```bash
# Check LLM server logs
kubectl logs -n ml-pipeline deployment/llm-server

# Test LLM server endpoint
curl http://localhost:11434/api/tags

# Check resource allocation (LLM server needs more resources)
kubectl describe pod -n ml-pipeline -l app=llm-server
```

### 8. QA Services Issues

#### QA Frontend Not Loading
```bash
# Check frontend pod logs
kubectl logs -n qa-services deployment/qa-frontend

# Verify backend connectivity
curl http://localhost:8000/health

# Check browser console for JavaScript errors
```

#### QA Backend API Errors
```bash
# Check backend logs
kubectl logs -n qa-services deployment/qa-backend

# Test database connectivity from backend
kubectl exec -n qa-services deployment/qa-backend -- psql -h postgresql.infrastructure.svc.cluster.local -U backend -d backend -c "SELECT 1;"
```

## Diagnostic Commands

### General Health Check
```bash
# Run comprehensive test
./scripts/test-environment.sh

# Check all pod status
kubectl get pods --all-namespaces

# Check resource usage
kubectl top pods --all-namespaces
kubectl top nodes
```

### Service-Specific Diagnostics
```bash
# Check specific service
kubectl describe deployment <service-name> -n <namespace>
kubectl logs -f deployment/<service-name> -n <namespace>

# Check service endpoints
kubectl get endpoints -n <namespace>

# Check configmaps and secrets
kubectl get configmaps -n <namespace>
kubectl get secrets -n <namespace>
```

### Network Diagnostics
```bash
# Test internal DNS resolution
kubectl run -it --rm debug --image=busybox --restart=Never -- nslookup postgresql.infrastructure.svc.cluster.local

# Test service connectivity
kubectl run -it --rm debug --image=busybox --restart=Never -- wget -qO- http://qa-backend.qa-services.svc.cluster.local:8000/health
```

## Recovery Procedures

### Complete Environment Reset
```bash
# Stop everything
./scripts/stop-environment.sh

# Clean up completely
./scripts/cleanup-environment.sh

# Recreate from scratch
./scripts/setup-minikube.sh
./scripts/start-environment.sh
```

### Partial Service Reset
```bash
# Restart specific service
kubectl rollout restart deployment/<service-name> -n <namespace>

# Scale down and up
kubectl scale deployment <service-name> --replicas=0 -n <namespace>
kubectl scale deployment <service-name> --replicas=1 -n <namespace>
```

### Data Recovery
```bash
# Backup persistent data before cleanup
kubectl exec -n infrastructure deployment/postgresql -- pg_dump -U backend rr > backup_rr.sql

# Restore after recreation
kubectl cp backup_rr.sql infrastructure/postgresql-pod:/tmp/
kubectl exec -n infrastructure deployment/postgresql -- psql -U backend -d rr -f /tmp/backup_rr.sql
```

## Getting Help

### Log Collection
```bash
# Collect all logs
mkdir -p logs
kubectl logs deployment/postgresql -n infrastructure > logs/postgresql.log
kubectl logs deployment/minio -n infrastructure > logs/minio.log
kubectl logs deployment/rabbitmq -n infrastructure > logs/rabbitmq.log
# ... repeat for all services
```

### System Information
```bash
# Collect system info
minikube version > system-info.txt
kubectl version >> system-info.txt
docker version >> system-info.txt
kubectl get nodes -o wide >> system-info.txt
kubectl get pods --all-namespaces -o wide >> system-info.txt
```

### Contact Information
For additional support:
- Check the main README.md for project contacts
- Review the architecture documentation
- Consult the original Flux repository configurations
