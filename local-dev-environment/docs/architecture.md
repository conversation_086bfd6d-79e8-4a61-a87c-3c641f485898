# Record Ranger ML Pipeline - Local Development Architecture

This document describes the architecture and design of the local development environment for the Record Ranger ML Pipeline system.

## Overview

The local development environment replicates the production Record Ranger ML Pipeline using Minikube and Kubernetes, providing a complete, self-contained system for development and testing.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                    Local Development Environment                 │
│                         (Minikube Cluster)                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Infrastructure │  │   ML Pipeline   │  │   QA Services   │ │
│  │    Namespace     │  │    Namespace    │  │    Namespace    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Component Architecture

### Infrastructure Services (infrastructure namespace)

#### PostgreSQL Database
- **Purpose**: Primary data storage for all schemas
- **Databases**: `rr`, `backend`, `queue`
- **Access**: Internal cluster + external port 5432
- **Storage**: Persistent volume (10GB)

#### MinIO Object Storage
- **Purpose**: S3-compatible object storage for documents
- **Buckets**: `atom-advantage-packets-local`, `downloader-intake`, `splitter-local`
- **Access**: API port 9000, Console port 9001
- **Storage**: Persistent volume (20GB)

#### RabbitMQ Message Broker
- **Purpose**: Inter-service communication and job queuing
- **Queues**: ML pipeline queues, QA queues, status queues
- **Access**: AMQP port 5672, Management port 15672
- **Storage**: Persistent volume (5GB)

#### SFTP Server
- **Purpose**: Document ingestion endpoint
- **Access**: Port 2222
- **Storage**: Persistent volume (5GB)

#### Azure Service Bus Emulator
- **Purpose**: Emulates Azure Service Bus for local development
- **Implementation**: Apache Qpid Broker-J
- **Access**: AMQP port 5671, Management port 8080

### ML Pipeline Services (ml-pipeline namespace)

#### Document Processing Flow
```
SFTP → Downloader → Classifier → Splitter → Metadata Extractor → 
Metadata Post-Processor → Validate-Route → Uploader
```

#### Service Details

**Downloader** 📥
- Monitors SFTP server for new documents
- Uploads documents to MinIO
- Sends classification requests to RabbitMQ

**Classifier** 🏷️
- Classifies document types using ML models
- Routes documents based on classification
- Stores results in PostgreSQL

**Splitter** ✂️
- Splits multi-page documents
- Handles document segmentation
- Prepares documents for metadata extraction

**Metadata Extractor** 📝
- Extracts metadata using ML models
- Communicates with LLM Server for advanced processing
- Stores extracted metadata

**Metadata Post-Processor** 🔄
- Post-processes extracted metadata
- Applies business rules and validation
- Prepares for QA review

**Validate-Route** ✅
- Validates processed documents
- Routes to appropriate next steps
- Handles exception cases

**Uploader** 📤
- Handles final document upload
- Manages document delivery
- Updates status tracking

**LLM Server** 🤖
- Provides large language model inference
- Ollama-compatible API
- Supports various ML models

### QA Services (qa-services namespace)

#### QA Backend 🎯
- RESTful API for QA operations
- Manages QA workflow and user sessions
- Integrates with PostgreSQL and RabbitMQ
- **Access**: Port 8000

#### QA Frontend 🖥️
- React-based web interface
- Document review and approval interface
- Real-time status updates
- **Access**: Port 3000

#### QA Post-Processor 🔄
- Handles post-QA processing
- Manages approved document workflow
- Integrates with external systems

## Data Flow

### Document Processing Pipeline

1. **Ingestion**
   - Documents uploaded to SFTP server
   - Downloader monitors and retrieves files
   - Files stored in MinIO object storage

2. **Classification**
   - Classifier analyzes document types
   - Results stored in PostgreSQL
   - Routing decisions made based on classification

3. **Processing**
   - Documents split and processed
   - Metadata extracted using ML models
   - LLM Server provides advanced analysis

4. **Quality Assurance**
   - Documents routed to QA system
   - Human review via QA Frontend
   - Approval/rejection workflow

5. **Finalization**
   - Approved documents processed
   - Final upload and delivery
   - Status tracking and reporting

### Message Flow (RabbitMQ Queues)

```
to_classify → to_split → to_extract_metadata → to_postprocess_metadata → 
to_validate → to_upload → to_backend_qa → from_backend_qa
```

## Network Architecture

### Internal Networking
- **Cluster DNS**: `<service>.<namespace>.svc.cluster.local`
- **Service Discovery**: Kubernetes native DNS
- **Load Balancing**: Kubernetes Services (ClusterIP)

### External Access
- **Port Forwarding**: kubectl port-forward for development
- **NodePort Services**: Direct access to specific services
- **Ingress**: Not configured (using port forwarding instead)

### Service Communication
- **Database**: PostgreSQL connection pooling
- **Message Queues**: RabbitMQ AMQP protocol
- **Object Storage**: MinIO S3-compatible API
- **HTTP APIs**: RESTful APIs between services

## Storage Architecture

### Persistent Volumes
- **PostgreSQL**: Database files and WAL logs
- **MinIO**: Object storage data
- **RabbitMQ**: Message persistence and metadata
- **SFTP**: Uploaded documents (temporary)
- **ML Models**: Model files for inference

### Data Persistence Strategy
- **Development**: Local hostPath volumes
- **Production**: Would use cloud storage classes
- **Backup**: Manual backup procedures for development

## Security Considerations

### Local Development Security
- **Default Credentials**: Simple passwords for ease of use
- **Network Isolation**: Services isolated within Minikube
- **No TLS**: HTTP communication for simplicity
- **No Authentication**: Simplified for development

### Production Differences
- **Strong Passwords**: Complex, rotated credentials
- **TLS Encryption**: All communication encrypted
- **RBAC**: Role-based access control
- **Network Policies**: Strict network segmentation

## Configuration Management

### ConfigMaps
- **Global Config**: Shared configuration across services
- **Service-Specific**: Individual service configurations
- **Environment Variables**: Runtime configuration

### Secrets
- **Database Passwords**: PostgreSQL credentials
- **API Keys**: Service authentication
- **Encryption Keys**: Data encryption keys

### Environment Variables
- **Service Discovery**: Kubernetes DNS names
- **Feature Flags**: Development-specific features
- **Resource Limits**: Memory and CPU constraints

## Monitoring and Observability

### Available Monitoring
- **Kubernetes Dashboard**: Cluster overview
- **RabbitMQ Management**: Queue monitoring
- **MinIO Console**: Storage monitoring
- **Application Logs**: kubectl logs access

### Metrics Collection
- **Kubernetes Metrics**: CPU, memory, disk usage
- **Application Metrics**: Service-specific metrics
- **Custom Metrics**: Business logic metrics

### Logging Strategy
- **Centralized Logging**: All logs via kubectl
- **Log Levels**: Configurable per service
- **Log Retention**: Local development only

## Scalability Considerations

### Current Limitations
- **Single Replica**: All services run single instance
- **Resource Constraints**: Limited by local machine
- **No Auto-scaling**: Manual scaling only

### Scaling Options
- **Horizontal Scaling**: Increase replica count
- **Vertical Scaling**: Increase resource limits
- **Load Testing**: Use sample data for testing

## Development Workflow

### Local Development Process
1. **Setup**: Run setup scripts
2. **Development**: Modify configurations/code
3. **Testing**: Use test scripts and sample data
4. **Debugging**: Access logs and monitoring
5. **Cleanup**: Reset environment as needed

### Integration Points
- **Source Code**: Mount local code for development
- **Configuration**: Easy config modification
- **Data**: Sample data for testing
- **Monitoring**: Real-time feedback

## Comparison with Production

### Similarities
- **Service Architecture**: Same microservices design
- **Data Flow**: Identical processing pipeline
- **APIs**: Same interface contracts
- **Message Queues**: Same queue structure

### Differences
- **Scale**: Single instance vs. multiple replicas
- **Security**: Simplified vs. production-grade
- **Storage**: Local volumes vs. cloud storage
- **Networking**: Port forwarding vs. ingress/load balancers
- **Monitoring**: Basic vs. comprehensive observability

This architecture provides a faithful representation of the production system while maintaining simplicity for local development and testing.
