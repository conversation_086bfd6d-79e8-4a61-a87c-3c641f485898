# Record Ranger ML Pipeline - Local Registry Setup

## Overview

The Record Ranger ML Pipeline local development environment uses Minikube's built-in registry addon for container image management. This document explains how the registry works and how to use it effectively.

## Registry Architecture

### Minikube Registry Components

1. **Registry Service**: A ClusterIP service running on port 80/443 within the cluster
2. **Registry Pod**: The actual Docker registry container
3. **Registry Proxy**: A DaemonSet that provides registry access to nodes

### Access Methods

#### Method 1: Direct Docker Daemon (Recommended for Development)
When using `eval $(minikube docker-env)`, images are built directly in Minikube's Docker daemon and are immediately available to Kubernetes without needing to push to a registry.

```bash
# Set Docker environment to use Minikube's Docker daemon
eval $(minikube docker-env --profile=record-ranger-local)

# Build images - they're immediately available to Kubernetes
docker build -t my-service:latest .

# No push needed - Kubernetes can use the image directly
```

#### Method 2: Registry Push/Pull (For External Access)
For pushing images from external Docker daemons or for sharing images, use port forwarding to access the registry.

```bash
# Start port forwarding to registry
./scripts/registry-port-forward.sh start

# Tag and push image
docker tag my-service:latest localhost:5000/my-service:latest
docker push localhost:5000/my-service:latest

# Stop port forwarding when done
./scripts/registry-port-forward.sh stop
```

## Scripts and Tools

### registry-port-forward.sh
Manages port forwarding to the Minikube registry.

```bash
# Start port forwarding
./registry-port-forward.sh start

# Check status
./registry-port-forward.sh status

# Stop port forwarding
./registry-port-forward.sh stop

# Restart port forwarding
./registry-port-forward.sh restart
```

### build-images.sh
Builds all Docker images for the ML pipeline.

- Builds images using Minikube's Docker daemon
- Optionally pushes to registry (requires port forwarding)
- Images are immediately available to Kubernetes

### Configuration Files

#### registry-config.env
Contains registry connection details:
```
REGISTRY_HOST=localhost
REGISTRY_PORT=5000
REGISTRY_URL=localhost:5000
```

## Usage Patterns

### For Local Development (Recommended)
```bash
# 1. Set up Minikube environment
./setup-minikube.sh

# 2. Build images (they're immediately available)
./build-images.sh
# Choose 'N' when asked about pushing to registry

# 3. Deploy services
./start-environment.sh
```

### For Registry Testing/External Access
```bash
# 1. Start port forwarding
./registry-port-forward.sh start

# 2. Build and push images
./build-images.sh
# Choose 'y' when asked about pushing to registry

# 3. Verify images in registry
curl http://localhost:5000/v2/_catalog

# 4. Stop port forwarding when done
./registry-port-forward.sh stop
```

## Troubleshooting

### Common Issues

#### "Connection refused" errors
- **Cause**: Registry port forwarding is not active
- **Solution**: Run `./registry-port-forward.sh start`

#### "Image not found" in Kubernetes
- **Cause**: Image was built on host Docker daemon, not Minikube's
- **Solution**: Use `eval $(minikube docker-env)` before building

#### Port forwarding fails
- **Cause**: Registry addon not enabled or pod not running
- **Solution**: 
  ```bash
  minikube addons enable registry --profile=record-ranger-local
  kubectl get pods -n kube-system | grep registry
  ```

### Verification Commands

```bash
# Check registry addon status
minikube addons list --profile=record-ranger-local | grep registry

# Check registry pods
kubectl get pods -n kube-system | grep registry

# Check registry service
kubectl get service registry -n kube-system

# Test registry access (with port forwarding active)
curl http://localhost:5000/v2/

# List images in registry
curl http://localhost:5000/v2/_catalog
```

## Best Practices

1. **Use Minikube Docker daemon for development**: Faster and doesn't require registry pushes
2. **Only use registry for external access**: When you need to share images or test registry functionality
3. **Clean up port forwarding**: Always stop port forwarding when done to free up ports
4. **Monitor registry storage**: Registry storage is limited by Minikube's disk space

## Security Notes

- The local registry runs without authentication (suitable for development only)
- Registry is only accessible via localhost when port forwarding is active
- Images stored in registry are lost when Minikube cluster is deleted
