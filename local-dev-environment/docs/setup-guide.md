# Record Ranger ML Pipeline - Local Development Setup Guide

This guide provides detailed instructions for setting up and running the Record Ranger ML Pipeline in a local development environment using Minikube and Kubernetes.

## Prerequisites

Before starting, ensure you have the following tools installed:

### Required Software
- **Docker Desktop** (v4.0+) or **Docker Engine** (v20.0+)
- **Minikube** (v1.30+)
- **kubectl** (v1.28+)
- **Git** (for cloning repositories)

### Optional Tools
- **Helm** (v3.0+) - for alternative deployment methods
- **sshpass** - for automated SFTP uploads
- **curl** - for testing API endpoints

### System Requirements
- **CPU**: 4+ cores recommended
- **Memory**: 8GB+ RAM recommended
- **Disk**: 50GB+ free space
- **OS**: Linux, macOS, or Windows with WSL2

## Installation Steps

### 1. Install Prerequisites

#### Docker Desktop
```bash
# macOS (using Homebrew)
brew install --cask docker

# Linux (Ubuntu/Debian)
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Windows
# Download from https://www.docker.com/products/docker-desktop
```

#### Minikube
```bash
# macOS
brew install minikube

# Linux
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
sudo install minikube-linux-amd64 /usr/local/bin/minikube

# Windows
# Download from https://minikube.sigs.k8s.io/docs/start/
```

#### kubectl
```bash
# macOS
brew install kubectl

# Linux
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Windows
# Download from https://kubernetes.io/docs/tasks/tools/install-kubectl-windows/
```

### 2. Clone the Repository

```bash
git clone <repository-url>
cd aa_record_ranger_architecture_docs/local-dev-environment
```

### 3. Setup Minikube Cluster

Run the setup script to create and configure the Minikube cluster:

```bash
./scripts/setup-minikube.sh
```

This script will:
- Create a Minikube cluster named `record-ranger-local`
- Configure required addons (ingress, dashboard, metrics-server, registry)
- Create necessary namespaces
- Setup persistent volumes
- Configure local Docker registry

### 4. Build Docker Images (Optional)

If you have access to the source code repositories, build the Docker images:

```bash
./scripts/build-images.sh
```

This will create placeholder images for all services. Replace with actual builds when source code is available.

### 5. Start the Environment

Deploy all services to the cluster:

```bash
./scripts/start-environment.sh
```

This script will:
- Deploy infrastructure services (PostgreSQL, MinIO, RabbitMQ, etc.)
- Deploy ML pipeline services
- Deploy QA services
- Setup port forwarding for external access
- Display access URLs and credentials

### 6. Verify the Setup

Test the environment to ensure all services are running correctly:

```bash
./scripts/test-environment.sh
```

### 7. Upload Sample Data (Optional)

Upload sample documents for testing:

```bash
./config/sample-data/upload-sample-data.sh
```

## Service Access

Once the environment is running, you can access the following services:

### Web Interfaces
- **QA Frontend**: http://localhost:3000
- **QA Backend API**: http://localhost:8000
- **MinIO Console**: http://localhost:9001
- **RabbitMQ Management**: http://localhost:15672

### Database and Storage
- **PostgreSQL**: localhost:5432
- **MinIO API**: http://localhost:9000
- **SFTP Server**: localhost:2222

### ML Services
- **LLM Server**: http://localhost:11434

## Default Credentials

### PostgreSQL
- **Username**: backend
- **Password**: localdev123
- **Databases**: rr, backend, queue

### MinIO
- **Username**: minioadmin
- **Password**: minioadmin123

### RabbitMQ
- **Username**: user
- **Password**: localdev123

### SFTP Server
- **Username**: sftp
- **Password**: test

## Common Operations

### Starting/Stopping the Environment

```bash
# Start all services
./scripts/start-environment.sh

# Stop all services (preserves data)
./scripts/stop-environment.sh

# Complete cleanup (removes all data)
./scripts/cleanup-environment.sh
```

### Monitoring Services

```bash
# Check all pods
kubectl get pods --all-namespaces

# Check specific service logs
kubectl logs -f deployment/qa-backend -n qa-services

# Check service status
kubectl get svc --all-namespaces
```

### Accessing Minikube Dashboard

```bash
minikube dashboard --profile=record-ranger-local
```

## Alternative: Docker Compose

For simpler setups, you can use Docker Compose instead of Kubernetes:

```bash
cd docker-compose
docker-compose up -d
```

This provides the same services but with simpler networking and configuration.

## Next Steps

1. **Explore the QA Frontend** at http://localhost:3000
2. **Upload test documents** using the sample data script
3. **Monitor processing** through RabbitMQ management interface
4. **Review logs** to understand the ML pipeline flow
5. **Customize configurations** as needed for your development work

For troubleshooting, see [troubleshooting.md](troubleshooting.md).
For architecture details, see [architecture.md](architecture.md).
