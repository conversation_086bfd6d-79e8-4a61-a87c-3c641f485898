# Record Ranger ML Pipeline - Setup Guide

Complete setup instructions for the local development environment.

## Prerequisites

### Required Software
- **Docker Desktop** (v4.0+) or **Docker Engine** (v20.0+)
- **Minikube** (v1.30+)
- **kubectl** (v1.28+)

### System Requirements
- **CPU**: 4+ cores recommended
- **Memory**: 8GB+ RAM recommended  
- **Disk**: 50GB+ free space

## Installation

### 1. Install Prerequisites

#### macOS (using Homebrew)
```bash
brew install --cask docker
brew install minikube kubectl
```

#### Linux (Ubuntu/Debian)
```bash
# Docker
curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh

# Minikube
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
sudo install minikube-linux-amd64 /usr/local/bin/minikube

# kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
```

#### Windows
- **Docker Desktop**: Download from https://www.docker.com/products/docker-desktop
- **Minikube**: Download from https://minikube.sigs.k8s.io/docs/start/
- **kubectl**: Download from https://kubernetes.io/docs/tasks/tools/install-kubectl-windows/

### 2. Clone Repository

```bash
git clone <repository-url>
cd aa_record_ranger_architecture_docs/local-dev-environment
```

## Setup Process

### 1. Initialize Minikube Cluster

```bash
./scripts/setup-minikube.sh
```

**This script will:**
- Create Minikube cluster named `record-ranger-local`
- Enable required addons (ingress, dashboard, metrics-server, registry)
- Create namespaces: `infrastructure`, `ml-pipeline`, `qa-services`
- Configure local Docker registry access
- Set up persistent volumes

**Expected output:** ✅ Minikube setup complete!

### 2. Build Docker Images

```bash
./scripts/build-images.sh
```

**Choose 'N' when prompted** about pushing to registry (images are immediately available in Minikube).

**This script will:**
- Build placeholder images for all 11 services
- Tag images for local use
- Make images available to Kubernetes

### 3. Start All Services

```bash
./scripts/start-environment.sh
```

**This script will:**
- Deploy infrastructure services (PostgreSQL, MinIO, RabbitMQ, SFTP)
- Deploy ML pipeline services (downloader, classifier, splitter, etc.)
- Deploy QA services (backend, frontend, post-processor)
- Set up port forwarding for external access
- Display service URLs and credentials

**Expected time:** 5-10 minutes for all pods to be ready

### 4. Verify Setup

```bash
./scripts/test-environment.sh
```

**This will test:**
- All pods are running
- Services are accessible
- Database connections work
- Port forwarding is active

## Service Access

Once setup is complete, access these services:

| Service | URL | Credentials |
|---------|-----|-------------|
| **QA Frontend** | http://localhost:3000 | None |
| **QA Backend API** | http://localhost:8000 | None |
| **RabbitMQ Management** | http://localhost:15672 | user / localdev123 |
| **MinIO Console** | http://localhost:9001 | minioadmin / minioadmin123 |
| **PostgreSQL** | localhost:5432 | backend / localdev123 |
| **LLM Server** | http://localhost:11434 | None |

## Docker Registry Setup

The local environment includes a Docker registry for image management.

### For Development (Recommended)
Images built with `eval $(minikube docker-env)` are immediately available to Kubernetes:

```bash
# Set Docker environment
eval $(minikube docker-env --profile=record-ranger-local)

# Build images - no push needed
docker build -t my-service:latest .
```

### For Registry Push/Pull
If you need to push images to the registry:

```bash
# Start registry port forwarding
./scripts/registry-port-forward.sh start

# Tag and push image
docker tag my-service:latest localhost:5000/my-service:latest
docker push localhost:5000/my-service:latest

# Stop port forwarding
./scripts/registry-port-forward.sh stop
```

## Testing the Environment

### Upload Sample Data
```bash
./config/sample-data/upload-sample-data.sh
```

### Monitor Processing
- **RabbitMQ Queues**: http://localhost:15672 → Queues tab
- **Service Logs**: `kubectl logs -f deployment/downloader -n ml-pipeline`
- **Pod Status**: `kubectl get pods --all-namespaces`

### Check Service Health
```bash
# Test individual services
curl http://localhost:8000/health  # QA Backend
curl http://localhost:11434/api/tags  # LLM Server

# Check database connectivity
kubectl exec -n infrastructure deployment/postgresql -- pg_isready -U backend
```

## Daily Operations

### Start Development Session
```bash
./scripts/start-environment.sh
./scripts/test-environment.sh  # Verify everything is working
```

### Stop Development Session
```bash
./scripts/stop-environment.sh  # Preserves data
```

### Complete Reset (if needed)
```bash
./scripts/cleanup-environment.sh  # Removes all data
./scripts/setup-minikube.sh       # Recreate from scratch
```

## Alternative: Docker Compose

For simpler setups without Kubernetes:

```bash
cd docker-compose
docker-compose up -d
```

This provides the same services with simpler networking.

## Troubleshooting Setup

### Common Setup Issues

| Problem | Solution |
|---------|----------|
| Minikube won't start | Check Docker is running, try `minikube delete --profile=record-ranger-local` |
| Out of disk space | Increase disk size in `setup-minikube.sh`, recreate cluster |
| Pods stuck pending | Check resources: `kubectl top nodes` |
| Port conflicts | Kill existing forwards: `pkill -f "kubectl port-forward"` |

### Verification Commands
```bash
# Check cluster status
minikube status --profile=record-ranger-local

# Check all pods
kubectl get pods --all-namespaces

# Check resource usage
kubectl top nodes
kubectl top pods --all-namespaces

# Check services
kubectl get svc --all-namespaces
```

### Getting Help
- Run `./scripts/test-environment.sh` for automated diagnostics
- Check [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for detailed solutions
- Review logs: `kubectl logs -f deployment/<service> -n <namespace>`

---
**Next Steps**: After successful setup, see [README.md](README.md) for daily development workflow.
