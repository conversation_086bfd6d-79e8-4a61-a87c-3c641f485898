{"sample_documents": [{"id": "doc_001", "name": "medical_record_sample.pdf", "type": "medical_record", "description": "<PERSON>ple medical record for testing document classification", "metadata": {"patient_id": "P123456", "date_created": "2024-01-15", "document_type": "medical_record", "keywords": ["medical", "patient", "diagnosis", "treatment"], "confidence": 0.95}, "content_preview": "MEDICAL RECORD\nPatient: <PERSON>\nID: P123456\nDate: 2024-01-15\nDiagnosis: Routine checkup\nTreatment: Prescribed medication for blood pressure"}, {"id": "doc_002", "name": "rfa_form_sample.pdf", "type": "rfa_form", "description": "Sample RFA (Request for Authorization) form", "metadata": {"request_id": "RFA789012", "date_created": "2024-01-16", "document_type": "rfa_form", "keywords": ["rfa", "request for authorization", "expedited"], "confidence": 0.92}, "content_preview": "REQUEST FOR <PERSON><PERSON><PERSON><PERSON><PERSON>ATION\nRequest ID: RFA789012\nPatient: <PERSON>\nProcedure: MRI Scan\nUrgency: Expedited\nReason: Follow-up examination"}, {"id": "doc_003", "name": "work_status_report.pdf", "type": "work_status", "description": "Sample work status report for disability assessment", "metadata": {"employee_id": "E456789", "date_created": "2024-01-17", "document_type": "work_status", "keywords": ["work status", "disability", "return to work"], "confidence": 0.88}, "content_preview": "WORK STATUS REPORT\nEmployee: <PERSON>\nID: E456789\nStatus: Temporary Disability\nExpected Return: 2024-02-15\nRestrictions: Light duty only"}], "test_scenarios": [{"scenario": "document_classification", "description": "Test document classification pipeline", "steps": ["Upload sample documents to SFTP server", "Verify downloader picks up documents", "Check classifier processes documents correctly", "Validate metadata extraction results"]}, {"scenario": "qa_workflow", "description": "Test QA review workflow", "steps": ["Process documents through ML pipeline", "Send documents to QA backend", "Review documents in QA frontend", "Approve/reject documents", "Verify post-processing workflow"]}, {"scenario": "end_to_end", "description": "Complete end-to-end pipeline test", "steps": ["Upload documents via SFTP", "Monitor processing through all ML stages", "Review in QA interface", "Complete approval workflow", "Verify final upload and storage"]}]}