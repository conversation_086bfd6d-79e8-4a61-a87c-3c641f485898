#!/bin/bash

# Upload Sample Data Script
# This script uploads sample documents to the SFTP server for testing

set -e

echo "📤 Uploading sample data to Record Ranger ML Pipeline"
echo "===================================================="

# Configuration
SFTP_HOST="localhost"
SFTP_PORT="2222"
SFTP_USER="sftp"
SFTP_PASS="test"
SFTP_PATH="folder"

# Check if SFTP server is accessible
echo "🔍 Checking SFTP server connectivity..."
if ! nc -z $SFTP_HOST $SFTP_PORT; then
    echo "❌ SFTP server is not accessible at $SFTP_HOST:$SFTP_PORT"
    echo "   Please ensure the environment is running: ./start-environment.sh"
    exit 1
fi

echo "✅ SFTP server is accessible"

# Create sample documents
echo "📝 Creating sample documents..."

# Create temporary directory for sample files
TEMP_DIR=$(mktemp -d)
echo "   Using temporary directory: $TEMP_DIR"

# Medical Record Sample
cat > "$TEMP_DIR/medical_record_sample.txt" << EOF
MEDICAL RECORD
==============

Patient Information:
Name: John <PERSON>e
Patient ID: P123456
Date of Birth: 1980-05-15
Date of Visit: 2024-01-15

Chief Complaint:
Routine annual physical examination

Diagnosis:
1. Hypertension, controlled
2. Hyperlipidemia
3. Routine health maintenance

Treatment Plan:
- Continue current blood pressure medication
- Dietary counseling for cholesterol management
- Follow-up in 6 months
- Laboratory work recommended

Physician: Dr. Sarah Johnson, MD
Date: 2024-01-15
EOF

# RFA Form Sample
cat > "$TEMP_DIR/rfa_form_sample.txt" << EOF
REQUEST FOR AUTHORIZATION (RFA)
===============================

Request ID: RFA789012
Date Submitted: 2024-01-16
Priority: EXPEDITED

Patient Information:
Name: Jane Smith
Member ID: M987654
Date of Birth: 1975-08-22

Requested Service:
Procedure: MRI Brain with and without contrast
CPT Code: 70553
Diagnosis Code: R51 (Headache)

Clinical Information:
Patient presents with persistent headaches for 3 weeks.
Neurological examination shows no focal deficits.
MRI requested to rule out secondary causes.

Requesting Physician: Dr. Michael Brown, MD
Specialty: Neurology
Phone: (*************

Urgency: Expedited review requested due to symptom severity
EOF

# Work Status Report Sample
cat > "$TEMP_DIR/work_status_report.txt" << EOF
WORK STATUS REPORT
==================

Employee Information:
Name: Bob Johnson
Employee ID: E456789
Department: Manufacturing
Position: Machine Operator

Injury/Condition:
Date of Injury: 2024-01-10
Type: Lower back strain
Cause: Lifting heavy equipment

Current Status:
Work Status: Temporary Total Disability
Restrictions: No lifting over 10 lbs
Expected Return Date: 2024-02-15

Treatment Summary:
- Physical therapy 3x per week
- Anti-inflammatory medication
- Ergonomic assessment completed

Treating Physician: Dr. Lisa Wilson, MD
Occupational Medicine
Date: 2024-01-17

Return to Work Plan:
Phase 1: Light duty (weeks 1-2)
Phase 2: Modified duty (weeks 3-4)
Phase 3: Full duty (week 5+)
EOF

echo "✅ Sample documents created"

# Upload files via SFTP
echo "📤 Uploading files to SFTP server..."

# Use sshpass and sftp to upload files
for file in "$TEMP_DIR"/*.txt; do
    filename=$(basename "$file")
    echo "   Uploading $filename..."
    
    # Create SFTP batch file
    cat > "$TEMP_DIR/sftp_batch" << EOF
cd $SFTP_PATH
put $file
quit
EOF
    
    # Upload using sftp (with password authentication)
    if command -v sshpass >/dev/null 2>&1; then
        sshpass -p "$SFTP_PASS" sftp -o StrictHostKeyChecking=no -P $SFTP_PORT -b "$TEMP_DIR/sftp_batch" $SFTP_USER@$SFTP_HOST
    else
        echo "   ⚠️  sshpass not available, manual upload required"
        echo "   Command: sftp -P $SFTP_PORT $SFTP_USER@$SFTP_HOST"
        echo "   Password: $SFTP_PASS"
        echo "   Upload: put $file"
    fi
done

# Clean up
rm -rf "$TEMP_DIR"

echo "✅ Sample data upload completed!"
echo ""
echo "📋 Uploaded Files:"
echo "   - medical_record_sample.txt"
echo "   - rfa_form_sample.txt" 
echo "   - work_status_report.txt"
echo ""
echo "🎯 Next Steps:"
echo "   1. Monitor downloader logs: kubectl logs -f deployment/downloader -n ml-pipeline"
echo "   2. Check RabbitMQ queues: http://localhost:15672"
echo "   3. Watch processing in QA Frontend: http://localhost:3000"
echo ""
