#!/bin/bash

# Database Initialization Script for Record Ranger Local Development
# This script initializes all three databases (rr, backend, queue) with their schemas

set -e

echo "🗄️  Initializing Record Ranger databases..."

# Wait for PostgreSQL to be ready
until pg_isready -h localhost -p 5432 -U backend; do
  echo "Waiting for PostgreSQL to be ready..."
  sleep 2
done

echo "✅ PostgreSQL is ready"

# Create databases
echo "📁 Creating databases..."
psql -h localhost -p 5432 -U backend -d postgres -c "CREATE DATABASE IF NOT EXISTS rr OWNER backend;"
psql -h localhost -p 5432 -U backend -d postgres -c "CREATE DATABASE IF NOT EXISTS backend OWNER backend;"
psql -h localhost -p 5432 -U backend -d postgres -c "CREATE DATABASE IF NOT EXISTS queue OWNER backend;"

# Grant privileges
psql -h localhost -p 5432 -U backend -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE rr TO backend;"
psql -h localhost -p 5432 -U backend -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE backend TO backend;"
psql -h localhost -p 5432 -U backend -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE queue TO backend;"

echo "✅ Databases created successfully"

# Initialize rr database schema
echo "🔧 Initializing rr database schema..."
psql -h localhost -p 5432 -U backend -d rr -f /init-scripts/rr.sql

# Initialize backend database schema
echo "🔧 Initializing backend database schema..."
psql -h localhost -p 5432 -U backend -d backend -f /init-scripts/backend.sql

# Initialize queue database schema
echo "🔧 Initializing queue database schema..."
psql -h localhost -p 5432 -U backend -d queue -f /init-scripts/queue.sql

# Insert sample data
echo "📊 Inserting sample data..."
psql -h localhost -p 5432 -U backend -d rr -c "
INSERT INTO public.tenants (tenant_id, tenant_name, description, contact_email, active) 
VALUES ('local-dev', 'Local Development', 'Local development tenant for testing', 'dev@localhost', true)
ON CONFLICT (tenant_id) DO NOTHING;
"

psql -h localhost -p 5432 -U backend -d backend -c "
INSERT INTO public.clients (client_id, name, priority) 
VALUES (gen_random_uuid(), 'Local Development Client', 1)
ON CONFLICT DO NOTHING;
"

psql -h localhost -p 5432 -U backend -d queue -c "
INSERT INTO public.tenant_configurations (tenant_id, configuration_key, configuration_value, configuration_type) 
VALUES ('local-dev', 'environment', '{\"type\": \"local-development\", \"debug\": true}', 'system')
ON CONFLICT DO NOTHING;
"

echo "✅ Database initialization completed successfully!"
echo ""
echo "📋 Database Summary:"
echo "   - rr database: Tenant and user activity data"
echo "   - backend database: Client and notification data"
echo "   - queue database: Raw packages and tenant configurations"
echo "   - Sample tenant: local-dev"
echo ""
