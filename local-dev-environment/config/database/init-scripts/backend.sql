-- DROP SCHEMA public;

CREATE SCHEMA public AUTHORIZATION pg_database_owner;

COMMENT ON SCHEMA public IS 'standard public schema';

-- DROP TYPE public."notificationentitynameenum";

CREATE TYPE public."notificationentitynameenum" AS ENUM (
	'CHUNK',
	'PACKET');

-- DROP TYPE public."notificationtypeenum";

CREATE TYPE public."notificationtypeenum" AS ENUM (
	'CHUNK_DELEGATED_STUCK',
	'CHUNK_REVIEW_STUCK',
	'PACKET_LARGE_STUCK',
	'PACKET_STUCK_BEFORE_QA',
	'PACKET_STUCK_AFTER_QA');
-- public.alembic_version definition

-- Drop table

-- DROP TABLE public.alembic_version;

CREATE TABLE public.alembic_version (
	version_num varchar(32) NOT NULL,
	CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);

-- Permissions

ALTER TABLE public.alembic_version OWNER TO backend;
GRANT ALL ON TABLE public.alembic_version TO backend;


-- public.clients definition

-- Drop table

-- DROP TABLE public.clients;

CREATE TABLE public.clients (
	client_id uuid NOT NULL,
	"name" varchar(256) NOT NULL,
	priority int2 NOT NULL,
	CONSTRAINT clients_pkey PRIMARY KEY (client_id)
);

-- Permissions

ALTER TABLE public.clients OWNER TO backend;
GRANT ALL ON TABLE public.clients TO backend;


-- public.mailings definition

-- Drop table

-- DROP TABLE public.mailings;

CREATE TABLE public.mailings (
	mailing_id uuid NOT NULL,
	"name" varchar(128) NOT NULL,
	mailing_emails _varchar NOT NULL,
	CONSTRAINT mailings_name_key UNIQUE (name),
	CONSTRAINT mailings_pkey PRIMARY KEY (mailing_id)
);

-- Permissions

ALTER TABLE public.mailings OWNER TO backend;
GRANT ALL ON TABLE public.mailings TO backend;


-- public.notifications definition

-- Drop table

-- DROP TABLE public.notifications;

CREATE TABLE public.notifications (
	notification_id uuid NOT NULL,
	entity public."notificationentitynameenum" NOT NULL,
	entity_id uuid NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	notification_type public."notificationtypeenum" NOT NULL,
	email bool NOT NULL,
	slack bool NOT NULL,
	CONSTRAINT notifications_pkey PRIMARY KEY (notification_id)
);

-- Permissions

ALTER TABLE public.notifications OWNER TO backend;
GRANT ALL ON TABLE public.notifications TO backend;


-- public.tenants definition

-- Drop table

-- DROP TABLE public.tenants;

CREATE TABLE public.tenants (
	tenant_id varchar(255) NOT NULL,
	tenant_name varchar(255) NOT NULL,
	tenant_type varchar(100) DEFAULT 'standard'::character varying NOT NULL,
	is_active bool DEFAULT true NOT NULL,
	created_date timestamptz DEFAULT now() NOT NULL,
	modified_date timestamptz DEFAULT now() NOT NULL,
	description text NULL,
	contact_email varchar(255) NULL,
	max_storage_gb int4 NULL,
	max_documents_per_month int4 NULL,
	retention_days int4 DEFAULT 365 NOT NULL,
	CONSTRAINT tenants_pkey PRIMARY KEY (tenant_id)
);

-- Permissions

ALTER TABLE public.tenants OWNER TO backend;
GRANT ALL ON TABLE public.tenants TO backend;


-- public.users_activity_logs definition

-- Drop table

-- DROP TABLE public.users_activity_logs;

CREATE TABLE public.users_activity_logs (
	activity_id uuid NOT NULL,
	user_id uuid NOT NULL,
	"action" varchar(256) NOT NULL,
	description varchar(256) NULL,
	action_timestamp timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT users_activity_logs_pkey PRIMARY KEY (activity_id)
);

-- Permissions

ALTER TABLE public.users_activity_logs OWNER TO backend;
GRANT ALL ON TABLE public.users_activity_logs TO backend;


-- public.subtenants definition

-- Drop table

-- DROP TABLE public.subtenants;

CREATE TABLE public.subtenants (
	tenant_id varchar(255) NOT NULL,
	subtenant_id varchar(255) NOT NULL,
	subtenant_name varchar(255) NOT NULL,
	is_active bool DEFAULT true NOT NULL,
	created_date timestamptz DEFAULT now() NOT NULL,
	modified_date timestamptz DEFAULT now() NOT NULL,
	description text NULL,
	max_storage_gb int4 NULL,
	max_documents_per_month int4 NULL,
	CONSTRAINT subtenants_pkey PRIMARY KEY (tenant_id, subtenant_id),
	CONSTRAINT subtenants_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id) ON DELETE CASCADE
);
CREATE INDEX idx_subtenants_tenant_id ON public.subtenants USING btree (tenant_id);

-- Permissions

ALTER TABLE public.subtenants OWNER TO backend;
GRANT ALL ON TABLE public.subtenants TO backend;


-- public.users definition

-- Drop table

-- DROP TABLE public.users;

CREATE TABLE public.users (
	user_id uuid NOT NULL,
	username varchar(50) NOT NULL,
	"password" varchar(60) NOT NULL,
	user_type varchar(50) NOT NULL,
	secret_key varchar NULL,
	password_set timestamptz DEFAULT now() NOT NULL,
	otp_requested timestamptz NULL,
	login_attempt int2 NOT NULL,
	active bool NOT NULL,
	pass_expire_mail int2 NOT NULL,
	authenticator_connect_requested bool NOT NULL,
	email varchar(50) NOT NULL,
	given_name varchar(60) NOT NULL,
	family_name varchar(60) NOT NULL,
	last_online timestamptz NULL,
	websocket_id uuid NULL,
	logged_in bool DEFAULT false NOT NULL,
	tenant_id varchar(255) NULL,
	subtenant_id varchar(255) NULL,
	CONSTRAINT users_email_key UNIQUE (email),
	CONSTRAINT users_pkey PRIMARY KEY (user_id),
	CONSTRAINT users_username_key UNIQUE (username),
	CONSTRAINT fk_users_subtenant FOREIGN KEY (tenant_id,subtenant_id) REFERENCES public.subtenants(tenant_id,subtenant_id) ON DELETE SET NULL,
	CONSTRAINT fk_users_tenant FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id) ON DELETE SET NULL
);
CREATE INDEX idx_users_subtenant_id ON public.users USING btree (subtenant_id);
CREATE INDEX idx_users_tenant_id ON public.users USING btree (tenant_id);

-- Permissions

ALTER TABLE public.users OWNER TO backend;
GRANT ALL ON TABLE public.users TO backend;


-- public.packets definition

-- Drop table

-- DROP TABLE public.packets;

CREATE TABLE public.packets (
	packet_id uuid NOT NULL,
	filename varchar(256) NULL,
	status varchar(12) NOT NULL,
	received_time timestamptz DEFAULT now() NULL,
	sent_time timestamptz NULL,
	"token" varchar NULL,
	pipeline_packet_id uuid NOT NULL,
	client_id uuid NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	pipeline_out_time timestamptz NULL,
	pages_count int4 NULL,
	fields_to_validate _varchar NULL,
	is_large bool DEFAULT false NOT NULL,
	chunk_amount int4 NULL,
	tenant_id varchar(255) NULL,
	subtenant_id varchar(255) NULL,
	CONSTRAINT packets_pkey PRIMARY KEY (packet_id),
	CONSTRAINT fk_packets_subtenant FOREIGN KEY (tenant_id,subtenant_id) REFERENCES public.subtenants(tenant_id,subtenant_id) ON DELETE SET NULL,
	CONSTRAINT fk_packets_tenant FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id) ON DELETE SET NULL,
	CONSTRAINT packets_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(client_id) ON DELETE SET NULL
);
CREATE INDEX idx_packets_subtenant_id ON public.packets USING btree (subtenant_id);
CREATE INDEX idx_packets_tenant_id ON public.packets USING btree (tenant_id);

-- Permissions

ALTER TABLE public.packets OWNER TO backend;
GRANT ALL ON TABLE public.packets TO backend;


-- public.user_clients definition

-- Drop table

-- DROP TABLE public.user_clients;

CREATE TABLE public.user_clients (
	user_id uuid NOT NULL,
	client_id uuid NOT NULL,
	CONSTRAINT user_clients_pkey PRIMARY KEY (user_id, client_id),
	CONSTRAINT user_clients_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(client_id) ON DELETE CASCADE,
	CONSTRAINT user_clients_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- Permissions

ALTER TABLE public.user_clients OWNER TO backend;
GRANT ALL ON TABLE public.user_clients TO backend;


-- public.packet_chunks definition

-- Drop table

-- DROP TABLE public.packet_chunks;

CREATE TABLE public.packet_chunks (
	chunk_id uuid NOT NULL,
	packet_id uuid NOT NULL,
	pipeline_chunk_id uuid NOT NULL,
	chunk_number int2 NOT NULL,
	status varchar(17) DEFAULT 'PENDING'::character varying NOT NULL,
	pages_count int4 NULL,
	priority int4 NULL,
	delegated_by_username varchar(50) NULL,
	delegated_by_user_id uuid NULL,
	delegated_time timestamptz NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	merged_incomplete bool DEFAULT false NOT NULL,
	pages_range _int4 NULL,
	CONSTRAINT packet_chunks_pkey PRIMARY KEY (chunk_id),
	CONSTRAINT packet_chunks_packet_id_fkey FOREIGN KEY (packet_id) REFERENCES public.packets(packet_id) ON DELETE CASCADE
);

-- Permissions

ALTER TABLE public.packet_chunks OWNER TO backend;
GRANT ALL ON TABLE public.packet_chunks TO backend;


-- public.screenshots definition

-- Drop table

-- DROP TABLE public.screenshots;

CREATE TABLE public.screenshots (
	screenshot_id uuid NOT NULL,
	ocr_text varchar NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	chunk_id uuid NOT NULL,
	CONSTRAINT screenshots_pkey PRIMARY KEY (screenshot_id),
	CONSTRAINT screenshots_chunk_id_fkey FOREIGN KEY (chunk_id) REFERENCES public.packet_chunks(chunk_id) ON DELETE CASCADE
);

-- Permissions

ALTER TABLE public.screenshots OWNER TO backend;
GRANT ALL ON TABLE public.screenshots TO backend;


-- public.user_packet_chunks definition

-- Drop table

-- DROP TABLE public.user_packet_chunks;

CREATE TABLE public.user_packet_chunks (
	user_id uuid NOT NULL,
	chunk_id uuid NOT NULL,
	active bool NULL,
	first_opened timestamptz DEFAULT now() NULL,
	submitted timestamptz NULL,
	delegated timestamptz NULL,
	last_opened timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT user_packet_chunks_chunk_id_active_key UNIQUE (chunk_id, active),
	CONSTRAINT user_packet_chunks_pkey PRIMARY KEY (user_id, chunk_id),
	CONSTRAINT user_packet_chunks_user_id_active_key UNIQUE (user_id, active),
	CONSTRAINT user_packet_chunks_chunk_id_fkey FOREIGN KEY (chunk_id) REFERENCES public.packet_chunks(chunk_id) ON DELETE CASCADE,
	CONSTRAINT user_packet_chunks_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- Permissions

ALTER TABLE public.user_packet_chunks OWNER TO backend;
GRANT ALL ON TABLE public.user_packet_chunks TO backend;


-- public.packet_chunk_comments definition

-- Drop table

-- DROP TABLE public.packet_chunk_comments;

CREATE TABLE public.packet_chunk_comments (
	packet_chunk_comment_id uuid NOT NULL,
	chunk_id uuid NOT NULL,
	user_id uuid NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	"text" varchar(1000) NULL,
	CONSTRAINT packet_chunk_comments_pkey PRIMARY KEY (packet_chunk_comment_id),
	CONSTRAINT packet_chunk_comments_chunk_id_fkey FOREIGN KEY (chunk_id) REFERENCES public.packet_chunks(chunk_id) ON DELETE CASCADE,
	CONSTRAINT packet_chunk_comments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE
);

-- Permissions

ALTER TABLE public.packet_chunk_comments OWNER TO backend;
GRANT ALL ON TABLE public.packet_chunk_comments TO backend;


-- public.packet_chunk_metadata definition

-- Drop table

-- DROP TABLE public.packet_chunk_metadata;

CREATE TABLE public.packet_chunk_metadata (
	packet_chunk_metadata_id uuid NOT NULL,
	chunk_id uuid NOT NULL,
	metadata_type varchar(128) NOT NULL,
	"docName" varchar NULL,
	"docLink" varchar NULL,
	"docType" varchar NOT NULL,
	"docConfidence" float4 NULL,
	"pageRefStart" int4 NULL,
	"pageRefEnd" int4 NULL,
	"pagesRef" _int4 NOT NULL,
	"isDuplicated" bool DEFAULT false NOT NULL,
	"pagesRefOrig" _int4 NULL,
	"claimNumber" varchar NULL,
	"namingData" jsonb NOT NULL,
	"metaData" jsonb NOT NULL,
	CONSTRAINT packet_chunk_metadata_pkey PRIMARY KEY (packet_chunk_metadata_id),
	CONSTRAINT packet_chunk_metadata_chunk_id_fkey FOREIGN KEY (chunk_id) REFERENCES public.packet_chunks(chunk_id) ON DELETE CASCADE
);

-- Permissions

ALTER TABLE public.packet_chunk_metadata OWNER TO backend;
GRANT ALL ON TABLE public.packet_chunk_metadata TO backend;


-- public.packet_chunk_predictions definition

-- Drop table

-- DROP TABLE public.packet_chunk_predictions;

CREATE TABLE public.packet_chunk_predictions (
	packet_chunk_prediction_id uuid NOT NULL,
	chunk_id uuid NOT NULL,
	prediction_type varchar(128) NOT NULL,
	page_ranges _int4 NOT NULL,
	packet_type varchar(128) NOT NULL,
	classification_confidence float4 NOT NULL,
	overall_confidence float4 NOT NULL,
	incomplete bool NOT NULL,
	CONSTRAINT packet_chunk_predictions_pkey PRIMARY KEY (packet_chunk_prediction_id),
	CONSTRAINT packet_chunk_predictions_chunk_id_fkey FOREIGN KEY (chunk_id) REFERENCES public.packet_chunks(chunk_id) ON DELETE CASCADE
);

-- Permissions

ALTER TABLE public.packet_chunk_predictions OWNER TO backend;
GRANT ALL ON TABLE public.packet_chunk_predictions TO backend;




-- Permissions;