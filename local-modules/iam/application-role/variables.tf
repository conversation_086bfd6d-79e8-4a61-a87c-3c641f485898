variable "oidc_provider_arn" {}
variable "oidc_provider_url" {}
variable "cluster_name" {}
variable "namespace" {}
variable "serviceaccount" {}
variable "s3_bucket_names" {
  description = "Map of S3 bucket names"
  type        = map(string)
  default     = {}
}
variable "sqs_arns" {
  description = "Map of SQS arns"
  type        = map(string)
  default     = {}
}

variable "include_default_policies" {
  description = "Whether to include default policies or not"
  type        = bool
  default     = true
}
