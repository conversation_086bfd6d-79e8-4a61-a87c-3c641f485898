data "aws_iam_policy_document" "application_assume" {
  statement {
    actions = ["sts:AssumeRoleWithWebIdentity"]

    principals {
      type        = "Federated"
      identifiers = [var.oidc_provider_arn]
    }

    condition {
      test     = "StringEquals"
      variable = "${replace(var.oidc_provider_url, "https://", "")}:aud"

      values = [
        "sts.amazonaws.com",
      ]
    }

    condition {
      test     = "StringEquals"
      variable = "${replace(var.oidc_provider_url, "https://", "")}:sub"

      values = [
        "system:serviceaccount:${var.namespace}:${var.serviceaccount}",
      ]
    }

    effect = "Allow"
  }
}

resource "aws_iam_role" "application_role" {
  name               = "${var.serviceaccount}-role"
  assume_role_policy = data.aws_iam_policy_document.application_assume.json
}

resource "aws_iam_role_policy_attachment" "application_attachment" {
  for_each = var.include_default_policies ? toset(local.application_policies_arn) : []
  role       = aws_iam_role.application_role.name
  policy_arn = each.key
}

resource "aws_iam_role_policy" "s3_policy" {
  for_each = var.s3_bucket_names

  name = "${var.serviceaccount}-s3-bucket-policy-${each.key}"
  role = aws_iam_role.application_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    "Statement" : [
      {
        "Sid" : "S3AccessForSA",
        "Effect" : "Allow",
        "Action" : [
          "s3:*",
        ],
        "Resource" : [
          "arn:aws:s3:::${each.value}/*",
          "arn:aws:s3:::${each.value}"
        ]
      },
    ]
  })
}

resource "aws_iam_role_policy" "sqs_policy" {
  for_each = var.sqs_arns

  name = "${var.serviceaccount}-sqs-policy${each.key}"
  role = aws_iam_role.application_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "SQSAccessForSA"
        Effect = "Allow"
        Action = [
          "sqs:*",
        ]
        Resource = [
          each.value,
        ]
      },
    ]
  })
}