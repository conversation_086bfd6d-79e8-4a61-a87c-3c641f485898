resource "aws_security_group" "main" {
  for_each = local.security_groups

  name        = "${var.environment}-${each.key}"
  description = "${var.environment}-${each.key}"
  vpc_id      = var.vpc_id

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-${each.key}"
    }
  )
}

resource "aws_security_group_rule" "egress_default" {
  for_each = local.security_groups

  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "all"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.main[each.key].id
}

resource "aws_security_group_rule" "ingress_cidr" {
  for_each = local.security_group_rules_cidr_map

  security_group_id = aws_security_group.main[each.value["sg_name"]].id
  description       = each.value["description"]
  type              = "ingress"

  from_port = each.value["from_port"]
  to_port   = each.value["to_port"]
  protocol  = each.value["protocol"]

  cidr_blocks = [each.value["source"]]
}

resource "aws_security_group_rule" "ingress_sg" {
  for_each = local.security_group_rules_sg_map

  security_group_id = aws_security_group.main[each.value["sg_name"]].id
  description       = each.value["description"]
  type              = "ingress"

  from_port = each.value["from_port"]
  to_port   = each.value["to_port"]
  protocol  = each.value["protocol"]

  source_security_group_id = aws_security_group.main[each.value["source"]].id
}

resource "aws_security_group_rule" "ingress_self" {
  for_each = local.security_group_rules_self_map

  security_group_id = aws_security_group.main[each.value["sg_name"]].id
  description       = each.value["description"]
  type              = "ingress"

  from_port = each.value["from_port"]
  to_port   = each.value["to_port"]
  protocol  = each.value["protocol"]

  self = true
}

resource "aws_security_group_rule" "ingress_vpc" {
  for_each = local.security_group_rules_vpc_map

  security_group_id = aws_security_group.main[each.value["sg_name"]].id
  description       = each.value["description"]
  type              = "ingress"

  from_port = each.value["from_port"]
  to_port   = each.value["to_port"]
  protocol  = each.value["protocol"]

  cidr_blocks = [var.vpc_cidr]
}