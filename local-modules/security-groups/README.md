Example variable:

```
security_groups = {
   "management" = {
     "Ovpn"          = { from_port = 0, to_port = 0, protocol = "all", source = "ovpn" }
   }
   "eks-worker" = {
     "self"               = { from_port = 0, to_port = 0, protocol = "all", source = "self" }
     "alb-internal"       = { from_port = 0, to_port = 0, protocol = "all", source = "alb-internal" }
     "alb-external-http"  = { from_port = 80, to_port = 80, protocol = "tcp", source = "alb-external" }
     "alb-external-https" = { from_port = 443, to_port = 443, protocol = "tcp", source = "alb-external" }
   }
   "rds" = {
     "EKS workers" = { from_port = 3306, to_port = 3306, protocol = "tcp", source = "eks-worker" }
   }
   "ovpn" = {
       "OpenVPN Server" = { from_port = 1194, to_port = 1194, protocol = "udp", source = "0.0.0.0/0" }
    }
   "alb-internal" = {}
   "alb-external" = {
    "http" = { from_port = 80, to_port = 80, protocol = "tcp", source = "0.0.0.0/0" }
    "https" = { from_port = 443, to_port = 443, protocol = "tcp", source = "0.0.0.0/0" }
    }
   "efs-k8s" = {
     "efs" = { from_port = 2049, to_port = 2049, protocol = "all", source = "172.27.101.0/24" }
    }
}
```


Example resource association:

```
vpc_security_group_ids = [module.sg.id["management"], module.sg.id["rds"]]
```
