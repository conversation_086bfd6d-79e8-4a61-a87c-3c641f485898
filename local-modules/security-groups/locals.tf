locals {
  security_groups = merge(var.security_groups)
  security_group_rules_cidr = flatten([
    for sg_key, sg_rules in local.security_groups : [
      for rule_key, rule in sg_rules : {
        sg_name     = sg_key
        description = rule_key
        from_port   = rule["from_port"]
        to_port     = rule["to_port"]
        protocol    = rule["protocol"]
        source      = rule["source"]
      } if length(regexall("(\\d+\\.)+\\d+/+\\d+", rule["source"])) > 0
    ]
  ])
  security_group_rules_sg = flatten([
    for sg_key, sg_rules in local.security_groups : [
      for rule_key, rule in sg_rules : {
        sg_name     = sg_key
        description = rule_key
        from_port   = rule["from_port"]
        to_port     = rule["to_port"]
        protocol    = rule["protocol"]
        source      = rule["source"]

      } if rule["source"] != "self" && rule["source"] != "vpc" && length(regexall("^[a-zA-Z0-9_-]+$", rule["source"])) > 0
    ]
  ])
  security_group_rules_self = flatten([
    for sg_key, sg_rules in local.security_groups : [
      for rule_key, rule in sg_rules : {
        sg_name     = sg_key
        description = rule_key
        from_port   = rule["from_port"]
        to_port     = rule["to_port"]
        protocol    = rule["protocol"]
        source      = rule["source"]

      } if rule["source"] == "self"
    ]
  ])
  security_group_rules_vpc = flatten([
    for sg_key, sg_rules in local.security_groups : [
      for rule_key, rule in sg_rules : {
        sg_name     = sg_key
        description = rule_key
        from_port   = rule["from_port"]
        to_port     = rule["to_port"]
        protocol    = rule["protocol"]
        source      = rule["source"]

      } if rule["source"] == "vpc"
    ]
  ])

  security_group_rules_cidr_map = { for sg_rule in local.security_group_rules_cidr : "${sg_rule.sg_name}.${sg_rule.description}" => sg_rule }
  security_group_rules_sg_map   = { for sg_rule in local.security_group_rules_sg : "${sg_rule.sg_name}.${sg_rule.description}" => sg_rule }
  security_group_rules_self_map = { for sg_rule in local.security_group_rules_self : "${sg_rule.sg_name}.${sg_rule.description}" => sg_rule }
  security_group_rules_vpc_map  = { for sg_rule in local.security_group_rules_vpc : "${sg_rule.sg_name}.${sg_rule.description}" => sg_rule }
}