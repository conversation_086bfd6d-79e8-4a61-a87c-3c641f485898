

resource "aws_s3_bucket" "bucket" {
  bucket = var.bucket

  lifecycle {
    prevent_destroy = false
  }

  tags = {
    Name = var.bucket
  }


#  dynamic "logging" {
#    for_each = var.logging_target_bucket == null ? [] : [""]
#    content {
#      target_bucket = var.logging_target_bucket
#      target_prefix = var.logging_target_prefix
#    }
#  }

}
resource "aws_s3_bucket_ownership_controls" "this" {
  bucket = aws_s3_bucket.bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}


resource "aws_s3_bucket_acl" "acl" {
  depends_on = [aws_s3_bucket_ownership_controls.this]

  bucket = aws_s3_bucket.bucket.id
  acl    = var.acl
}

resource "aws_s3_bucket_cors_configuration" "cors_configuration" {
  count = var.enable_cors ? 1 : 0
  bucket = aws_s3_bucket.bucket.id

    cors_rule {
      allowed_headers = ["*"]
      allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
      allowed_origins = ["*"]
      expose_headers  = ["ETag"]
      max_age_seconds = 3000
    }
}

resource "aws_s3_bucket_policy" "policy" {
  count = var.policy != "" ? 1 : 0

  bucket = aws_s3_bucket.bucket.id
  policy = var.policy
}

resource "aws_s3_bucket_versioning" "versioning" {
  bucket = aws_s3_bucket.bucket.id
  versioning_configuration {
    status = var.versioning_enabled
  }
}
resource "aws_s3_bucket_public_access_block" "upload_csv" {
  bucket = aws_s3_bucket.bucket.id

  block_public_acls   = var.block_public_acls
  block_public_policy = var.block_public_policy
  ignore_public_acls = var.ignore_public_acls
  restrict_public_buckets = var.restrict_public_buckets
}