provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [var.aws_account_id]
  alias               = "us-east-2"

  assume_role {
    role_arn = "arn:aws:iam::${var.aws_account_id}:role/ci-provisioner"
  }
}


terraform {
  required_providers {
    aws = {
      source                = "hashicorp/aws"
      version               = ">= 5.55"
      configuration_aliases = [aws.us-east-2]
    }
  }
}
