variable "tenant" {}
variable "tenant_mail_domain" {}
variable "provisioner_additional_principals" {
  type        = list(string)
  description = "provisioner additional principals"
}
variable "aws_region" {}
variable "aws_account_id" { type = string }
variable "override_account_alias" { default = "" }
variable "monthly_budget" {
  type        = number
  description = "amount of dollars $ to invoke budget alarm"
}
variable "alert_mails" {
  type        = list(string)
  description = "List of emails to send the monthy budget alarm"
}
variable "domain_name" {}