 environment                   = "prod"
 tenant                        = "atom-advantage"
 domain                        = "atomadvantage.ai"
 account_id                    = "************"
 sns_endpoint                  = ["<EMAIL>"]

 sso_admin_role     = "c82a6a6fbc3b995c"

 enable_cf                     = true
 enable_nat_gateway            = true

 tags = {
    Terraform   = "true"
    Environment = "prod"
    tenant      = "atom-advantage"
 }

##  eu-central-1
 cidr_block                    = "10.74.0.0/16"
 region                        = "us-east-2"



 security_groups = {
   "rds" = {
      "backend" = { from_port = 5432, to_port = 5432, protocol = "TCP", source = "10.74.0.0/16" }
   }
   "management" = {
      "ssh" = { from_port = 22, to_port = 22, protocol = "TCP", source = "0.0.0.0/0" },
      "rdp" = { from_port = 3389, to_port = 3389, protocol = "TCP", source = "0.0.0.0/0" },
      "internal-db-proxy-access" = { from_port = 5432, to_port = 5434, protocol = "TCP", source = "10.74.0.0/16" }
   }
}

ssm_backend = ["API_KEY", "JWT_SECRET_KEY", "JWT_REFRESH_SECRET_KEY", "SOURCE_SECRET_KEY", "MAILGUN_API_DOMAIN", "MAILGUN_API_KEY", "MAIL_TOKEN"]

# RDS

rds_name = "backend"
rds_engine = "postgres"
rds_family = "postgres16"
rds_username = "backend"
rds_engine_version = 16
apply_immediately = true
rds_allocated_storage = 20
rds_instance_class = "db.t3.medium"
rds_port = 5432
rds_multi_az = false

is_powerbi_proxy_consumer  = true
powerbi_proxy_service_name = "com.amazonaws.vpce.us-east-2.vpce-svc-0e773a426f2fad1f2"
