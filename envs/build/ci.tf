locals {
  deployer_principals = concat(
    tolist(["arn:${data.aws_partition.current.partition}:iam::266088940863:user/ci-deployer"]),
  )
}

module "ci_deployer" {
  providers = {
    aws = aws.us-east-2
  }
  source = "../../local-modules/iam/role"

  name               = "ci-deployer"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          ${join(",", formatlist("\"%s\"", local.deployer_principals))}
        ]
      },
      "Action": "sts:AssumeRole"
    },
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          ${join(",", formatlist("\"%s\"", local.deployer_principals))}
        ]
      },
      "Action": "sts:TagSession"
    }
  ]
}
EOF
  policy             = var.deployer_policy
}
