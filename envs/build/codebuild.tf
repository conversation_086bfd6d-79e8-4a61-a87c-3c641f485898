module "github_runner" {
  source = "../../local-modules/github-aws-runner"
  name            = "github-runner-codebuild"
  source_location = "https://github.com/atom-advantage/codebuild-setup-test.git"
  description = "Created for https://github.com/atom-advantage/codebuild-setup-test.git"
  github_personal_access_token_ssm_parameter = "/atom-advantage/github-runner/GITHUB_PAT"
  providers = {
    aws = aws.us-east-2
  }
}

module "github_runner_ml_pipeline" {
  source = "../../local-modules/github-aws-runner"
  name            = "github-runner-ml-pipeline"
  source_location = "https://github.com/atom-advantage/aa_record_ranger_ml_pipeline.git"
  description = "Created for https://github.com/atom-advantage/aa_record_ranger_ml_pipeline.git"
  github_personal_access_token_ssm_parameter = "/atom-advantage/github-runner/GITHUB_PAT"
  providers = {
    aws = aws.us-east-2
  }
  build_timeout = 30
}