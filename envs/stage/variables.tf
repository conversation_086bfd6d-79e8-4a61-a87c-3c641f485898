variable "tenant" {}
variable "environment" {}
variable "account_id" {}
variable "cidr_block" {}
variable "region" {}
variable "enable_cf" { default = false }
variable "sns_endpoint" {
  type    = list(any)
  default = []
}
variable "enable_nat_gateway" {

}

variable "tags" {

}
variable "security_groups" {

}
variable "ssm_backend" {
  description = "A list of paramaters that will be crated in ssm"
  type        = list(string)
  default     = []
}
variable "rds_name" {}
variable "rds_username" {}
variable "rds_engine" {}
variable "rds_engine_version" {}
variable "apply_immediately" {}
variable "rds_allocated_storage" {}
variable "rds_instance_class" {}
variable "rds_port" {}
variable "rds_multi_az" {}
variable "rds_family" {}
variable "sso_admin_role" {}
variable "domain" {}

variable "powerbi_proxy_rds_targets" {
  description = "A map of RDS instances to expose through the PowerBI proxy."
  type = map(object({
    listener_port = number
    rds_hostname  = string
    rds_port      = number
  }))
  default = {}
}

variable "powerbi_proxy_consumer_account_arn" {
  description = "The ARN of the AWS Account that is allowed to connect to the endpoint service."
  type        = string
  default     = ""
}